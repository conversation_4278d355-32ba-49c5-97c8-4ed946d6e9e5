import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Dimensions, Alert, Linking, Platform } from 'react-native';
import * as Location from 'expo-location';
import { MaterialIcons } from '@expo/vector-icons';

// Dynamically import MapView to handle potential errors
let <PERSON>Vie<PERSON>, Marker;
try {
  const Maps = require('react-native-maps');
  MapView = Maps.default;
  Marker = Maps.Marker;
} catch (error) {
  console.error('Error loading react-native-maps:', error);
  // Create placeholder components if maps can't be loaded
  MapView = ({ children, ...props }) => (
    <View style={[props.style, { justifyContent: 'center', alignItems: 'center' }]}>
      <Text>Map not available</Text>
    </View>
  );
  Marker = ({ children }) => <View>{children}</View>;
}

const DeliveryMapView = ({
    customerLocation,
    customerAddress,
    customerName,
    customerPhone,
    onClose,
    showDirections = true
}) => {
    const [currentLocation, setCurrentLocation] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const mapRef = useRef(null);

    // Get current location on component mount
    useEffect(() => {
        getLocation();
    }, []);

    // Get current location
    const getLocation = async () => {
        try {
            setLoading(true);

            // Request location permission
            const { status } = await Location.requestForegroundPermissionsAsync();

            if (status !== 'granted') {
                setError('Location permission denied');
                setLoading(false);
                return;
            }

            // Get current position
            const location = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.High
            });

            setCurrentLocation({
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01
            });

            // Fit map to show both current location and customer location
            if (customerLocation && mapRef.current) {
                setTimeout(() => {
                    mapRef.current.fitToCoordinates(
                        [
                            {
                                latitude: location.coords.latitude,
                                longitude: location.coords.longitude
                            },
                            {
                                latitude: customerLocation.latitude,
                                longitude: customerLocation.longitude
                            }
                        ],
                        {
                            edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
                            animated: true
                        }
                    );
                }, 1000);
            }
        } catch (err) {
            console.error('Error getting location:', err);
            setError('Could not get your location');
        } finally {
            setLoading(false);
        }
    };

    // Handle get directions button press
    const handleGetDirections = () => {
        // Show confirmation alert before opening maps
        Alert.alert(
            "Open Maps",
            "Would you like to open this location in Google Maps?",
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Open Maps",
                    onPress: () => {
                        if (customerLocation) {
                            openMapsWithDirections(customerAddress, customerLocation);
                        } else {
                            openMapsWithDirections(customerAddress);
                        }
                    }
                }
            ],
            { cancelable: true }
        );
    };

    // Handle call customer button press
    const handleCallCustomer = () => {
        if (!customerPhone) {
            Alert.alert(
                "No Phone Number",
                "No phone number available for this customer.",
                [{ text: "OK", style: 'default' }],
                { cancelable: true }
            );
            return;
        }

        // Format phone number to Indian standard if needed
        let phoneNumber = customerPhone;
        if (phoneNumber && !phoneNumber.startsWith('+')) {
            // If it's a 10-digit number, add +91 prefix
            if (/^\d{10}$/.test(phoneNumber)) {
                phoneNumber = `+91${phoneNumber}`;
            }
        }

        // Show confirmation alert before making call
        Alert.alert(
            "Call Customer",
            `Would you like to call ${customerName || 'the customer'} at ${phoneNumber}?`,
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Call",
                    onPress: () => {
                        const url = `tel:${phoneNumber}`;
                        Linking.canOpenURL(url)
                            .then(supported => {
                                if (supported) {
                                    return Linking.openURL(url);
                                } else {
                                    Alert.alert(
                                        "Call Error",
                                        "Phone calls are not supported on this device.",
                                        [{ text: "OK", style: 'default' }],
                                        { cancelable: true }
                                    );
                                }
                            })
                            .catch(err => {
                                console.error('Error making phone call:', err);
                                Alert.alert(
                                    "Call Error",
                                    "Could not make the phone call. Please try again.",
                                    [{ text: "OK", style: 'default' }],
                                    { cancelable: true }
                                );
                            });
                    }
                }
            ],
            { cancelable: true }
        );
    };

    // Open maps application with directions
    const openMapsWithDirections = (address, coordinates = null) => {
        try {
            let url;

            // If we have coordinates, use them for more precise navigation
            if (coordinates && coordinates.latitude && coordinates.longitude) {
                if (Platform.OS === 'ios') {
                    url = `maps:?q=${coordinates.latitude},${coordinates.longitude}`;
                } else {
                    url = `geo:${coordinates.latitude},${coordinates.longitude}?q=${coordinates.latitude},${coordinates.longitude}`;
                }
            } else {
                // Otherwise use the address string
                const encodedAddress = encodeURIComponent(address);
                if (Platform.OS === 'ios') {
                    url = `maps:?q=${encodedAddress}`;
                } else {
                    url = `geo:0,0?q=${encodedAddress}`;
                }
            }

            // Check if the URL can be opened
            Linking.canOpenURL(url)
                .then(supported => {
                    if (supported) {
                        return Linking.openURL(url);
                    } else {
                        // Fallback to Google Maps web URL
                        const fallbackUrl = coordinates
                            ? `https://maps.google.com/?q=${coordinates.latitude},${coordinates.longitude}`
                            : `https://maps.google.com/?q=${encodeURIComponent(address)}`;
                        return Linking.openURL(fallbackUrl);
                    }
                })
                .catch(err => {
                    console.error('Error opening maps app:', err);
                    Alert.alert(
                        "Navigation Error",
                        "Could not open maps application. Please try again.",
                        [{ text: "OK", style: 'default' }],
                        { cancelable: true }
                    );
                });
        } catch (error) {
            console.error('Error in openMapsWithDirections:', error);
        }
    };
}
