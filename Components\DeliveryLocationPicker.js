import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ActivityIndicator,
    Alert,
    Dimensions,
    Platform,
    TextInput,
    KeyboardAvoidingView,
    Animated,
    ScrollView,
    Vibration
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { isWithinDeliveryZone, isValidPincode, getPincodeMessage } from '../utils/locationUtils';
import { getPermissionStatus } from '../utils/permissionStorage';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import PincodeAlert from './PincodeAlert';

const { width, height } = Dimensions.get('window');
const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.004; // Adjusted delta for approximately 300-400m view
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

const DeliveryLocationPicker = ({
    onLocationSelected = () => {},
    onClose = () => {},
    onValidationChange = () => {},
    existingAddress = null // New prop for editing existing address
}) => {
    // State variables
    const [location, setLocation] = useState(
        existingAddress?.coordinates || existingAddress?.latitude ? {
            latitude: existingAddress?.coordinates?.latitude || existingAddress?.latitude,
            longitude: existingAddress?.coordinates?.longitude || existingAddress?.longitude
        } : null // Start with null to trigger location request
    );
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [isWithinZone, setIsWithinZone] = useState(true);
    const [showAddressForm, setShowAddressForm] = useState(false);
    const [showMap, setShowMap] = useState(true);
    const [addressDetails, setAddressDetails] = useState({
        flatNumber: existingAddress?.doorNo || '',
        street: existingAddress?.streetName || '',
        locality: existingAddress?.area || '',
        district: existingAddress?.district || 'Vellore',
        pincode: existingAddress?.pincode || '',
        addressType: existingAddress?.type || existingAddress?.addressType || 'Home'
    });

    // Field validation state
    const [fieldErrors, setFieldErrors] = useState({
        flatNumber: '',
        street: '',
        locality: '',
        district: '',
        pincode: ''
    });

    // Pincode validation state
    const [showPincodeAlert, setShowPincodeAlert] = useState(false);
    const [pincodeMessage, setPincodeMessage] = useState(null);
    const [canSaveAddress, setCanSaveAddress] = useState(true);
    const [isUsingLiveLocation, setIsUsingLiveLocation] = useState(false);

    // Refs
    const mapRef = useRef(null);
    const hasShownDeliveryZoneWarning = useRef(false);

    // Animation values for the pulsating effect
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const pulseOpacity = useRef(new Animated.Value(0.7)).current;

    // Set up smooth pulsating animation
    useEffect(() => {
        const startPulseAnimation = () => {
            Animated.parallel([
                Animated.sequence([
                    Animated.timing(pulseAnim, {
                        toValue: 1.4,
                        duration: 1200,
                        useNativeDriver: true
                    }),
                    Animated.timing(pulseAnim, {
                        toValue: 1,
                        duration: 1200,
                        useNativeDriver: true
                    })
                ]),
                Animated.sequence([
                    Animated.timing(pulseOpacity, {
                        toValue: 0.3,
                        duration: 1200,
                        useNativeDriver: true
                    }),
                    Animated.timing(pulseOpacity, {
                        toValue: 0.8,
                        duration: 1200,
                        useNativeDriver: true
                    })
                ])
            ]).start(() => {
                startPulseAnimation();
            });
        };

        startPulseAnimation();

        return () => {
            pulseAnim.stopAnimation();
            pulseOpacity.stopAnimation();
        };
    }, []);

    // If editing an existing address, automatically show the address form without map
    useEffect(() => {
        if (existingAddress) {
            // Immediately show the address form for editing without map interaction
            setShowAddressForm(true);
            // Hide the map completely when editing an address
            setShowMap(false);
        }
    }, [existingAddress]);

    // Initialize location when component mounts
    useEffect(() => {
        const initializeLocation = async () => {
            // If editing existing address, don't auto-get location
            if (existingAddress) {
                return;
            }

            // If location is already set, zoom to it
            if (location) {
                // Zoom to existing location after a short delay
                const timer = setTimeout(() => {
                    if (mapRef.current) {
                        mapRef.current.animateToRegion({
                            ...location,
                            latitudeDelta: 0.004,
                            longitudeDelta: 0.004 * ASPECT_RATIO
                        }, 500);
                    }
                }, 300);
                return () => clearTimeout(timer);
            }

            // If no location set, try to get user's current location
            await getCurrentLocationOnMount();
        };

        initializeLocation();
    }, []);

    // Get current location on component mount
    const getCurrentLocationOnMount = async () => {
        try {
            setLoading(true);
            setError(null);

            // Check if location permission is already granted
            const { status: existingStatus } = await Location.getForegroundPermissionsAsync();

            let permissionStatus = existingStatus;

            // If permission not granted, check if it was already handled in initial setup
            if (existingStatus !== 'granted') {
                // Check if location permission was already handled in initial setup
                const permissionData = await getPermissionStatus();
                const locationAlreadyHandled = permissionData?.location === 'granted' ||
                                             permissionData?.location === 'denied';

                if (locationAlreadyHandled && permissionData?.location === 'denied') {
                    // User already denied location in initial setup, don't ask again
                    console.log('Location permission already denied in initial setup, using default location');
                    permissionStatus = 'denied';
                } else if (!locationAlreadyHandled) {
                    // Show permission request dialog only if not handled in initial setup
                    const shouldRequest = await new Promise((resolve) => {
                        Alert.alert(
                            "Location Access",
                            "We'd like to show your current location on the map to make address selection easier. This helps you quickly find and confirm your delivery address.",
                            [
                                {
                                    text: "Use Default Area",
                                    style: "cancel",
                                    onPress: () => resolve(false)
                                },
                                {
                                    text: "Allow Location",
                                    onPress: () => resolve(true)
                                }
                            ],
                            { cancelable: false }
                        );
                    });

                    if (shouldRequest) {
                        const { status } = await Location.requestForegroundPermissionsAsync();
                        permissionStatus = status;
                    } else {
                        permissionStatus = 'denied';
                    }
                } else {
                    // Permission was granted in initial setup, try to use it
                    const { status } = await Location.requestForegroundPermissionsAsync();
                    permissionStatus = status;
                }
            }

            if (permissionStatus !== 'granted') {
                // If permission denied, fall back to default location
                console.log('Location permission denied, using default location');
                const defaultLocation = {
                    latitude: 12.8997028,
                    longitude: 79.136073
                };
                setLocation(defaultLocation);
                setLoading(false);
                return;
            }

            // Get current position
            const position = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.High,
                maximumAge: 10000
            });

            const newLocation = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude
            };

            setLocation(newLocation);
            setIsUsingLiveLocation(true);
            validateLocation(newLocation);

            // Animate to user's location with precise centering
            if (mapRef.current) {
                mapRef.current.animateToRegion({
                    latitude: newLocation.latitude,
                    longitude: newLocation.longitude,
                    latitudeDelta: 0.004,
                    longitudeDelta: 0.004 * ASPECT_RATIO
                }, 1000);

                // Ensure perfect centering after initial animation
                setTimeout(() => {
                    if (mapRef.current) {
                        mapRef.current.animateToRegion({
                            latitude: newLocation.latitude,
                            longitude: newLocation.longitude,
                            latitudeDelta: 0.004,
                            longitudeDelta: 0.004 * ASPECT_RATIO
                        }, 200);
                    }
                }, 1100);
            }

            // Get address from coordinates
            fetchAddressFromCoordinates(newLocation);

        } catch (err) {
            console.error('Error getting current location on mount:', err);
            // Fall back to default location on error
            const defaultLocation = {
                latitude: 12.8997028,
                longitude: 79.136073
            };
            setLocation(defaultLocation);
            setError('Could not get your location, using default area');
        } finally {
            setLoading(false);
        }
    };

    // Validate if location is within delivery zone
    const validateLocation = (loc) => {
        const withinZone = isWithinDeliveryZone(loc);
        setIsWithinZone(withinZone);
        onValidationChange(withinZone);

        if (!withinZone && !hasShownDeliveryZoneWarning.current) {
            hasShownDeliveryZoneWarning.current = true;
            Alert.alert(
                "Outside Delivery Zone",
                "The selected location appears to be outside our delivery zone. You can still save this address, but we may not be able to deliver to this location.",
                [{ text: "OK" }],
                { cancelable: true }
            );
        }
    };

    // Get current location
    const getCurrentLocation = async () => {
        try {
            setLoading(true);
            setError(null);

            const { status } = await Location.requestForegroundPermissionsAsync();

            if (status !== 'granted') {
                setError('Location permission denied');
                setLoading(false);

                Alert.alert(
                    "Location Permission Required",
                    "Please enable location services to use this feature.",
                    [{ text: "OK" }],
                    { cancelable: true }
                );
                return;
            }

            const position = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Highest,
                maximumAge: 10000
            });

            const newLocation = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude
            };

            setLocation(newLocation);
            setIsUsingLiveLocation(true);
            validateLocation(newLocation);

            // Ensure map centers exactly on live location
            if (mapRef.current) {
                // First set the region immediately
                mapRef.current.animateToRegion({
                    latitude: newLocation.latitude,
                    longitude: newLocation.longitude,
                    latitudeDelta: 0.004, // Approximately 300-400m view
                    longitudeDelta: 0.004 * ASPECT_RATIO
                }, 800);

                // Then ensure perfect centering after animation
                setTimeout(() => {
                    if (mapRef.current) {
                        mapRef.current.animateToRegion({
                            latitude: newLocation.latitude,
                            longitude: newLocation.longitude,
                            latitudeDelta: 0.004,
                            longitudeDelta: 0.004 * ASPECT_RATIO
                        }, 200);
                    }
                }, 900);
            }

            // Get address from coordinates
            fetchAddressFromCoordinates(newLocation);

        } catch (err) {
            console.error('Error getting current location:', err);
            setError('Could not get your location');

            Alert.alert(
                "Location Error",
                "We couldn't get your current location. Please make sure location services are enabled and try again, or manually select a location on the map.",
                [{ text: "OK" }],
                { cancelable: true }
            );
        } finally {
            setLoading(false);
        }
    };

    // Fetch address details from coordinates
    const fetchAddressFromCoordinates = async (coords) => {
        try {
            const result = await Location.reverseGeocodeAsync({
                latitude: coords.latitude,
                longitude: coords.longitude
            });

            if (result && result.length > 0) {
                const address = result[0];
                console.log('Address from map:', address);

                // Update address details with fetched information
                // Keep existing values if the API doesn't return specific fields
                // Map city to district and ensure correct field mapping
                const newAddressDetails = {
                    ...addressDetails,
                    street: address.street || addressDetails.street,
                    locality: address.district || address.region || addressDetails.locality,
                    district: address.city || 'Vellore', // Map city to district
                    pincode: address.postalCode || addressDetails.pincode
                };

                setAddressDetails(newAddressDetails);

                // Validate pincode if we got one from the map
                if (address.postalCode && address.postalCode.length === 6) {
                    console.log('Validating pincode from map:', address.postalCode);
                    const message = getPincodeMessage(address.postalCode);
                    setPincodeMessage(message);
                    setCanSaveAddress(message.type === 'success');

                    if (message.type !== 'success') {
                        // Show pincode alert for invalid pincode from map
                        setShowPincodeAlert(true);
                        console.log('Map location pincode validation failed:', message);
                    } else {
                        console.log('Map location pincode validation passed:', message);
                    }
                }

                console.log("Address details retrieved from map, pincode validated");
                return address;
            }
            return null;
        } catch (error) {
            console.error('Error getting address:', error);
            return null;
        }
    };

    // Handle map region change
    const handleRegionChange = (region) => {
        const newLocation = {
            latitude: region.latitude,
            longitude: region.longitude
        };
        setLocation(newLocation);
        validateLocation(newLocation);

        // If user manually moves map, turn off live location indicator
        if (isUsingLiveLocation) {
            setIsUsingLiveLocation(false);
        }
    };

    // Handle region change complete
    const handleRegionChangeComplete = async (region) => {
        // Update location coordinates
        const newLocation = {
            latitude: region.latitude,
            longitude: region.longitude
        };
        setLocation(newLocation);

        // If user manually moves map, turn off live location indicator
        if (isUsingLiveLocation) {
            setIsUsingLiveLocation(false);
        }

        // Fetch address details and validate pincode when user stops dragging
        try {
            const result = await Location.reverseGeocodeAsync({
                latitude: region.latitude,
                longitude: region.longitude
            });

            if (result && result.length > 0) {
                const address = result[0];
                console.log('Address from map region change:', address);

                // Validate pincode if we got one from the map
                if (address.postalCode && address.postalCode.length === 6) {
                    console.log('Validating pincode from map region change:', address.postalCode);
                    const message = getPincodeMessage(address.postalCode);
                    setPincodeMessage(message);
                    setCanSaveAddress(message.type === 'success');

                    // Update address details with the new pincode
                    setAddressDetails(prev => ({
                        ...prev,
                        pincode: address.postalCode,
                        district: address.city || 'Vellore',
                        locality: address.district || address.region || prev.locality,
                        street: address.street || prev.street
                    }));

                    if (message.type !== 'success') {
                        // Show pincode alert for invalid pincode from map
                        setShowPincodeAlert(true);
                        console.log('Map region pincode validation failed:', message);
                    } else {
                        console.log('Map region pincode validation passed:', message);
                    }
                }
            }
        } catch (error) {
            console.error('Error getting address from region change:', error);
        }
    };

    // Set location and proceed to address form with confirmation alert
    const handleSetLocation = () => {
        if (location) {
            if (!canSaveAddress) {
                // Show pincode alert if location is outside delivery area
                if (pincodeMessage) {
                    setShowPincodeAlert(true);
                } else {
                    Alert.alert(
                        "Outside Delivery Area",
                        "The selected location is outside our delivery area. Please select a location within our service zones.",
                        [{ text: "OK" }],
                        { cancelable: true }
                    );
                }
                return;
            }

            // Show confirmation alert before proceeding for valid locations
            Alert.alert(
                "Confirm Delivery Location",
                "Would you like to deliver your order to this location?",
                [
                    {
                        text: "Cancel",
                        style: "cancel"
                    },
                    {
                        text: "Confirm",
                        onPress: () => setShowAddressForm(true)
                    }
                ],
                { cancelable: true }
            );
        } else {
            Alert.alert(
                "No Location Selected",
                "Please select a location first.",
                [{ text: "OK" }]
            );
        }
    };

    // Validate pincode and show appropriate message
    const validatePincode = (pincode) => {
        const message = getPincodeMessage(pincode);
        setPincodeMessage(message);
        setCanSaveAddress(message.type === 'success');

        if (message.type !== 'success') {
            setShowPincodeAlert(true);
        }

        return message.type === 'success';
    };

    // Validate all address fields
    const validateAddressFields = () => {
        let isValid = true;
        const errors = {
            flatNumber: '',
            street: '',
            locality: '',
            district: '',
            pincode: ''
        };

        // Validate Flat/House Number
        if (!addressDetails.flatNumber.trim()) {
            errors.flatNumber = 'Flat/House number is required';
            isValid = false;
        }

        // Validate Street Name
        if (!addressDetails.street.trim()) {
            errors.street = 'Street name is required';
            isValid = false;
        }

        // Validate Area/Locality
        if (!addressDetails.locality.trim()) {
            errors.locality = 'Area/Locality is required';
            isValid = false;
        }

        // Validate District
        if (!addressDetails.district.trim()) {
            errors.district = 'District is required';
            isValid = false;
        }

        // Validate Pincode
        if (!addressDetails.pincode.trim()) {
            errors.pincode = 'Pincode is required';
            isValid = false;
        } else if (!/^\d{6}$/.test(addressDetails.pincode.trim())) {
            errors.pincode = 'Pincode must be 6 digits';
            isValid = false;
        } else {
            // Validate pincode for service availability
            const pincodeValid = validatePincode(addressDetails.pincode.trim());
            if (!pincodeValid) {
                errors.pincode = 'Service not available in this pincode';
                isValid = false;
            }
        }

        setFieldErrors(errors);
        return isValid;
    };

    // Save complete address
    const saveAddress = () => {
        // Check if address can be saved (valid pincode)
        if (!canSaveAddress) {
            const message = getPincodeMessage(addressDetails.pincode.trim());
            setPincodeMessage(message);
            setShowPincodeAlert(true);
            return;
        }

        // Validate all required fields
        if (!validateAddressFields()) {
            Alert.alert(
                "Missing or Invalid Information",
                "Please fill in all required fields correctly.",
                [{ text: "OK" }]
            );
            return;
        }

        // Format the complete address in standardized format
        const formattedAddress = {
            // If editing, preserve the original ID
            ...(existingAddress?._id ? { _id: existingAddress._id } : {}),

            // Preserve other important fields if editing
            ...(existingAddress?.isPrimary ? { isPrimary: existingAddress.isPrimary } : {}),
            ...(existingAddress?.isDefault ? { isDefault: existingAddress.isDefault } : {}),
            ...(existingAddress?.isWithinDeliveryZone ? { isWithinDeliveryZone: existingAddress.isWithinDeliveryZone } : {}),

            coordinates: location,
            // Map UI field names to backend field names
            doorNo: addressDetails.flatNumber.trim(),
            flatNumber: addressDetails.flatNumber.trim(), // Add this for compatibility
            streetName: addressDetails.street.trim(),
            street: addressDetails.street.trim(), // Add this for compatibility
            area: addressDetails.locality.trim(),
            locality: addressDetails.locality.trim(), // Add this for compatibility
            district: addressDetails.district.trim(),
            city: addressDetails.district.trim(), // Add this for compatibility
            pincode: addressDetails.pincode.trim(),
            addressType: addressDetails.addressType,
            type: addressDetails.addressType, // Add this for compatibility
            // Create standardized full address string
            fullAddress: `${addressDetails.flatNumber.trim()}, ${addressDetails.street.trim()}, ${addressDetails.locality.trim()}, ${addressDetails.district.trim()}, ${addressDetails.pincode.trim()}`
        };

        // If editing an existing address, include the ID
        if (existingAddress && existingAddress._id) {
            formattedAddress._id = existingAddress._id;
        }

        // Pass the complete address back to parent component
        onLocationSelected(formattedAddress);
        onClose();
    };

    // Render address form
    const renderAddressForm = () => (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.addressFormContainer}
        >
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.addressFormHeader}>
                    <Text style={styles.addressFormTitle}>
                        {existingAddress ? "Edit Primary Address" : "Complete Your Address"}
                    </Text>
                    <TouchableOpacity
                        style={styles.backButton}
                        onPress={existingAddress ? onClose : () => setShowAddressForm(false)}
                    >
                        <MaterialIcons name="arrow-back" size={24} color="#FFFFFF" />
                    </TouchableOpacity>
                </View>

                {/* Information banner */}
                <View style={styles.infoBanner}>
                    <MaterialIcons name="info" size={18} color="#3B82F6" />
                    <Text style={styles.infoText}>
                        Please enter your complete address details. Fields marked with * are required.
                    </Text>
                </View>

                <View style={styles.formField}>
                    <Text style={styles.fieldLabel}>Flat/House/Office Number <Text style={styles.requiredStar}>*</Text></Text>
                    <TextInput
                        style={[styles.input, fieldErrors.flatNumber ? styles.inputError : null]}
                        value={addressDetails.flatNumber}
                        onChangeText={(text) => {
                            setAddressDetails({...addressDetails, flatNumber: text});
                            if (text.trim()) setFieldErrors({...fieldErrors, flatNumber: ''});
                        }}
                        placeholder="e.g., Flat 101, House No. 42"
                    />
                    {fieldErrors.flatNumber ? (
                        <Text style={styles.errorText}>{fieldErrors.flatNumber}</Text>
                    ) : null}
                </View>

                <View style={styles.formField}>
                    <Text style={styles.fieldLabel}>Street Name <Text style={styles.requiredStar}>*</Text></Text>
                    <TextInput
                        style={[styles.input, fieldErrors.street ? styles.inputError : null]}
                        value={addressDetails.street}
                        onChangeText={(text) => {
                            setAddressDetails({...addressDetails, street: text});
                            if (text.trim()) setFieldErrors({...fieldErrors, street: ''});
                        }}
                        placeholder="e.g., Main Street"
                    />
                    {fieldErrors.street ? (
                        <Text style={styles.errorText}>{fieldErrors.street}</Text>
                    ) : null}
                </View>

                <View style={styles.formField}>
                    <Text style={styles.fieldLabel}>Area/Locality <Text style={styles.requiredStar}>*</Text></Text>
                    <TextInput
                        style={[styles.input, fieldErrors.locality ? styles.inputError : null]}
                        value={addressDetails.locality}
                        onChangeText={(text) => {
                            setAddressDetails({...addressDetails, locality: text});
                            if (text.trim()) setFieldErrors({...fieldErrors, locality: ''});
                        }}
                        placeholder="e.g., Gandhi Nagar"
                    />
                    {fieldErrors.locality ? (
                        <Text style={styles.errorText}>{fieldErrors.locality}</Text>
                    ) : null}
                </View>

                <View style={styles.formRow}>
                    <View style={[styles.formField, {flex: 1, marginRight: 10}]}>
                        <Text style={styles.fieldLabel}>District <Text style={styles.requiredStar}>*</Text></Text>
                        <TextInput
                            style={[styles.input, fieldErrors.district ? styles.inputError : null]}
                            value={addressDetails.district}
                            onChangeText={(text) => {
                                setAddressDetails({...addressDetails, district: text});
                                if (text.trim()) setFieldErrors({...fieldErrors, district: ''});
                            }}
                            placeholder="e.g., Vellore"
                        />
                        {fieldErrors.district ? (
                            <Text style={styles.errorText}>{fieldErrors.district}</Text>
                        ) : null}
                    </View>
                    <View style={[styles.formField, {flex: 1}]}>
                        <Text style={styles.fieldLabel}>Pincode <Text style={styles.requiredStar}>*</Text></Text>
                        <TextInput
                            style={[styles.input, fieldErrors.pincode ? styles.inputError : null]}
                            value={addressDetails.pincode}
                            onChangeText={(text) => {
                                setAddressDetails({...addressDetails, pincode: text});
                                if (/^\d{6}$/.test(text.trim())) {
                                    setFieldErrors({...fieldErrors, pincode: ''});
                                    // Validate pincode when user enters 6 digits
                                    validatePincode(text.trim());
                                }
                            }}
                            placeholder="e.g., 632001"
                            keyboardType="numeric"
                            maxLength={6}
                        />
                        {fieldErrors.pincode ? (
                            <Text style={styles.errorText}>{fieldErrors.pincode}</Text>
                        ) : null}
                    </View>
                </View>

                <View style={[styles.formField, styles.addressTypeField]}>
                    <Text style={[styles.fieldLabel, styles.addressTypeLabel]}>Select Address Type</Text>
                    <View style={styles.addressTypeContainer}>
                        {[
                            { type: 'Home', icon: 'home' },
                            { type: 'Work', icon: 'work' },
                            { type: 'Other', icon: 'place' }
                        ].map(item => (
                            <TouchableOpacity
                                key={item.type}
                                style={[
                                    styles.addressTypeButton,
                                    addressDetails.addressType === item.type && styles.addressTypeButtonSelected
                                ]}
                                onPress={() => {
                                    // Add a small vibration effect when selecting an address type
                                    if (Platform.OS === 'android' && Vibration) {
                                        Vibration.vibrate(50);
                                    }
                                    setAddressDetails({...addressDetails, addressType: item.type});
                                }}
                            >
                                <View style={styles.addressTypeContent}>
                                    <MaterialIcons
                                        name={item.icon}
                                        size={18}
                                        color={addressDetails.addressType === item.type ? '#FFFFFF' : '#555'}
                                        style={styles.addressTypeIcon}
                                    />
                                    <Text style={[
                                        styles.addressTypeText,
                                        addressDetails.addressType === item.type && styles.addressTypeTextSelected
                                    ]}>{item.type}</Text>
                                </View>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>

                <TouchableOpacity style={styles.saveButton} onPress={saveAddress}>
                    <Text style={styles.saveButtonText}>
                        {existingAddress ? "Update Primary Address" : "Save Address"}
                    </Text>
                </TouchableOpacity>

                {!existingAddress && (
                    <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={() => setShowAddressForm(false)}
                    >
                        <Text style={styles.cancelButtonText}>Back to Map</Text>
                    </TouchableOpacity>
                )}
                {existingAddress && (
                    <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={onClose}
                    >
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                    </TouchableOpacity>
                )}
            </ScrollView>

            {/* Pincode Alert Modal */}
            <PincodeAlert
                visible={showPincodeAlert}
                onClose={() => setShowPincodeAlert(false)}
                onBrowseProducts={() => {
                    setShowPincodeAlert(false);
                    // Navigate to home screen to browse products
                    onClose();
                }}
                pincodeMessage={pincodeMessage}
                showBrowseOption={true}
            />
        </KeyboardAvoidingView>
    );

    // If showing address form, render it
    if (showAddressForm) {
        return renderAddressForm();
    }

    // If editing an existing address, don't show the map picker at all
    if (existingAddress) {
        // Force show the address form for editing
        if (!showAddressForm) {
            setShowAddressForm(true);
        }
        return renderAddressForm();
    }

    // Render map picker for new addresses only
    return (
        <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
            <View style={styles.mapContainer}>
                <MapView
                    ref={mapRef}
                    style={styles.map}
                    provider={PROVIDER_GOOGLE}
                    initialRegion={location ? {
                        ...location,
                        latitudeDelta: LATITUDE_DELTA,
                        longitudeDelta: LONGITUDE_DELTA
                    } : {
                        // Temporary default region while getting user location
                        latitude: 12.8997028,
                        longitude: 79.136073,
                        latitudeDelta: LATITUDE_DELTA,
                        longitudeDelta: LONGITUDE_DELTA
                    }}
                    showsUserLocation={true}
                    showsMyLocationButton={false}
                    showsBuildings={true}
                    showsTraffic={true}
                    showsIndoors={true}
                    showsPointsOfInterest={true}
                    showsCompass={true}
                    mapType="standard"
                    onRegionChange={handleRegionChange}
                    onRegionChangeComplete={handleRegionChangeComplete}
                />

                {/* Fixed center indicator - always at map center */}
                <View style={styles.absoluteCenterContainer}>
                    <Animated.View
                        style={[
                            styles.centerDiamond,
                            {
                                transform: [{ scale: pulseAnim }],
                                opacity: pulseOpacity,
                                backgroundColor: canSaveAddress ? 'rgba(163, 22, 33, 0.9)' : 'rgba(239, 68, 68, 0.9)',
                                borderColor: canSaveAddress ? '#A31621' : '#EF4444'
                            }
                        ]}
                    />
                </View>

                {/* Fixed marker label above center */}
                <View style={styles.fixedMarkerContainer}>
                    <View style={[
                        styles.markerLabel,
                        !canSaveAddress && { backgroundColor: '#FEE2E2', borderColor: '#EF4444' }
                    ]}>
                        <Text style={[
                            styles.markerLabelText,
                            !canSaveAddress && { color: '#DC2626' }
                        ]}>
                            {canSaveAddress ? 'The order is delivered here' : 'Outside delivery area'}
                        </Text>
                        <View style={[
                            styles.markerPointer,
                            !canSaveAddress && { borderTopColor: '#FEE2E2' }
                        ]} />
                    </View>
                </View>

                {loading && (
                    <View style={styles.loadingOverlay}>
                        <ActivityIndicator size="large" color="#A31621" />
                        <Text style={styles.loadingText}>
                            {location ? 'Loading map...' : 'Getting your location...'}
                        </Text>
                    </View>
                )}

                {error && (
                    <View style={styles.errorContainer}>
                        <Text style={styles.errorText}>{error}</Text>
                    </View>
                )}

                <TouchableOpacity
                    style={styles.currentLocationButton}
                    onPress={getCurrentLocation}
                    activeOpacity={0.8}
                >
                    <MaterialIcons name="my-location" size={20} color="#A31621" />
                    <Text style={styles.currentLocationText}>Live Location</Text>
                </TouchableOpacity>

                {/* Enhanced Live Location Indicator */}
                {isUsingLiveLocation && (
                    <View style={styles.liveLocationIndicator}>
                        <View style={styles.liveLocationPulse}>
                            <MaterialIcons name="my-location" size={14} color="white" />
                        </View>
                        <Text style={styles.liveLocationText}>Live Location</Text>
                    </View>
                )}

                {/* Set Location Button */}
                <TouchableOpacity
                    style={[
                        styles.setLocationButton,
                        !canSaveAddress && { backgroundColor: '#EF4444' }
                    ]}
                    onPress={handleSetLocation}
                >
                    <MaterialIcons
                        name={canSaveAddress ? "check-circle" : "warning"}
                        size={20}
                        color="white"
                    />
                    <Text style={styles.setLocationButtonText}>
                        {canSaveAddress ? 'Set Location' : 'Outside Delivery Area'}
                    </Text>
                </TouchableOpacity>

                {!isWithinZone && (
                    <View style={styles.warningBanner}>
                        <MaterialIcons name="warning" size={20} color="#fff" />
                        <View style={styles.warningTextContainer}>
                            <Text style={styles.warningText}>Outside delivery zone</Text>
                            <Text style={styles.warningSubtext}>You can still select this location</Text>
                        </View>
                    </View>
                )}

                <View style={styles.mapAttributionContainer} />
            </View>

            {/* Pincode Alert Modal */}
            <PincodeAlert
                visible={showPincodeAlert}
                onClose={() => setShowPincodeAlert(false)}
                onBrowseProducts={() => {
                    setShowPincodeAlert(false);
                    // Navigate to home screen to browse products
                    onClose();
                }}
                pincodeMessage={pincodeMessage}
                showBrowseOption={true}
            />
        </KeyboardAvoidingView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    },

    mapContainer: {
        flex: 1,
        position: 'relative'
    },
    map: {
        ...StyleSheet.absoluteFillObject
    },
    absoluteCenterContainer: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        width: 0,
        height: 0,
        zIndex: 10,
    },
    centerDiamond: {
        position: 'absolute',
        top: 5, // Reduced gap - closer to pointer tip
        left: -5, // Center horizontally (same for both states)
        width: 10,
        height: 10,
        borderRadius: 5,
        backgroundColor: 'rgba(163, 22, 33, 0.9)',
        borderWidth: 1.5,
        borderColor: '#A31621',
    },
    fixedMarkerContainer: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        width: 0,
        height: 0,
    },
    markerLabel: {
        position: 'absolute',
        top: -45, // Position above center (same for both states)
        left: -100, // Center horizontally (same for both states)
        width: 200,
        backgroundColor: 'rgba(0,0,0,0.85)',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 6,
        borderWidth: 1.5,
        borderColor: '#A31621',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3,
        elevation: 4,
        marginBottom: 8, // Space for the pointer
    },
    markerPointer: {
        position: 'absolute',
        bottom: -5, // Position at bottom of label (same for both states)
        left: 93, // Center horizontally (same for both states)
        width: 10,
        height: 10,
        backgroundColor: 'rgba(0,0,0,0.85)',
        borderLeftWidth: 1.5,
        borderBottomWidth: 1.5,
        borderColor: '#A31621',
        transform: [{ rotate: '45deg' }],
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.15,
        shadowRadius: 1,
        elevation: 2
    },
    markerLabelText: {
        color: 'white',
        fontSize: 13.5,
        fontWeight: '600',
        textAlign: 'center',
        letterSpacing: 0.2,
        flexShrink: 1,
        flexWrap: 'nowrap'
    },
    loadingOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(255,255,255,0.9)',
        alignItems: 'center',
        justifyContent: 'center'
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#A31621',
        fontWeight: '500'
    },
    errorContainer: {
        position: 'absolute',
        top: '50%',
        left: 20,
        right: 20,
        backgroundColor: 'rgba(255,0,0,0.7)',
        padding: 10,
        borderRadius: 8,
        alignItems: 'center'
    },
    errorText: {
        color: 'white',
        textAlign: 'center'
    },
    currentLocationButton: {
        position: 'absolute',
        bottom: 80,
        right: 20,
        backgroundColor: 'white',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5,
        borderWidth: 1,
        borderColor: '#E5E7EB'
    },
    currentLocationText: {
        color: '#A31621',
        fontSize: 12,
        fontWeight: '600',
        marginLeft: 6,
        letterSpacing: 0.2
    },
    liveLocationIndicator: {
        position: 'absolute',
        bottom: 140,
        right: 20,
        backgroundColor: '#A31621',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 20,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
        borderWidth: 2,
        borderColor: 'white'
    },
    liveLocationPulse: {
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 6
    },
    liveLocationText: {
        color: 'white',
        fontSize: 13,
        fontWeight: '700',
        letterSpacing: 0.3
    },
    setLocationButton: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        backgroundColor: '#A31621',
        height: 50,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5
    },
    setLocationButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
        marginLeft: 10
    },
    warningBanner: {
        position: 'absolute',
        top: 10,
        left: 10,
        right: 10,
        backgroundColor: '#ff6b6b',
        padding: 8,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        elevation: 3
    },
    warningTextContainer: {
        flex: 1,
        marginLeft: 5
    },
    warningText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14
    },
    warningSubtext: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12
    },
    mapAttributionContainer: {
        position: 'absolute',
        bottom: 0,
        right: 0,
        backgroundColor: 'transparent',
        width: 100,
        height: 20
    },
    addressFormContainer: {
        flex: 1,
        backgroundColor: '#f8f8f8',
        padding: 0
    },
    addressFormHeader: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#A31621',
        paddingVertical: 15,
        paddingHorizontal: 20,
        marginBottom: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        position: 'relative'
    },
    addressFormTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#FFFFFF',
        textAlign: 'center'
    },
    backButton: {
        position: 'absolute',
        left: 15,
        padding: 5,
        borderRadius: 20
    },
    // New styles for the info banner
    infoBanner: {
        backgroundColor: '#f8f8f8',
        borderRadius: 8,
        padding: 12,
        marginHorizontal: 20,
        marginBottom: 15,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e0e0e0'
    },
    infoText: {
        color: '#555',
        fontSize: 13,
        marginLeft: 8,
        flex: 1
    },
    formField: {
        marginBottom: 18,
        marginHorizontal: 20
    },
    formRow: {
        flexDirection: 'row',
        marginBottom: 18,
        marginHorizontal: 20
    },
    fieldLabel: {
        fontSize: 15,
        fontWeight: '600',
        marginBottom: 8,
        color: '#333',
        letterSpacing: 0.3
    },
    input: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 10,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#FFFFFF',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 1,
        elevation: 1
    },
    addressTypeContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: 20,
        marginBottom: 20
    },
    addressTypeButton: {
        flex: 1,
        padding: 10,
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 10,
        marginHorizontal: 5,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1,
        height: 60,
        backgroundColor: '#FFFFFF'
    },
    addressTypeButtonSelected: {
        backgroundColor: '#A31621',
        borderColor: '#A31621',
        transform: [{scale: 1.03}]
    },
    addressTypeContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center'
    },
    addressTypeIcon: {
        marginRight: 5
    },
    addressTypeText: {
        color: '#333',
        fontWeight: '600',
        fontSize: 14
    },
    addressTypeTextSelected: {
        color: 'white'
    },
    addressTypeField: {
        marginTop: 10,
        marginBottom: 15
    },
    addressTypeLabel: {
        fontSize: 16,
        fontWeight: '700',
        color: '#222',
        marginBottom: 12,
        textAlign: 'center'
    },
    saveButton: {
        backgroundColor: '#A31621',
        padding: 15,
        borderRadius: 10,
        alignItems: 'center',
        marginTop: 25,
        marginHorizontal: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
        elevation: 3
    },
    saveButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 17,
        letterSpacing: 0.5
    },
    // New styles for the cancel button
    cancelButton: {
        backgroundColor: '#f5f5f5',
        padding: 15,
        borderRadius: 10,
        alignItems: 'center',
        marginTop: 12,
        marginBottom: 30,
        marginHorizontal: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1
    },
    cancelButtonText: {
        color: '#555',
        fontWeight: 'bold',
        fontSize: 16
    },
    // New styles for validation
    requiredStar: {
        color: '#A31621',
        fontWeight: 'bold',
        fontSize: 16
    },
    inputError: {
        borderColor: '#A31621',
        backgroundColor: '#FFF5F5',
        borderWidth: 1.5
    },
    errorText: {
        color: '#A31621',
        fontSize: 13,
        marginTop: 6,
        marginLeft: 2,
        fontWeight: '500'
    }
});

export default DeliveryLocationPicker;
