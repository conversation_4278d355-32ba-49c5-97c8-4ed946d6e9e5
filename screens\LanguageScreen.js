import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useUser } from '../context/UserContext';

const LanguageScreen = () => {
    const navigation = useNavigation();
    const { currentUser, updateUserPreferences } = useUser();
    
    const [selectedLanguage, setSelectedLanguage] = useState(
        currentUser.preferences?.language || 'English'
    );
    
    // Animation for toast notification
    const toastAnimation = useRef(new Animated.Value(0)).current;
    const [toastVisible, setToastVisible] = useState(false);
    const [toastMessage, setToastMessage] = useState('');
    
    // Available languages
    const languages = [
        { id: 'en', name: 'English', flag: '🇬🇧' },
        { id: 'hi', name: 'हिंदी', flag: '🇮🇳' },
        { id: 'ta', name: 'தமிழ்', flag: '🇮🇳' },
        { id: 'te', name: 'తెలుగు', flag: '🇮🇳' },
        { id: 'ml', name: 'മലയാളം', flag: '🇮🇳' },
        { id: 'kn', name: 'ಕನ್ನಡ', flag: '🇮🇳' },
    ];
    
    // Show toast notification
    const showToast = (message) => {
        setToastMessage(message);
        setToastVisible(true);
        
        Animated.sequence([
            // Animate in
            Animated.timing(toastAnimation, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            // Hold
            Animated.delay(2000),
            // Animate out
            Animated.timing(toastAnimation, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            })
        ]).start(() => {
            setToastVisible(false);
        });
    };
    
    const handleSave = () => {
        // Update user preferences
        updateUserPreferences(currentUser.id, {
            language: selectedLanguage
        });
        
        showToast('Language updated successfully');
        
        // Navigate back after a delay
        setTimeout(() => {
            navigation.goBack();
        }, 2000);
    };
    
    // Toast animation styles
    const toastTranslateY = toastAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [100, 0],
    });
    
    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-6 pt-16 pb-6 flex-row items-center">
                <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                    <MaterialIcons name="arrow-back" size={24} color="white" />
                </TouchableOpacity>
                <Text className="text-xl text-white font-bold">Language</Text>
            </View>
            
            <View className="p-4">
                <Text className="text-gray-600 mb-4">
                    Select your preferred language for the app interface.
                </Text>
                
                <View className="bg-white rounded-xl shadow-sm overflow-hidden">
                    {languages.map((language, index) => (
                        <TouchableOpacity 
                            key={language.id}
                            className={`p-4 flex-row items-center justify-between ${
                                index < languages.length - 1 ? 'border-b border-gray-100' : ''
                            }`}
                            onPress={() => setSelectedLanguage(language.name)}
                        >
                            <View className="flex-row items-center">
                                <Text className="text-2xl mr-3">{language.flag}</Text>
                                <Text className="text-gray-800 font-medium">{language.name}</Text>
                            </View>
                            
                            {selectedLanguage === language.name && (
                                <MaterialIcons name="check-circle" size={24} color="#A31621" />
                            )}
                        </TouchableOpacity>
                    ))}
                </View>
                
                <TouchableOpacity 
                    className="bg-madder py-4 rounded-xl items-center mt-6"
                    onPress={handleSave}
                >
                    <Text className="text-white font-bold">Save</Text>
                </TouchableOpacity>
                
                <Text className="text-gray-500 text-center mt-4 text-xs">
                    Note: Changing the language will apply to the app interface only. It will not affect the content of products or other data.
                </Text>
            </View>
            
            {/* Toast Notification */}
            {toastVisible && (
                <Animated.View 
                    className="absolute bottom-20 left-5 right-5 bg-green-500 rounded-lg p-4 flex-row items-center"
                    style={{
                        transform: [{ translateY: toastTranslateY }],
                        opacity: toastAnimation,
                        shadowColor: "#000",
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.25,
                        shadowRadius: 3.84,
                        elevation: 5,
                    }}
                >
                    <MaterialIcons name="check-circle" size={24} color="white" />
                    <Text className="text-white font-medium ml-2 flex-1">{toastMessage}</Text>
                </Animated.View>
            )}
        </View>
    );
};

export default LanguageScreen;
