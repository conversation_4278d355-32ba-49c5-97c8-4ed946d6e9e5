import React, { createContext, useState, useContext, useEffect } from 'react';
import {
    getUserProfile,
    updateUserProfile as apiUpdateUserProfile,
    addUserAddress as apiAddUserAddress,
    getUserAddresses,
    update<PERSON><PERSON><PERSON>ddress as apiUpdateUserAddress,
    deleteUserAddress as apiDeleteUserAddress,
    getUserCoins,
    useCoinsForDiscount as apiUseCoinsForDiscount,
    restoreCoins as apiRestoreCoins
} from '../utils/api/userProfileApi';
import { getTotalCoins } from '../utils/addressUtils';
import { API_URL } from '../config/constants';
import { getAuthToken } from '../utils/authStorage';

// Create the context
const UserContext = createContext();

// Create a provider component
export const UserProvider = ({ children }) => {
    // Current logged in user
    const [currentUser, setCurrentUser] = useState(null);
    // User addresses
    const [addresses, setAddresses] = useState([]);
    // User coins/rewards
    const [coins, setCoins] = useState([]);
    // Loading state
    const [loading, setLoading] = useState(true);

    // Fetch user profile from API on component mount
    useEffect(() => {
        const fetchUserProfile = async () => {
            try {
                setLoading(true);

                console.log('Fetching user profile from API');
                try {
                    // Force clear any cached data to ensure fresh fetch
                    console.log('Clearing any cached user data to ensure fresh fetch');

                    // Get user profile
                    const profileResponse = await getUserProfile();
                    console.log('User profile fetched from database:', profileResponse);

                    if (profileResponse && profileResponse.user) {
                        console.log('Setting current user from API response:', profileResponse.user);

                        // Check if user has address data in the profile
                        if (profileResponse.user.address) {
                            console.log('User has address data in profile:', profileResponse.user.address);
                        } else {
                            console.log('User has no address data in profile');
                        }

                        // Check if user has addresses array in the profile
                        if (profileResponse.user.addresses && profileResponse.user.addresses.length > 0) {
                            console.log('User has addresses array in profile:', profileResponse.user.addresses.length);
                            // Set addresses directly from the user profile
                            setAddresses(profileResponse.user.addresses);
                        }

                        // Set the current user
                        setCurrentUser(profileResponse.user);

                        // Get user addresses from the dedicated endpoint
                        try {
                            const addressesResponse = await getUserAddresses();
                            console.log('User addresses fetched from dedicated endpoint:', addressesResponse);

                            // Check if we have addresses from the dedicated endpoint
                            if (addressesResponse && addressesResponse.addresses && addressesResponse.addresses.length > 0) {
                                console.log('Setting addresses from dedicated endpoint');
                                setAddresses(addressesResponse.addresses);
                            }
                            // Check if we have addresses in the user profile
                            else if (profileResponse.user.addresses && profileResponse.user.addresses.length > 0) {
                                console.log('Using addresses from user profile as fallback');
                                setAddresses(profileResponse.user.addresses);
                            }
                            // Check if we have a single address in the user profile
                            else if (profileResponse.user.address) {
                                // Check if the address object has any data
                                const hasAddressData = profileResponse.user.address.doorNo ||
                                                      profileResponse.user.address.streetName ||
                                                      profileResponse.user.address.area ||
                                                      profileResponse.user.address.district ||
                                                      profileResponse.user.address.pincode ||
                                                      profileResponse.user.address.fullAddress;

                                if (hasAddressData) {
                                    // Create an address from the user's address field
                                    console.log('Creating address from user profile field');
                                    const addressFromProfile = {
                                        _id: 'profile-address',
                                        type: 'Home',
                                        doorNo: profileResponse.user.address.doorNo || '',
                                        streetName: profileResponse.user.address.streetName || '',
                                        area: profileResponse.user.address.area || '',
                                        district: profileResponse.user.address.district || '',
                                        pincode: profileResponse.user.address.pincode || '',
                                        fullAddress: profileResponse.user.address.fullAddress ||
                                            `${profileResponse.user.address.doorNo || ''}${profileResponse.user.address.streetName ? `, ${profileResponse.user.address.streetName}` : ''}${profileResponse.user.address.area ? `, ${profileResponse.user.address.area}` : ''}${profileResponse.user.address.district ? `, ${profileResponse.user.address.district}` : ''}${profileResponse.user.address.pincode ? ` - ${profileResponse.user.address.pincode}` : ''}`.replace(/\s+/g, ' ').trim(),
                                        isDefault: true
                                    };
                                    setAddresses([addressFromProfile]);

                                    // Try to add this address to the database
                                    try {
                                        await apiAddUserAddress(addressFromProfile);
                                        console.log('Address created from profile and added to database during initial load');

                                        // Fetch addresses again to get the newly added address with proper ID
                                        const updatedAddressesResponse = await getUserAddresses();
                                        if (updatedAddressesResponse && updatedAddressesResponse.addresses &&
                                            updatedAddressesResponse.addresses.length > 0) {
                                            setAddresses(updatedAddressesResponse.addresses);
                                            console.log('Updated addresses after adding profile address:',
                                                updatedAddressesResponse.addresses);
                                        }
                                    } catch (addError) {
                                        console.error('Error adding address from profile during initial load:', addError);
                                    }
                                } else {
                                    console.log('User address object exists but has no data (initial load)');
                                }
                            }
                        } catch (addressError) {
                            console.error('Error fetching user addresses:', addressError);

                            // Use addresses from user profile as fallback
                            if (profileResponse.user.addresses && profileResponse.user.addresses.length > 0) {
                                console.log('Using addresses from user profile as fallback after error');
                                setAddresses(profileResponse.user.addresses);
                            } else if (profileResponse.user.address) {
                                // Check if the address object has any data
                                const hasAddressData = profileResponse.user.address.doorNo ||
                                                      profileResponse.user.address.streetName ||
                                                      profileResponse.user.address.area ||
                                                      profileResponse.user.address.district ||
                                                      profileResponse.user.address.pincode ||
                                                      profileResponse.user.address.fullAddress;

                                if (hasAddressData) {
                                    // Create an address from the user's address field
                                    console.log('Creating address from user profile field after error');
                                    const addressFromProfile = {
                                        _id: 'profile-address',
                                        type: 'Home',
                                        doorNo: profileResponse.user.address.doorNo || '',
                                        streetName: profileResponse.user.address.streetName || '',
                                        area: profileResponse.user.address.area || '',
                                        district: profileResponse.user.address.district || '',
                                        pincode: profileResponse.user.address.pincode || '',
                                        fullAddress: profileResponse.user.address.fullAddress ||
                                            `${profileResponse.user.address.doorNo || ''}${profileResponse.user.address.streetName ? `, ${profileResponse.user.address.streetName}` : ''}${profileResponse.user.address.area ? `, ${profileResponse.user.address.area}` : ''}${profileResponse.user.address.district ? `, ${profileResponse.user.address.district}` : ''}${profileResponse.user.address.pincode ? ` - ${profileResponse.user.address.pincode}` : ''}`.replace(/\s+/g, ' ').trim(),
                                        isDefault: true
                                    };
                                    setAddresses([addressFromProfile]);

                                    // Try to add this address to the database
                                    try {
                                        await apiAddUserAddress(addressFromProfile);
                                        console.log('Address created from profile and added to database after error');

                                        // Try to fetch addresses again to get the newly added address with proper ID
                                        try {
                                            const updatedAddressesResponse = await getUserAddresses();
                                            if (updatedAddressesResponse && updatedAddressesResponse.addresses &&
                                                updatedAddressesResponse.addresses.length > 0) {
                                                setAddresses(updatedAddressesResponse.addresses);
                                                console.log('Updated addresses after adding profile address (error path):',
                                                    updatedAddressesResponse.addresses);
                                            }
                                        } catch (fetchError) {
                                            console.error('Error fetching updated addresses after adding profile address:', fetchError);
                                        }
                                    } catch (addError) {
                                        console.error('Error adding address from profile after error:', addError);
                                    }
                                } else {
                                    console.log('User address object exists but has no data (error path)');
                                }
                            }
                            // Don't reset addresses on error to prevent data loss
                        }

                        // Get user coins
                        try {
                            const coinsResponse = await getUserCoins();
                            console.log('User coins fetched:', coinsResponse);

                            if (coinsResponse && coinsResponse.coins) {
                                setCoins(coinsResponse.coins);
                            }
                        } catch (coinsError) {
                            console.error('Error fetching user coins:', coinsError);
                            // Don't reset coins on error to prevent data loss
                        }
                    } else if (profileResponse && (profileResponse.message === 'User profile not found' || profileResponse.message === 'User not authenticated')) {
                        // This is a 404 error or auth error - try to get the user data from local storage as a fallback
                        try {
                            const { getUserData } = require('../utils/authStorage');
                            const localUserData = await getUserData();

                            if (localUserData) {
                                console.log('Using local user data as fallback:', localUserData);
                                // Use the local user data as a temporary solution
                                setCurrentUser(localUserData);
                            } else {
                                console.log('No local user data available');
                                // Only set to null if we don't have any data
                                if (!currentUser) {
                                    setCurrentUser(null);
                                }
                            }
                        } catch (localDataError) {
                            console.error('Error getting local user data:', localDataError);
                            // Only set to null if we don't have any data
                            if (!currentUser) {
                                setCurrentUser(null);
                            }
                        }
                    } else {
                        console.log('No user profile returned from API');
                        // Try to get local data before setting to null
                        try {
                            const { getUserData } = require('../utils/authStorage');
                            const localUserData = await getUserData();

                            if (localUserData) {
                                console.log('Using local user data as fallback after API returned no data:', localUserData);
                                setCurrentUser(localUserData);
                            } else if (!currentUser) {
                                // Only set to null if we don't have any data
                                setCurrentUser(null);
                            }
                        } catch (localDataError) {
                            console.error('Error getting local user data after API returned no data:', localDataError);
                            // Only set to null if we don't have any data
                            if (!currentUser) {
                                setCurrentUser(null);
                            }
                        }
                    }
                } catch (apiError) {
                    console.error('Error fetching user profile from API:', apiError);

                    // Try to get local data before setting to null
                    try {
                        const { getUserData } = require('../utils/authStorage');
                        const localUserData = await getUserData();

                        if (localUserData) {
                            console.log('Using local user data as fallback after API error:', localUserData);
                            setCurrentUser(localUserData);
                        } else if (!currentUser) {
                            // Only set to null if we don't have any data
                            setCurrentUser(null);
                        }
                    } catch (localDataError) {
                        console.error('Error getting local user data after API error:', localDataError);
                        // Only set to null if we don't have any data
                        if (!currentUser) {
                            setCurrentUser(null);
                        }
                    }
                }
            } catch (error) {
                console.error('Error in user profile fetch process:', error);
                // Don't reset user data on error to prevent data loss
            } finally {
                setLoading(false);
            }
        };

        fetchUserProfile();
    }, []); // Empty dependency array to run only on mount

    // Function to update user profile
    const updateUserProfile = async (updatedData) => {
        console.log('UserContext - Updating profile with data:', updatedData);

        try {
            // Ensure address has fullAddress if other address fields are provided
            if (updatedData.address) {
                const { doorNo, streetName, area, district, pincode } = updatedData.address;

                // If all address fields are provided but fullAddress is missing, create it
                if (doorNo && streetName && area && district && pincode && !updatedData.address.fullAddress) {
                    updatedData.address.fullAddress = `${doorNo}, ${streetName}, ${area}, ${district}, ${pincode}`;
                }
            }

            // Call API to update user profile
            const response = await apiUpdateUserProfile(updatedData);
            console.log('User profile updated:', response);

            if (response && response.user) {
                // Update current user with the response from the API
                setCurrentUser(response.user);

                // Refresh user data to get the latest info
                await refreshUserData();

                return response.user;
            } else if (response && response.message) {
                // If we have a success message but no user object, refresh user data
                console.log('Profile update successful but no user returned. Refreshing user data...');
                await refreshUserData();
                return currentUser;
            }
        } catch (error) {
            console.error('Error updating user profile:', error);

            // Try to refresh user data even if update failed
            try {
                console.log('Attempting to refresh user data after update error');
                await refreshUserData();
            } catch (refreshError) {
                console.error('Error refreshing user data after update error:', refreshError);
            }

            throw error;
        }
    };

    // Function to add a new address
    const addUserAddress = async (newAddress) => {
        try {
            console.log('Adding new address with 5-field structure');

            if (!currentUser) {
                console.error('Cannot add address: No user is logged in');
                throw new Error('User not authenticated');
            }

            // Always allow duplicate addresses
            newAddress.allowDuplicate = true;

            // Call API to add address
            const response = await apiAddUserAddress(newAddress);
            console.log('Address added:', response);

            // Always add the address to the state, even if it's a duplicate
            if (response && response.address) {
                // Update addresses state with the new address from the API
                setAddresses(prevAddresses => {
                    // If this is set as default, update all other addresses
                    const updatedAddresses = newAddress.isDefault
                        ? prevAddresses.map(addr => ({ ...addr, isDefault: false }))
                        : [...prevAddresses];

                    // Always add the new address to the state
                    console.log('Adding address to state, allowing duplicates');
                    return [...updatedAddresses, response.address];
                });

                return response.address._id;
            }
        } catch (error) {
            console.error('Error adding address:', error);
            throw error;
        }
    };

    // Function to update an address
    const updateUserAddress = async (addressId, updatedAddress) => {
        try {
            console.log('Updating address with 5-field structure:', addressId);

            if (!currentUser) {
                console.error('Cannot update address: No user is logged in');
                throw new Error('User not authenticated');
            }

            if (!addressId) {
                console.error('Cannot update address: No address ID provided');
                throw new Error('Address ID is required');
            }

            // Call API to update address
            const response = await apiUpdateUserAddress(addressId, updatedAddress);
            console.log('Address updated:', response);

            if (response && response.address) {
                // Update addresses state with the updated address from the API
                setAddresses(prevAddresses => {
                    // If this is set as default, update all other addresses
                    return prevAddresses.map(addr => {
                        // Match by either _id or id
                        if (addr._id === addressId || addr.id === addressId) {
                            return response.address;
                        } else if (updatedAddress.isDefault) {
                            return { ...addr, isDefault: false };
                        } else {
                            return addr;
                        }
                    });
                });

                return response.address;
            }
        } catch (error) {
            console.error(`Error updating address ${addressId}:`, error);
            throw error;
        }
    };

    // Function to delete an address
    const deleteUserAddress = async (addressId) => {
        try {
            console.log('Deleting address:', addressId);

            // Call API to delete address
            const response = await apiDeleteUserAddress(addressId);
            console.log('Address deleted:', response);

            // Update addresses state by removing the deleted address
            setAddresses(prevAddresses =>
                prevAddresses.filter(addr => addr._id !== addressId)
            );
        } catch (error) {
            console.error(`Error deleting address ${addressId}:`, error);
            throw error;
        }
    };

    // Function to get user coins
    const getUserCoinsData = async () => {
        try {
            console.log('Fetching user coins');

            // Call API to get user coins with a fixed timestamp to reduce unnecessary calls
            // Only update once per minute at most
            const timestamp = Math.floor(new Date().getTime() / 60000) * 60000;
            const response = await getUserCoins(timestamp);
            console.log('User coins fetched:', response);

            if (response) {
                // Set coins data
                if (response.coins !== undefined) {
                    setCoins(response.coins);
                }

                // Only update currentUser if the coin values have actually changed
                if (currentUser) {
                    const newTotalCoins = response.totalCoins !== undefined ? response.totalCoins : (currentUser?.coins || 0);
                    const newAvailableCoins = response.activeCoins !== undefined ? response.activeCoins : (currentUser?.coins || 0);
                    const newUsedCoins = response.usedCoins !== undefined ? response.usedCoins : (currentUser?.usedCoins || 0);
                    const newHasUsedCoins = response.hasUsedCoins !== undefined ? response.hasUsedCoins : (newUsedCoins > 0);

                    // Check if any values have changed before updating state
                    const hasChanged =
                        newTotalCoins !== currentUser.totalCoins ||
                        newAvailableCoins !== currentUser.availableCoins ||
                        newUsedCoins !== currentUser.usedCoins ||
                        newHasUsedCoins !== currentUser.hasUsedCoins;

                    if (hasChanged) {
                        console.log('Updating currentUser with new coin data:', {
                            totalCoins: newTotalCoins,
                            availableCoins: newAvailableCoins,
                            usedCoins: newUsedCoins,
                            hasUsedCoins: newHasUsedCoins
                        });

                        setCurrentUser(prev => ({
                            ...prev,
                            coins: newTotalCoins, // Update the simple coin counter too (can be negative)
                            totalCoins: newTotalCoins,
                            activeCoins: newAvailableCoins,
                            availableCoins: response.availableCoins !== undefined ? response.availableCoins : newAvailableCoins,
                            usedCoins: newUsedCoins,
                            hasUsedCoins: newHasUsedCoins,
                            hasNegativeBalance: response.hasNegativeBalance || false,
                            negativeAmount: response.negativeAmount || 0,
                            usedCoupons: response.usedCoupons || prev.usedCoupons || []
                        }));
                    } else {
                        console.log('Coin data unchanged, skipping currentUser update');
                    }
                }

                return response;
            }

            return { coins: 0 };
        } catch (error) {
            console.error('Error fetching user coins:', error);
            return { coins: 0 };
        }
    };

    // Function to use coins for discount
    const useCoinsForDiscount = async (coinsToUse, orderId = null) => {
        try {
            console.log('Attempting to use coins for discount, amount:', coinsToUse);

            // Get the latest coin data - but only once
            const coinData = await getUserCoins();
            console.log('Coin data before using coins:', coinData);

            const availableCoins = coinData.availableCoins !== undefined
                ? coinData.availableCoins
                : (coinData.totalCoins !== undefined ? coinData.totalCoins : getTotalCoins(coins));

            console.log('Available coins:', availableCoins, 'Coins to use:', coinsToUse);

            if (availableCoins < coinsToUse) {
                return {
                    success: false,
                    error: `Not enough coins. You have ${availableCoins} available coins but tried to use ${coinsToUse}.`
                };
            }

            // Use the API function from userProfileApi.js
            const result = await apiUseCoinsForDiscount(coinsToUse, orderId);
            console.log('Use coins result:', result);

            if (result.success) {
                // Update coins in state by fetching fresh data
                await getUserCoinsData();
            }

            return result;
        } catch (error) {
            console.error('Error using coins for discount:', error);

            // Try to refresh coin data even if there was an error
            try {
                await getUserCoinsData();
            } catch (refreshError) {
                console.error('Error refreshing coin data after discount error:', refreshError);
            }

            return {
                success: false,
                error: error.message || 'Failed to use coins'
            };
        }
    };

    // Function to restore previously used coins
    const restoreCoins = async () => {
        try {
            console.log('UserContext: Attempting to restore coins');

            // Use the API function from userProfileApi.js
            const result = await apiRestoreCoins();
            console.log('UserContext: Restore coins result:', result);

            if (result.success) {
                // Only update if coins were actually restored
                if (result.restoredCoins > 0) {
                    // Update coins in state by fetching fresh data
                    await getUserCoinsData();
                } else {
                    console.log('No coins were restored, skipping data refresh');
                }

                return {
                    success: true,
                    restoredCoins: result.restoredCoins || 0,
                    availableCoins: result.availableCoins,
                    message: result.message || `Restored ${result.restoredCoins || 0} coins successfully!`
                };
            }

            // Try to refresh coin data even if the API call wasn't successful
            try {
                await getUserCoinsData();
            } catch (refreshError) {
                console.error('Error refreshing coin data after restore error:', refreshError);
            }

            return {
                success: false,
                error: result.error || 'Failed to restore coins'
            };
        } catch (error) {
            console.error('Error restoring coins in UserContext:', error);

            // Try to refresh coin data even if there was an error
            try {
                await getUserCoinsData();
            } catch (refreshError) {
                console.error('Error refreshing coin data after restore exception:', refreshError);
            }

            return {
                success: false,
                error: error.message || 'Failed to restore coins'
            };
        }
    };

    // Function to get default address
    const getDefaultAddress = () => {
        if (!addresses || addresses.length === 0) {
            return null;
        }
        return addresses.find(addr => addr.isDefault) || addresses[0];
    };

    // Function to get primary address
    const getPrimaryAddress = () => {
        if (!addresses || addresses.length === 0) {
            return null;
        }
        return addresses.find(addr => addr.isPrimary) || getDefaultAddress();
    };

    // Function to refresh user data
    const refreshUserData = async () => {
        try {
            setLoading(true);
            console.log('Refreshing user data from database...');

            // Get user profile directly from the database
            try {
                console.log('Fetching fresh user profile from database...');
                const profileResponse = await getUserProfile();
                console.log('Profile response during refresh (from database):', profileResponse);

                // Check if we have a valid profile response
                // The API might return the user data directly without wrapping it in a 'user' property
                if (profileResponse && (profileResponse.user || profileResponse._id)) {
                    console.log('Valid user profile received during refresh');

                    // If the response has a 'user' property, use that, otherwise use the response itself
                    const userData = profileResponse.user || profileResponse;
                    setCurrentUser(userData);

                    // Check if user has addresses in the profile
                    if (userData.addresses && userData.addresses.length > 0) {
                        console.log('User has addresses in profile during refresh:', userData.addresses.length);
                        // Set addresses directly from the user profile
                        setAddresses(userData.addresses);
                    }

                    // Only try to get addresses and coins if we have a valid user
                    try {
                        // Get user addresses from dedicated endpoint
                        const addressesResponse = await getUserAddresses();
                        console.log('User addresses fetched during refresh:', addressesResponse);

                        // Check if we have addresses from the dedicated endpoint
                        if (addressesResponse && addressesResponse.addresses && addressesResponse.addresses.length > 0) {
                            console.log('Setting addresses from dedicated endpoint during refresh');
                            setAddresses(addressesResponse.addresses);
                        }
                        // Check if we have addresses in the user profile
                        else if (userData.addresses && userData.addresses.length > 0) {
                            console.log('Using addresses from user profile as fallback during refresh');
                            setAddresses(userData.addresses);
                        }
                        // Check if we have a single address in the user profile
                        else if (userData.address) {
                            // Check if the address object has any data
                            const hasAddressData = userData.address.doorNo ||
                                                  userData.address.streetName ||
                                                  userData.address.area ||
                                                  userData.address.district ||
                                                  userData.address.pincode ||
                                                  userData.address.fullAddress;

                            if (hasAddressData) {
                                // If no addresses array but user has an address in profile, create an address object
                                console.log('Creating address from user profile field during refresh');
                                const addressFromProfile = {
                                    _id: 'profile-address',
                                    type: 'Home',
                                    doorNo: userData.address.doorNo || '',
                                    streetName: userData.address.streetName || '',
                                    area: userData.address.area || '',
                                    district: userData.address.district || '',
                                    pincode: userData.address.pincode || '',
                                    fullAddress: userData.address.fullAddress ||
                                        `${userData.address.doorNo || ''}${userData.address.streetName ? `, ${userData.address.streetName}` : ''}${userData.address.area ? `, ${userData.address.area}` : ''}${userData.address.district ? `, ${userData.address.district}` : ''}${userData.address.pincode ? ` - ${userData.address.pincode}` : ''}`.replace(/\s+/g, ' ').trim(),
                                    isDefault: true
                                };

                                // Set the address in the state
                                setAddresses([addressFromProfile]);
                                console.log('Created address from user profile during refresh:', addressFromProfile);

                                // Try to add this address to the database
                                try {
                                    await apiAddUserAddress(addressFromProfile);
                                    console.log('Address created from profile and added to database during refresh');

                                    // Fetch addresses again to get the newly added address with proper ID
                                    const updatedAddressesResponse = await getUserAddresses();
                                    if (updatedAddressesResponse && updatedAddressesResponse.addresses &&
                                        updatedAddressesResponse.addresses.length > 0) {
                                        setAddresses(updatedAddressesResponse.addresses);
                                        console.log('Updated addresses after adding profile address:', updatedAddressesResponse.addresses);
                                    }
                                } catch (addError) {
                                    console.error('Error adding address from profile during refresh:', addError);
                                }
                            } else {
                                console.log('User address object exists but has no data');
                            }
                        }
                    } catch (addressError) {
                        console.error('Error fetching user addresses during refresh:', addressError);

                        // If error fetching addresses but user has addresses in profile, use those
                        if (profileResponse.user.addresses && profileResponse.user.addresses.length > 0) {
                            console.log('Using addresses from user profile as fallback after error during refresh');
                            setAddresses(profileResponse.user.addresses);
                        }
                        // If error fetching addresses but user has an address in profile, use that
                        else if (profileResponse.user.address) {
                            // Check if the address object has any data
                            const hasAddressData = profileResponse.user.address.doorNo ||
                                                  profileResponse.user.address.streetName ||
                                                  profileResponse.user.address.area ||
                                                  profileResponse.user.address.district ||
                                                  profileResponse.user.address.pincode ||
                                                  profileResponse.user.address.fullAddress;

                            if (hasAddressData) {
                                // Create an address object from the user's address field
                                console.log('Creating address from user profile field after error during refresh');
                                const addressFromProfile = {
                                    _id: 'profile-address',
                                    type: 'Home',
                                    doorNo: profileResponse.user.address.doorNo || '',
                                    streetName: profileResponse.user.address.streetName || '',
                                    area: profileResponse.user.address.area || '',
                                    district: profileResponse.user.address.district || '',
                                    pincode: profileResponse.user.address.pincode || '',
                                    fullAddress: profileResponse.user.address.fullAddress ||
                                        `${profileResponse.user.address.doorNo || ''}${profileResponse.user.address.streetName ? `, ${profileResponse.user.address.streetName}` : ''}${profileResponse.user.address.area ? `, ${profileResponse.user.address.area}` : ''}${profileResponse.user.address.district ? `, ${profileResponse.user.address.district}` : ''}${profileResponse.user.address.pincode ? ` - ${profileResponse.user.address.pincode}` : ''}`.replace(/\s+/g, ' ').trim(),
                                    isDefault: true
                                };

                                setAddresses([addressFromProfile]);
                                console.log('Using address from user profile as fallback during refresh:', addressFromProfile);

                                // Try to add this address to the database
                                try {
                                    await apiAddUserAddress(addressFromProfile);
                                    console.log('Address created from profile and added to database during refresh after error');

                                    // Try to fetch addresses again to get the newly added address with proper ID
                                    try {
                                        const updatedAddressesResponse = await getUserAddresses();
                                        if (updatedAddressesResponse && updatedAddressesResponse.addresses &&
                                            updatedAddressesResponse.addresses.length > 0) {
                                            setAddresses(updatedAddressesResponse.addresses);
                                            console.log('Updated addresses after adding profile address (error path):',
                                                updatedAddressesResponse.addresses);
                                        }
                                    } catch (fetchError) {
                                        console.error('Error fetching updated addresses after adding profile address:', fetchError);
                                    }
                                } catch (addError) {
                                    console.error('Error adding address from profile during refresh after error:', addError);
                                }
                            } else {
                                console.log('User address object exists but has no data (error path)');
                            }
                        }
                        // Don't reset addresses on error to maintain existing data
                    }

                    try {
                        // Get user coins
                        const coinsResponse = await getUserCoins();
                        if (coinsResponse && coinsResponse.coins) {
                            setCoins(coinsResponse.coins);
                        }
                    } catch (coinsError) {
                        console.error('Error fetching user coins during refresh:', coinsError);
                        // Don't reset coins on error to maintain existing data
                    }

                    console.log('User data refresh completed successfully');
                    return true;
                } else if (profileResponse && (profileResponse.message === 'User profile not found' || profileResponse.message === 'User not authenticated')) {
                    // This is a 404 error or auth error - the user is authenticated but doesn't have a profile yet
                    console.log('User profile not found or not authenticated during refresh');

                    // Try to get the user data from local storage as a fallback
                    try {
                        const { getUserData, isAuthenticated } = require('../utils/authStorage');

                        // Check if we're still authenticated
                        const isAuth = await isAuthenticated();
                        if (!isAuth) {
                            console.log('User is no longer authenticated');
                            // Don't reset user data to prevent data loss during temporary auth issues
                            return false;
                        }

                        const localUserData = await getUserData();
                        if (localUserData) {
                            console.log('Using local user data as fallback during refresh:', localUserData);
                            // Use the local user data as a temporary solution
                            setCurrentUser(localUserData);
                            return true;
                        } else {
                            console.log('No local user data available during refresh');
                            // Keep the current user data if we have it
                            return false;
                        }
                    } catch (localDataError) {
                        console.error('Error getting local user data during refresh:', localDataError);
                        // Keep the current user data if we have it
                        return false;
                    }
                } else {
                    console.log('No valid user profile returned during refresh');

                    // Check if profileResponse itself is a valid user object (has _id)
                    if (profileResponse && profileResponse._id) {
                        console.log('Profile response appears to be a direct user object, using it');
                        setCurrentUser(profileResponse);

                        // Check if user has addresses in the profile
                        if (profileResponse.addresses && profileResponse.addresses.length > 0) {
                            console.log('User has addresses in direct profile response:', profileResponse.addresses.length);
                            setAddresses(profileResponse.addresses);
                        } else if (profileResponse.address) {
                            // Check if the address object has any data
                            const hasAddressData = profileResponse.address.doorNo ||
                                                  profileResponse.address.streetName ||
                                                  profileResponse.address.area ||
                                                  profileResponse.address.district ||
                                                  profileResponse.address.pincode ||
                                                  profileResponse.address.fullAddress;

                            if (hasAddressData) {
                                console.log('Creating address from direct profile response');
                                const addressFromProfile = {
                                    _id: 'profile-address',
                                    type: 'Home',
                                    doorNo: profileResponse.address.doorNo || '',
                                    streetName: profileResponse.address.streetName || '',
                                    area: profileResponse.address.area || '',
                                    district: profileResponse.address.district || '',
                                    pincode: profileResponse.address.pincode || '',
                                    fullAddress: profileResponse.address.fullAddress ||
                                        `${profileResponse.address.doorNo || ''}${profileResponse.address.streetName ? `, ${profileResponse.address.streetName}` : ''}${profileResponse.address.area ? `, ${profileResponse.address.area}` : ''}${profileResponse.address.district ? `, ${profileResponse.address.district}` : ''}${profileResponse.address.pincode ? ` - ${profileResponse.address.pincode}` : ''}`.replace(/\s+/g, ' ').trim(),
                                    isDefault: true
                                };
                                setAddresses([addressFromProfile]);
                            }
                        }
                        return true;
                    }

                    // Try to get local data before giving up
                    try {
                        const { getUserData } = require('../utils/authStorage');
                        const localUserData = await getUserData();

                        if (localUserData) {
                            console.log('Using local user data as fallback after API returned no data during refresh:', localUserData);

                            // Keep the current user data but update with local user data
                            if (currentUser) {
                                // Preserve the address data from the current user
                                setCurrentUser({
                                    ...localUserData,
                                    address: currentUser.address || {},
                                    addresses: currentUser.addresses || []
                                });

                                // If we have address data in the current user, make sure it's also in the addresses state
                                if (currentUser.address) {
                                    const hasAddressData = currentUser.address.doorNo ||
                                                          currentUser.address.streetName ||
                                                          currentUser.address.area ||
                                                          currentUser.address.district ||
                                                          currentUser.address.pincode ||
                                                          currentUser.address.fullAddress;

                                    if (hasAddressData && (!addresses || addresses.length === 0)) {
                                        console.log('Creating address from current user data during fallback');
                                        const addressFromProfile = {
                                            _id: 'profile-address',
                                            type: 'Home',
                                            doorNo: currentUser.address.doorNo || '',
                                            streetName: currentUser.address.streetName || '',
                                            area: currentUser.address.area || '',
                                            district: currentUser.address.district || '',
                                            pincode: currentUser.address.pincode || '',
                                            fullAddress: currentUser.address.fullAddress ||
                                                `${currentUser.address.doorNo || ''}${currentUser.address.streetName ? `, ${currentUser.address.streetName}` : ''}${currentUser.address.area ? `, ${currentUser.address.area}` : ''}${currentUser.address.district ? `, ${currentUser.address.district}` : ''}${currentUser.address.pincode ? ` - ${currentUser.address.pincode}` : ''}`.replace(/\s+/g, ' ').trim(),
                                            isDefault: true
                                        };
                                        setAddresses([addressFromProfile]);
                                    }
                                }
                            } else {
                                // Just set the local user data if we don't have current user data
                                setCurrentUser(localUserData);
                            }
                            return true;
                        }
                    } catch (localDataError) {
                        console.error('Error getting local user data after API returned no data during refresh:', localDataError);
                    }

                    // Don't reset user data if the API returns no data
                    // This prevents losing local state if there's a temporary API issue
                    return false;
                }
            } catch (profileError) {
                console.error('Error fetching user profile during refresh:', profileError);

                // Try to get local data before giving up
                try {
                    const { getUserData } = require('../utils/authStorage');
                    const localUserData = await getUserData();

                    if (localUserData) {
                        console.log('Using local user data as fallback after API error during refresh:', localUserData);
                        setCurrentUser(localUserData);
                        return true;
                    }
                } catch (localDataError) {
                    console.error('Error getting local user data after API error during refresh:', localDataError);
                }

                // Don't reset user data on error to maintain existing data
                return false;
            }
        } catch (error) {
            console.error('Error in refresh user data process:', error);
            return false;
        } finally {
            setLoading(false);
        }
    };

    // Value to be provided to consumers
    const value = {
        currentUser,
        setCurrentUser,
        addresses,
        coins,
        loading,
        updateUserProfile,
        addUserAddress,
        updateUserAddress,
        deleteUserAddress,
        getUserCoinsData,
        useCoinsForDiscount,
        restoreCoins,
        getDefaultAddress,
        getPrimaryAddress,
        refreshUserData
    };

    return (
        <UserContext.Provider value={value}>
            {children}
        </UserContext.Provider>
    );
};

// Custom hook to use the user context
export const useUser = () => {
    const context = useContext(UserContext);
    if (context === undefined) {
        throw new Error('useUser must be used within a UserProvider');
    }
    return context;
};
