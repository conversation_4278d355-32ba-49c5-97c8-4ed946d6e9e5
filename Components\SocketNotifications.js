import React, { useEffect } from 'react';
import { useSocket } from '../context/SocketContext';
import Toast from 'react-native-toast-message';

/**
 * Component to handle real-time notifications via Socket.IO
 * This component should be included once in the app to listen for notifications
 */
const SocketNotifications = () => {
    const { connected, onNotification, onOrderStatusUpdate, onDeliveryUpdate, onOrderUpdate } = useSocket();

    useEffect(() => {
        if (!connected) return;

        console.log('Setting up socket notification listeners');

        // Listen for general notifications
        const unsubscribeNotifications = onNotification((notification) => {
            console.log('Received notification:', notification);

            // Show toast notification
            if (notification.message) {
                Toast.show({
                    type: notification.type || 'info',
                    text1: notification.title || 'Notification',
                    text2: notification.message,
                    visibilityTime: 4000,
                    autoHide: true,
                    topOffset: 60,
                });
            }
        });

        // Listen for order status updates and show notifications
        const unsubscribeOrderStatus = onOrderStatusUpdate((data) => {
            console.log('Received order status update notification:', data);

            if (data.orderId && data.status) {
                let title = 'Order Update';
                let message = '';
                let type = 'info';

                switch (data.status) {
                    case 'CONFIRMED':
                        title = 'Order Confirmed';
                        message = `Your order #${data.orderId.slice(-6)} has been confirmed!`;
                        type = 'success';
                        break;
                    case 'PREPARING':
                        title = 'Order Being Prepared';
                        message = `Your order #${data.orderId.slice(-6)} is being prepared.`;
                        type = 'info';
                        break;
                    case 'OUT_FOR_DELIVERY':
                        title = '🚚 Out for Delivery';
                        message = `Your order #${data.orderId.slice(-6)} is out for delivery!`;
                        type = 'info';
                        break;
                    case 'DELIVERED':
                        title = '✅ Order Delivered';
                        message = `Your order #${data.orderId.slice(-6)} has been delivered successfully!`;
                        type = 'success';
                        break;
                    case 'CANCELLED':
                        title = 'Order Cancelled';
                        message = `Your order #${data.orderId.slice(-6)} has been cancelled.`;
                        type = 'error';
                        break;
                    default:
                        message = `Your order #${data.orderId.slice(-6)} status: ${data.status}`;
                        break;
                }

                if (message) {
                    Toast.show({
                        type: type,
                        text1: title,
                        text2: message,
                        visibilityTime: 5000,
                        autoHide: true,
                        topOffset: 60,
                    });
                }
            }
        });

        // Listen for delivery updates
        const unsubscribeDelivery = onDeliveryUpdate((data) => {
            console.log('Received delivery update notification:', data);

            if (data.message) {
                Toast.show({
                    type: data.type || 'info',
                    text1: 'Delivery Update',
                    text2: data.message,
                    visibilityTime: 4000,
                    autoHide: true,
                    topOffset: 60,
                });
            }
        });

        // Listen for new order notifications (for admin and delivery partners)
        const unsubscribeNewOrder = onOrderUpdate ? onOrderUpdate((data) => {
            console.log('Received new order notification:', data);

            // Handle direct order object (from new_order event)
            if (data.orderNumber || data._id) {
                Toast.show({
                    type: 'success',
                    text1: '🆕 New Order Available',
                    text2: `Order #${data.orderNumber || data._id?.slice(-6)} is ready for delivery`,
                    visibilityTime: 6000,
                    autoHide: true,
                    topOffset: 60,
                });
            }
            // Handle wrapped order data (from order_update event)
            else if (data.type === 'new' && data.order) {
                Toast.show({
                    type: 'success',
                    text1: '🆕 New Order Available',
                    text2: `Order #${data.order.orderNumber || data.order._id?.slice(-6)} is ready for delivery`,
                    visibilityTime: 6000,
                    autoHide: true,
                    topOffset: 60,
                });
            }
        }) : () => {};

        // Cleanup listeners
        return () => {
            unsubscribeNotifications();
            unsubscribeOrderStatus();
            unsubscribeDelivery();
            if (unsubscribeNewOrder) unsubscribeNewOrder();
        };
    }, [connected, onNotification, onOrderStatusUpdate, onDeliveryUpdate, onOrderUpdate]);

    // This component doesn't render anything visible
    return null;
};

export default SocketNotifications;
