import axios from 'axios';
import { API_URL, USER_TYPES } from '../../config/constants';
import { getAuthToken, getUserData } from '../authStorage';

// Get all orders
export const getAllOrders = async () => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('OrderAPI: User type:', userType);

        // Only admins should be able to fetch all orders
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch all orders');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.get(`${API_URL}/admin/orders`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching orders:', error);
        throw error;
    }
};

// Get order by ID
export const getOrderById = async (orderId) => {
    try {
        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/admin/orders/${orderId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error fetching order ${orderId}:`, error);
        throw error;
    }
};

// Get orders by status
export const getOrdersByStatus = async (status) => {
    try {
        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/admin/orders/status/${status}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error fetching orders with status ${status}:`, error);
        throw error;
    }
};

// Get orders by user ID with improved error handling and fallback
export const getOrdersByUserId = async (userId) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('OrderAPI: User type for getOrdersByUserId:', userType);

        // Only admins should be able to fetch orders by user ID through admin API
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch orders by user ID through admin API');
            throw new Error('Unauthorized: Admin access required');
        }

        try {
            // Try to fetch from admin/orders/user/:userId endpoint
            const response = await axios.get(`${API_URL}/admin/orders/user/${userId}`, {
                headers: {
                    Authorization: `Bearer ${token}`
                },
                timeout: 10000 // 10 second timeout
            });

            // Check if response has orders property
            if (response.data && response.data.orders) {
                return response.data;
            }
            // If response is an array directly
            else if (response.data && Array.isArray(response.data)) {
                return { orders: response.data };
            }
            // If response has no orders property but has data
            else if (response.data) {
                return { orders: [response.data] };
            }
            // No orders found
            else {
                return { orders: [] };
            }
        } catch (directError) {
            console.log(`Direct orders fetch failed, trying to find orders in all orders: ${directError.message}`);

            // If direct fetch fails, try to get all orders and filter by user ID
            try {
                const allOrdersResponse = await getAllOrders();

                if (allOrdersResponse && allOrdersResponse.orders && allOrdersResponse.orders.length > 0) {
                    // Filter orders by user ID
                    const userOrders = allOrdersResponse.orders.filter(order =>
                        (order.userId === userId ||
                         (order.userId && order.userId._id === userId) ||
                         (order.userId && order.userId.id === userId))
                    );

                    console.log(`Found ${userOrders.length} orders for user in all orders`);
                    return { orders: userOrders };
                } else if (allOrdersResponse && Array.isArray(allOrdersResponse) && allOrdersResponse.length > 0) {
                    // If allOrdersResponse is an array directly
                    const userOrders = allOrdersResponse.filter(order =>
                        (order.userId === userId ||
                         (order.userId && order.userId._id === userId) ||
                         (order.userId && order.userId.id === userId))
                    );

                    console.log(`Found ${userOrders.length} orders for user in all orders array`);
                    return { orders: userOrders };
                }
            } catch (fallbackError) {
                console.error('Fallback orders fetch failed:', fallbackError);
            }

            // If we still can't find any orders, return empty array
            return { orders: [] };
        }
    } catch (error) {
        console.error(`Error fetching orders for user ${userId}:`, error);
        // Return empty array instead of throwing to prevent app crashes
        return { orders: [] };
    }
};

// Get orders by delivery partner ID with optional status filter
export const getOrdersByDeliveryPartnerId = async (partnerId, status = null) => {
    try {
        const token = await getAuthToken();

        // Build the URL with optional status parameter
        let url = `${API_URL}/admin/orders/delivery-partner/${partnerId}`;

        if (status) {
            // Map frontend status names to backend status names if needed
            const statusMap = {
                'pending': 'PLACED',
                'in-transit': 'OUT_FOR_DELIVERY',
                'delivered': 'DELIVERED',
                'cancelled': 'CANCELLED'
            };

            // Get the actual status to filter by
            const filterStatus = statusMap[status] || status.toUpperCase();

            url += `?status=${filterStatus}`;
            console.log(`Fetching orders for delivery partner ${partnerId} with status filter: ${filterStatus} (original: ${status})`);
        } else {
            console.log(`Fetching all orders for delivery partner ${partnerId}`);
        }

        console.log('API URL:', url);

        const response = await axios.get(url, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error fetching orders for delivery partner ${partnerId}:`, error);
        throw error;
    }
};

// Update order status
export const updateOrderStatus = async (orderId, status, deliveryPartnerId = null) => {
    try {
        const token = await getAuthToken();
        const data = { status };

        if (deliveryPartnerId) {
            data.deliveryPartnerId = deliveryPartnerId;
        }

        const response = await axios.patch(`${API_URL}/admin/orders/${orderId}/status`, data, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error updating order ${orderId} status:`, error);
        throw error;
    }
};
