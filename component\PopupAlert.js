import { View, Text, Modal, TouchableOpacity, Animated } from 'react-native';
import React, { useEffect, useRef } from 'react';
import { MaterialIcons } from "@expo/vector-icons";

const PopupAlert = ({ visible, message, type = 'success', onClose }) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        if (visible) {
            Animated.sequence([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.delay(2000),
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                })
            ]).start(() => onClose());
        }
    }, [visible]);

    if (!visible) return null;

    const getAlertStyle = () => {
        switch (type) {
            case 'success':
                return {
                    icon: 'check-circle',
                    color: '#22C55E',
                    borderColor: 'border-green-500'
                };
            case 'error':
                return {
                    icon: 'error-outline',
                    color: '#A31621',
                    borderColor: 'border-madder'
                };
            default:
                return {
                    icon: 'info',
                    color: '#3B82F6',
                    borderColor: 'border-blue-500'
                };
        }
    };

    const alertStyle = getAlertStyle();

    return (
        <Modal
            transparent
            visible={visible}
            animationType="none"
            onRequestClose={onClose}
        >
            <View className="flex-1 items-center" style={{ justifyContent: 'flex-end', paddingBottom: 20 }}>
                <Animated.View
                    className={`bg-white rounded-lg shadow-md py-3 px-4 mx-6 ${type === 'success' ? 'bg-green-50' : type === 'error' ? 'bg-red-50' : 'bg-blue-50'}`}
                    style={{
                        opacity: fadeAnim,
                        transform: [{
                            translateY: fadeAnim.interpolate({
                                inputRange: [0, 1],
                                outputRange: [20, 0]
                            })
                        }]
                    }}
                >
                    <View className="flex-row items-center">
                        <MaterialIcons
                            name={alertStyle.icon}
                            size={18}
                            color={alertStyle.color}
                        />
                        <Text className={`ml-2 font-medium text-sm ${
                            type === 'success' ? 'text-green-700' :
                            type === 'error' ? 'text-red-700' :
                            'text-blue-700'
                        }`}>
                            {message}
                        </Text>
                    </View>
                </Animated.View>
            </View>
        </Modal>
    );
};

export default PopupAlert;
