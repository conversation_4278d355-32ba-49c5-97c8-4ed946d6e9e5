const express = require('express');
const router = express.Router();
const {
    getAllOrders,
    getOrderById,
    createOrder,
    updateOrderStatus,
    assignDeliveryPartner
} = require('../controllers/orderController');
const {
    protect,
    adminOnly,
    deliveryPartnerOnly
} = require('../middleware/authMiddleware');

// Get all orders (filtered by user type)
router.get('/', protect, getAllOrders);

// Get order by ID
router.get('/:id', protect, getOrderById);

// Create new order
router.post('/', protect, createOrder);

// Update order status
router.put('/:id/status', protect, updateOrderStatus);

// Assign delivery partner to order (admin only)
router.put('/:id/assign', protect, adminOnly, assignDeliveryPartner);

module.exports = router;