/**
 * Delivery Partner Theme
 * This file contains theme-related constants and utility functions for the delivery partner screens
 */

// Primary colors from tailwind.config.js
export const COLORS = {
  // Primary brand colors
  primary: "#A31621", // madder
  primaryLight: "#C73E49", // lighter madder
  primaryDark: "#7A1019", // darker madder
  secondary: "#FCF7F8", // snow
  
  // Status colors
  success: "#22C55E", // green-600
  successLight: "#DCFCE7", // green-100
  warning: "#F59E0B", // amber-500
  warningLight: "#FEF3C7", // amber-100
  info: "#3B82F6", // blue-500
  infoLight: "#DBEAFE", // blue-100
  error: "#EF4444", // red-500
  errorLight: "#FEE2E2", // red-100
  
  // Neutral colors
  black: "#1F2937", // gray-800
  darkGray: "#4B5563", // gray-600
  mediumGray: "#9CA3AF", // gray-400
  lightGray: "#E5E7EB", // gray-200
  white: "#FFFFFF",
  
  // Transparent colors
  primaryTransparent: "rgba(163, 22, 33, 0.1)", // madder with 10% opacity
  whiteTransparent: "rgba(255, 255, 255, 0.2)", // white with 20% opacity
};

// Status configurations
export const ORDER_STATUS = {
  pending: {
    label: 'Pending',
    icon: 'pending',
    color: COLORS.warning,
    bgColor: COLORS.warningLight,
    textColor: COLORS.warning,
  },
  'in-transit': {
    label: 'In Transit',
    icon: 'local-shipping',
    color: COLORS.info,
    bgColor: COLORS.infoLight,
    textColor: COLORS.info,
  },
  delivered: {
    label: 'Delivered',
    icon: 'check-circle',
    color: COLORS.success,
    bgColor: COLORS.successLight,
    textColor: COLORS.success,
  },
  cancelled: {
    label: 'Cancelled',
    icon: 'cancel',
    color: COLORS.error,
    bgColor: COLORS.errorLight,
    textColor: COLORS.error,
  },
};

// Shadow styles
export const SHADOWS = {
  small: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  medium: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 4
  },
  large: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 6
  },
};

// Card styles
export const CARD_STYLES = {
  default: "bg-white rounded-xl p-4 shadow-sm",
  highlighted: "bg-white rounded-xl p-4 border border-madder/20 shadow-sm",
  stats: "bg-white rounded-xl p-4 shadow-sm",
};

// Button styles
export const BUTTON_STYLES = {
  primary: "bg-madder px-4 py-2 rounded-lg",
  secondary: "bg-white border border-madder px-4 py-2 rounded-lg",
  success: "bg-green-600 px-4 py-2 rounded-lg",
  warning: "bg-amber-500 px-4 py-2 rounded-lg",
  info: "bg-blue-500 px-4 py-2 rounded-lg",
  disabled: "bg-gray-300 px-4 py-2 rounded-lg",
};

// Text styles
export const TEXT_STYLES = {
  header: "text-2xl text-white font-bold",
  subheader: "text-lg font-bold text-gray-800",
  body: "text-gray-600",
  label: "text-gray-700 font-medium",
  small: "text-xs text-gray-500",
};

// Helper function to get status style
export const getStatusStyle = (status) => {
  const statusConfig = ORDER_STATUS[status] || ORDER_STATUS.pending;
  
  return {
    container: `px-3 py-1 rounded-full bg-${statusConfig.bgColor}`,
    text: `font-medium text-${statusConfig.textColor}`,
    icon: statusConfig.icon,
    color: statusConfig.color,
  };
};

// Helper function to format currency
export const formatCurrency = (amount) => {
  return `₹${parseFloat(amount).toFixed(2)}`;
};

export default {
  COLORS,
  ORDER_STATUS,
  SHADOWS,
  CARD_STYLES,
  BUTTON_STYLES,
  TEXT_STYLES,
  getStatusStyle,
  formatCurrency,
};
