import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null, errorInfo: null };
    }

    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // Log the error for debugging
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        
        this.setState({
            error: error,
            errorInfo: errorInfo
        });

        // You can also log the error to an error reporting service here
        // Example: Sentry.captureException(error);
    }

    handleRetry = () => {
        this.setState({ hasError: false, error: null, errorInfo: null });
    };

    handleReportError = () => {
        const errorMessage = `Error: ${this.state.error?.message || 'Unknown error'}\n\nStack: ${this.state.error?.stack || 'No stack trace'}`;
        
        Alert.alert(
            "Error Details",
            errorMessage,
            [
                { text: "Copy to Clipboard", onPress: () => {
                    // In a real app, you would copy to clipboard here
                    console.log('Error copied to clipboard:', errorMessage);
                }},
                { text: "OK", style: "default" }
            ]
        );
    };

    render() {
        if (this.state.hasError) {
            // Fallback UI
            return (
                <View className="flex-1 bg-snow justify-center items-center p-6">
                    <View className="bg-white rounded-2xl p-6 shadow-lg items-center max-w-sm">
                        <View className="w-16 h-16 bg-red-100 rounded-full items-center justify-center mb-4">
                            <MaterialIcons name="error-outline" size={32} color="#DC2626" />
                        </View>
                        
                        <Text className="text-xl font-bold text-gray-800 mb-2 text-center">
                            Something went wrong
                        </Text>
                        
                        <Text className="text-gray-600 text-center mb-6">
                            An unexpected error occurred. Please try again or contact support if the problem persists.
                        </Text>

                        <View className="w-full space-y-3">
                            <TouchableOpacity
                                className="bg-madder py-3 px-6 rounded-xl items-center w-full"
                                onPress={this.handleRetry}
                            >
                                <Text className="text-white font-medium">Try Again</Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                className="bg-gray-200 py-3 px-6 rounded-xl items-center w-full"
                                onPress={this.handleReportError}
                            >
                                <Text className="text-gray-700 font-medium">View Error Details</Text>
                            </TouchableOpacity>
                        </View>

                        {__DEV__ && (
                            <View className="mt-4 p-3 bg-red-50 rounded-lg w-full">
                                <Text className="text-red-700 text-xs font-mono">
                                    {this.state.error?.message || 'Unknown error'}
                                </Text>
                            </View>
                        )}
                    </View>
                </View>
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;
