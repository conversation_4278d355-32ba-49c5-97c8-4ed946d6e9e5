import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TextInput, TouchableOpacity, Image, Alert, ActivityIndicator, Modal } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import * as ImagePicker from 'expo-image-picker';
import { useAdmin } from '../../context/AdminContext';
import { createProduct } from '../../utils/api/productApi';
import { getAllCategories } from '../../utils/api/categoryApi';

const AddProductScreen = ({ navigation }) => {
    const { addProduct } = useAdmin();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [imageUri, setImageUri] = useState(null);
    const [showCategoryModal, setShowCategoryModal] = useState(false);
    const [categories, setCategories] = useState([]);
    const [categoriesLoading, setCategoriesLoading] = useState(true);

    // Fetch categories from the backend
    useEffect(() => {
        const fetchCategories = async () => {
            try {
                setCategoriesLoading(true);
                console.log('Fetching categories from API...');
                const response = await getAllCategories();

                if (Array.isArray(response)) {
                    console.log(`Fetched ${response.length} categories`);
                    setCategories(response);
                } else {
                    console.log('Invalid categories response format:', response);
                    setCategories([]);
                }
            } catch (error) {
                console.error('Error fetching categories:', error);
                setCategories([]);
                Alert.alert('Error', 'Failed to load categories. Please try again.');
            } finally {
                setCategoriesLoading(false);
            }
        };

        fetchCategories();
    }, []);

    const [productData, setProductData] = useState({
        name: '',
        price: '',
        description: '',
        // Removed stock field
        category: null, // Changed to null to represent no selection
        categoryName: '', // Added to store the category name for display
        weight: '',
        pieces: '',
        discountPercentage: '0',
        discount_price: '',
        offer: '',
        isAvailable: true,
        image: 'https://via.placeholder.com/150', // Default placeholder
        id: `PROD${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`
    });

    // Request permission for image library
    useEffect(() => {
        const requestPermissions = async () => {
            try {
                const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
                if (status !== 'granted') {
                    Alert.alert('Permission Required', 'Sorry, we need camera roll permissions to upload images!');
                }
            } catch (error) {
                console.error('Error requesting permissions:', error);
            }
        };

        requestPermissions();

        // Cleanup function
        return () => {
            setImageUri(null);
            setError(null);
        };
    }, []);

    // Function to calculate discount price from price and discount percentage
    const calculateDiscountPrice = (price, discountPercentage) => {
        if (!price || !discountPercentage) return '';
        const priceValue = parseFloat(price);
        const discountValue = parseFloat(discountPercentage);
        if (isNaN(priceValue) || isNaN(discountValue)) return '';

        const discountedPrice = priceValue * (1 - discountValue / 100);
        return discountedPrice.toFixed(2);
    };

    // Function to calculate discount percentage from price and discount price
    const calculateDiscountPercentage = (price, discountPrice) => {
        if (!price || !discountPrice) return '0';
        const priceValue = parseFloat(price);
        const discountPriceValue = parseFloat(discountPrice);
        if (isNaN(priceValue) || isNaN(discountPriceValue) || priceValue <= 0 || discountPriceValue >= priceValue) return '0';

        const discountPercentage = ((priceValue - discountPriceValue) / priceValue) * 100;
        return discountPercentage.toFixed(0);
    };

    // Function to generate offer text
    const generateOfferText = (discountPercentage) => {
        if (!discountPercentage || parseFloat(discountPercentage) <= 0) return '';
        return `${parseFloat(discountPercentage).toFixed(0)}% OFF`;
    };

    // Function to update price
    const updatePrice = (price) => {
        const updatedData = { ...productData, price };

        // If discount percentage exists, recalculate discount price
        if (updatedData.discountPercentage && parseFloat(updatedData.discountPercentage) > 0) {
            updatedData.discount_price = calculateDiscountPrice(price, updatedData.discountPercentage);
            updatedData.offer = generateOfferText(updatedData.discountPercentage);
        }
        // If discount price exists, recalculate discount percentage
        else if (updatedData.discount_price && parseFloat(updatedData.discount_price) > 0) {
            updatedData.discountPercentage = calculateDiscountPercentage(price, updatedData.discount_price);
            updatedData.offer = generateOfferText(updatedData.discountPercentage);
        }

        setProductData(updatedData);
    };

    // Function to update discount percentage
    const updateDiscountPercentage = (discountPercentage) => {
        // Ensure discount is between 0 and 100
        const discount = Math.min(Math.max(parseFloat(discountPercentage) || 0, 0), 100);

        const updatedData = {
            ...productData,
            discountPercentage: discount.toString()
        };

        // Calculate discount price if price exists
        if (updatedData.price) {
            updatedData.discount_price = calculateDiscountPrice(updatedData.price, discount);
            updatedData.offer = generateOfferText(discount);
        }

        setProductData(updatedData);
    };

    // Function to update discount price
    const updateDiscountPrice = (discountPrice) => {
        const updatedData = { ...productData, discount_price: discountPrice };

        // Calculate discount percentage if price exists
        if (updatedData.price) {
            updatedData.discountPercentage = calculateDiscountPercentage(updatedData.price, discountPrice);
            updatedData.offer = generateOfferText(updatedData.discountPercentage);
        }

        setProductData(updatedData);
    };

    // Image picker function
    const pickImage = async () => {
        try {
            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
            });

            if (!result.canceled && result.assets && result.assets.length > 0) {
                setImageUri(result.assets[0].uri);
                setProductData({...productData, image: result.assets[0].uri});
            }
        } catch (error) {
            console.error('Error picking image:', error);
            Alert.alert('Error', 'Failed to pick image. Please try again.');
        }
    };

    const handleSave = async () => {
        try {
            setLoading(true);
            setError(null);

            // Validate required fields
            if (!productData.name?.trim()) {
                setError('Product name is required');
                Alert.alert("Validation Error", "Product name is required");
                setLoading(false);
                return;
            }

            if (!productData.price || parseFloat(productData.price) <= 0) {
                setError('Valid price is required');
                Alert.alert("Validation Error", "Please enter a valid price greater than 0");
                setLoading(false);
                return;
            }

            // Prepare data for creation - ensure numeric fields are properly formatted
            const dataToCreate = {
                ...productData,
                name: productData.name.trim(),
                price: parseFloat(productData.price) || 0,
                discountPercentage: parseFloat(productData.discountPercentage) || 0,
                discount_price: parseFloat(productData.discount_price) || 0,
                offer: productData.offer || '',
                isAvailable: Boolean(productData.isAvailable)
            };

            console.log('Creating new product:', dataToCreate);

            // Call API to create product
            const response = await createProduct(dataToCreate);

            if (response && response.product) {
                console.log('Product created successfully:', response.product);

                // Add product to admin context
                addProduct(response.product);

                Alert.alert(
                    "Success",
                    "Product created successfully",
                    [{ text: "OK", onPress: () => navigation.goBack() }]
                );
            } else {
                // If API doesn't return the created product
                console.log('Product created but no response data');

                // Add product to admin context with local data
                addProduct(dataToCreate);

                Alert.alert(
                    "Success",
                    "Product created successfully",
                    [{ text: "OK", onPress: () => navigation.goBack() }]
                );
            }
        } catch (err) {
            console.error('Error creating product:', err);
            const errorMessage = err.message || 'Failed to create product. Please try again.';
            setError(errorMessage);
            Alert.alert("Error", errorMessage);
        } finally {
            setLoading(false);
        }
    };

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder px-6 pt-8 pb-4 rounded-b-3xl shadow-md">
                <View className="flex-row items-center">
                    <TouchableOpacity onPress={() => navigation.goBack()} className="mr-3 bg-white/20 p-2 rounded-full">
                        <MaterialIcons name="arrow-back" size={22} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">Add New Product</Text>
                </View>
            </View>

            {loading ? (
                <View className="flex-1 justify-center items-center">
                    <ActivityIndicator size="large" color="#A31621" />
                    <Text className="mt-4 text-gray-600">Creating product...</Text>
                </View>
            ) : (
                <ScrollView className="p-4">
                    {error && (
                        <View className="bg-red-100 p-3 rounded-xl mb-4">
                            <Text className="text-red-700">{error}</Text>
                        </View>
                    )}

                    {/* Image Upload Section */}
                    <View className="bg-white p-4 rounded-xl mb-4 items-center">
                        <Image
                            source={{ uri: imageUri || productData.image }}
                            className="w-full h-48 rounded-xl mb-4"
                            resizeMode="cover"
                        />

                        <TouchableOpacity
                            className="bg-gray-100 p-3 rounded-lg flex-row items-center"
                            onPress={pickImage}
                        >
                            <MaterialIcons name="photo-camera" size={20} color="#666" />
                            <Text className="text-gray-700 ml-2">Upload Image</Text>
                        </TouchableOpacity>
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1">Product Name *</Text>
                        <TextInput
                            value={productData.name}
                            onChangeText={(text) => setProductData({ ...productData, name: text })}
                            className="bg-white p-4 rounded-xl"
                            placeholder="Product Name"
                        />
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1">Price (₹) *</Text>
                        <TextInput
                            value={productData.price}
                            onChangeText={(text) => updatePrice(text)}
                            className="bg-white p-4 rounded-xl"
                            keyboardType="numeric"
                            placeholder="Price"
                        />
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1">Discount Percentage (%)</Text>
                        <TextInput
                            value={productData.discountPercentage}
                            onChangeText={(text) => updateDiscountPercentage(text)}
                            className="bg-white p-4 rounded-xl"
                            keyboardType="numeric"
                            placeholder="Discount Percentage (0-100)"
                        />
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1">Discount Price (₹)</Text>
                        <TextInput
                            value={productData.discount_price}
                            onChangeText={(text) => updateDiscountPrice(text)}
                            className="bg-white p-4 rounded-xl"
                            keyboardType="numeric"
                            placeholder="Discounted Price"
                        />
                    </View>

                    {productData.offer && (
                        <View className="mb-4">
                            <Text className="text-gray-600 mb-1 ml-1">Offer</Text>
                            <View className="bg-green-50 p-4 rounded-xl">
                                <Text className="text-green-700 font-medium text-center">
                                    {productData.offer}
                                </Text>
                            </View>
                        </View>
                    )}

                    {/* Stock field removed */}

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1">Category</Text>
                        <TouchableOpacity
                            className="bg-white p-4 rounded-xl flex-row justify-between items-center"
                            onPress={() => setShowCategoryModal(true)}
                            disabled={categoriesLoading}
                        >
                            {categoriesLoading ? (
                                <View className="flex-row items-center">
                                    <ActivityIndicator size="small" color="#A31621" />
                                    <Text className="text-gray-400 ml-2">Loading categories...</Text>
                                </View>
                            ) : (
                                <Text className={productData.categoryName ? "text-gray-800" : "text-gray-400"}>
                                    {productData.categoryName || "Select Category"}
                                </Text>
                            )}
                            <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1 text-lg font-medium">Product Availability</Text>
                        <View className="bg-white p-5 rounded-xl">
                            <View className="flex-row items-center justify-between mb-3">
                                <View>
                                    <Text className="text-lg font-bold">
                                        {productData.isAvailable ? 'Product is Available' : 'Product is Unavailable'}
                                    </Text>
                                    <Text className="text-gray-500 mt-1">
                                        {productData.isAvailable
                                            ? 'Customers can purchase this product'
                                            : 'This product will be shown as unavailable to customers'}
                                    </Text>
                                </View>
                                <TouchableOpacity
                                    onPress={() => setProductData({ ...productData, isAvailable: !productData.isAvailable })}
                                    className={`w-14 h-8 rounded-full ${productData.isAvailable ? 'bg-green-500' : 'bg-gray-300'} justify-center px-1`}
                                >
                                    <View className={`w-6 h-6 rounded-full bg-white shadow-md ${productData.isAvailable ? 'ml-7' : 'ml-0'}`} />
                                </TouchableOpacity>
                            </View>

                            <View className={`p-3 rounded-lg mt-2 ${productData.isAvailable ? 'bg-green-50' : 'bg-red-50'}`}>
                                <Text className={`${productData.isAvailable ? 'text-green-700' : 'text-red-700'}`}>
                                    {productData.isAvailable
                                        ? 'This product will be displayed normally in the app.'
                                        : 'This product will appear darkened with an "Unavailable" label.'}
                                </Text>
                            </View>
                        </View>
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1">Weight (e.g., 500g)</Text>
                        <TextInput
                            value={productData.weight}
                            onChangeText={(text) => setProductData({ ...productData, weight: text })}
                            className="bg-white p-4 rounded-xl"
                            placeholder="Weight"
                        />
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1">Pieces (e.g., 4-5 pcs)</Text>
                        <TextInput
                            value={productData.pieces}
                            onChangeText={(text) => setProductData({ ...productData, pieces: text })}
                            className="bg-white p-4 rounded-xl"
                            placeholder="Pieces"
                        />
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-600 mb-1 ml-1">Description</Text>
                        <TextInput
                            value={productData.description}
                            onChangeText={(text) => setProductData({ ...productData, description: text })}
                            className="bg-white p-4 rounded-xl"
                            placeholder="Description"
                            multiline
                            numberOfLines={4}
                            textAlignVertical="top"
                        />
                    </View>



                    <TouchableOpacity
                        onPress={handleSave}
                        className="bg-madder p-4 rounded-xl items-center mt-4 mb-8"
                    >
                        <Text className="text-white font-bold">Create Product</Text>
                    </TouchableOpacity>
                </ScrollView>
            )}

            {/* Category Selection Modal */}
            <Modal
                visible={showCategoryModal}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowCategoryModal(false)}
            >
                <View className="flex-1 justify-end bg-black/50">
                    <View className="bg-white rounded-t-3xl p-4 h-1/2">
                        <View className="flex-row justify-between items-center mb-4">
                            <Text className="text-xl font-bold">Select Category</Text>
                            <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
                                <MaterialIcons name="close" size={24} color="#666" />
                            </TouchableOpacity>
                        </View>

                        {categoriesLoading ? (
                            <View className="flex-1 justify-center items-center">
                                <ActivityIndicator size="large" color="#A31621" />
                                <Text className="mt-4 text-gray-600">Loading categories...</Text>
                            </View>
                        ) : categories.length === 0 ? (
                            <View className="flex-1 justify-center items-center">
                                <MaterialIcons name="category" size={48} color="#ccc" />
                                <Text className="text-gray-400 mt-2">No categories found</Text>
                            </View>
                        ) : (
                            <ScrollView>
                                {categories.map((category) => (
                                    <TouchableOpacity
                                        key={category._id}
                                        className="py-3 border-b border-gray-100"
                                        onPress={() => {
                                            setProductData({
                                                ...productData,
                                                category: category._id,
                                                categoryName: category.name
                                            });
                                            setShowCategoryModal(false);
                                        }}
                                    >
                                        <View className="flex-row items-center">
                                            {category.image && (
                                                <Image
                                                    source={{ uri: category.image }}
                                                    className="w-8 h-8 rounded-full mr-3"
                                                    resizeMode="cover"
                                                />
                                            )}
                                            <Text className={`text-lg ${productData.category === category._id ? 'text-madder font-medium' : 'text-gray-800'}`}>
                                                {category.name}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                ))}
                            </ScrollView>
                        )}
                    </View>
                </View>
            </Modal>
        </View>
    );
};

export default AddProductScreen;