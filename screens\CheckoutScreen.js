import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  Animated,
  ActivityIndicator,
} from "react-native";
import React, { useState, useEffect, useRef } from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { useCart } from "../context/CartContext";
import { useOrders } from "../context/OrderContext";
import { useUser } from "../context/UserContext";
import { useAuth } from "../context/AuthContext";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import Toast from 'react-native-toast-message';
import { getCombinedAddresses } from '../utils/addressUtils';

const PAYMENT_METHODS = [
  {
    id: "upi",
    name: "UPI",
    icon: "payment",
    description: "Pay using UPI apps",
    apps: ["Google Pay", "PhonePe", "Paytm"],
  },
  {
    id: "cod",
    name: "Cash on Delivery",
    icon: "local-atm",
    description: "Pay when you receive your order",
    additionalFee: 0, // Removed the 49 rupee fee
  },
];

// Removed hardcoded ADDRESSES array - now fetching from database

// Base slot definitions - these will be filtered based on current time
const BASE_DELIVERY_SLOTS = [
  { id: 1, time: "9:00 AM - 12:00 PM", label: "Morning", day: "Today", startHour: 9, endHour: 12 },
  { id: 2, time: "5:00 PM - 7:00 PM", label: "Evening", day: "Today", startHour: 17, endHour: 19 },
  { id: 3, time: "9:00 AM - 12:00 PM", label: "Morning", day: "Tomorrow", startHour: 9, endHour: 12 },
  { id: 4, time: "5:00 PM - 7:00 PM", label: "Evening", day: "Tomorrow", startHour: 17, endHour: 19 },
];

// Function to get available delivery slots based on current time
const getAvailableDeliverySlots = () => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();

  // Calculate cutoff time (need at least 1 hour before slot starts)
  const CUTOFF_HOURS = 1;

  return BASE_DELIVERY_SLOTS.filter(slot => {
    if (slot.day === "Tomorrow") {
      // All tomorrow slots are available
      return true;
    }

    // For today's slots, check if we're past the cutoff time
    if (slot.day === "Today") {
      // If current time + cutoff is past the slot start time, it's unavailable
      if (currentHour > slot.startHour - CUTOFF_HOURS) {
        return false;
      }

      // If we're exactly at the cutoff hour, check minutes
      if (currentHour === slot.startHour - CUTOFF_HOURS && currentMinute > 0) {
        return false;
      }
    }

    return true;
  });
};

const CheckoutScreen = ({ route }) => {
  const navigation = useNavigation();
  const { cartItems, clearCart } = useCart();
  const { addOrder, calculateCoinsEarned } = useOrders();
  const { addresses, getDefaultAddress, getPrimaryAddress, currentUser, refreshUserData } = useUser();
  const { isLoggedIn } = useAuth();

  // Use user addresses from context, fallback to dummy data if none available
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState(PAYMENT_METHODS[1]); // Default to COD since UPI is disabled
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [addressesLoading, setAddressesLoading] = useState(true);

  // Get all bill details from route params if available
  const [subtotal] = useState(route.params?.subtotal || 0);
  const [deliveryFee] = useState(route.params?.deliveryFee || 0);
  const [totalSavings] = useState(route.params?.totalSavings || 0);
  const [finalTotal] = useState(route.params?.finalTotal || 0);
  const [couponDiscount] = useState(route.params?.couponDiscount || 0);
  const [coinsToApply] = useState(route.params?.coinsToApply || 0); // Correctly receive coinsToApply
  const [appliedCoupon] = useState(route.params?.appliedCoupon || null);
  const [usingCoins] = useState(route.params?.usingCoins || false);

  // New state variables for modals
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [newOrderData, setNewOrderData] = useState(null);

  // Animation values
  const scaleAnim = useRef(new Animated.Value(0.5)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  // Animation functions - Updated for smoother animations
  const animateIn = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateOut = (callback) => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(callback);
  };

  // Effect to trigger animation when modal opens
  useEffect(() => {
    if (confirmModalVisible || successModalVisible) {
      animateIn();
    }
  }, [confirmModalVisible, successModalVisible]);

  // Check if user is logged in when component mounts, fetch addresses, and initialize available slots
  useEffect(() => {
    const checkUserAndFetchAddresses = async () => {
      setAddressesLoading(true);
      try {
        // Always refresh user data to get the latest info and addresses
        console.log('Refreshing user data to get latest addresses...');
        await refreshUserData();

        // Check if user is logged in after refresh
        if (!currentUser) {
          console.log('User not logged in after refresh, checking auth context...');

          // Check if we're actually logged in according to auth context
          if (isLoggedIn) {
            console.log('Auth context says user is logged in, but user data is missing. Continuing without alert.');
            // We're logged in but user data is missing - this is likely a temporary API issue
            // Continue without showing the alert to avoid blocking the user
          } else {
            console.log('User not logged in according to auth context, showing alert');
            Alert.alert(
              "Authentication Required",
              "Please log in to proceed with checkout",
              [
                {
                  text: "OK",
                  onPress: () => {
                    // Navigate back to previous screen
                    navigation.goBack();
                  }
                }
              ]
            );
            return;
          }
        }

        console.log('Addresses from context:', addresses);

        // Get combined addresses
        const combinedAddresses = getCombinedAddresses(currentUser, addresses);
        console.log('Combined addresses:', combinedAddresses.length);

        if (combinedAddresses.length > 0) {
          // Always try to find a primary address first
          const primaryAddress = getPrimaryAddress();
          if (primaryAddress) {
            console.log('Using primary address:', primaryAddress);
            setSelectedAddress(primaryAddress);
          } else {
            // If no primary address, try to find a main address
            const mainAddress = combinedAddresses.find(addr => addr.isMain);
            if (mainAddress) {
              console.log('Using main address:', mainAddress);
              setSelectedAddress(mainAddress);
            } else {
              // Otherwise use the first address in the combined list
              console.log('Using first address from combined list:', combinedAddresses[0]);
              setSelectedAddress(combinedAddresses[0]);
            }
          }
        } else {
          // No addresses available
          console.log('No addresses available');
          setSelectedAddress(null);
        }
      } catch (error) {
        console.error('Error in checkout initialization:', error);
        setSelectedAddress(null);
      } finally {
        setAddressesLoading(false);
      }
    };

    // Initialize available delivery slots based on current time
    const initializeDeliverySlots = () => {
      const slots = getAvailableDeliverySlots();
      console.log('Available delivery slots:', slots.length);
      setAvailableSlots(slots);

      // Select the first available slot by default
      if (slots.length > 0) {
        setSelectedSlot(slots[0]);
      } else {
        // If no slots are available, set a default empty slot to prevent null errors
        console.log('No available slots found, setting default empty slot');
        setSelectedSlot({
          id: 0,
          time: "No available slots",
          label: "Unavailable",
          day: "None",
          startHour: 0,
          endHour: 0
        });
      }
    };

    // Check if cart items are available
    const checkCartItems = () => {
      console.log('Checking cart items in checkout screen:', cartItems);
      if (!cartItems || cartItems.length === 0) {
        console.log('No cart items found, navigating back to cart');
        Alert.alert(
          "Empty Cart",
          "Your cart is empty. Please add items to your cart before checkout.",
          [
            {
              text: "OK",
              onPress: () => {
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        console.log('Cart items found:', cartItems.length);
        console.log('Cart items details:', cartItems.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          totalPrice: item.totalPrice
        })));
      }
    };

    checkUserAndFetchAddresses();
    initializeDeliverySlots();
    checkCartItems();
  }, []);

  // Navigate to the new DeliveryAddressScreen
  const navigateToDeliveryAddressScreen = () => {
    navigation.navigate('DeliveryAddressScreen');
  };

  // Refresh addresses when returning from AddressScreen
  useFocusEffect(
    React.useCallback(() => {
      const refreshAddresses = async () => {
        console.log('Checkout screen focused, refreshing addresses...');
        setAddressesLoading(true);
        try {
          await refreshUserData();
          console.log('Addresses refreshed on focus:', addresses);

          // Get combined addresses
          const combinedAddresses = getCombinedAddresses(currentUser, addresses);
          console.log('Combined addresses on focus:', combinedAddresses.length);

          if (combinedAddresses.length > 0) {
            // Check if current selected address still exists
            if (selectedAddress) {
              // For main address, check by isMain flag
              if (selectedAddress.isMain) {
                const mainAddressExists = combinedAddresses.some(addr => addr.isMain);
                if (!mainAddressExists) {
                  console.log('Main address no longer exists, selecting new address');
                  selectNewAddress(combinedAddresses);
                }
              }
              // For regular addresses, check by ID
              else {
                const addressStillExists = combinedAddresses.some(
                  addr => !addr.isMain && ((addr._id && addr._id === selectedAddress._id) ||
                         (addr.id && addr.id === selectedAddress.id))
                );

                if (!addressStillExists) {
                  console.log('Selected address no longer exists, selecting new address');
                  selectNewAddress(combinedAddresses);
                }
              }
            } else {
              // No address was selected, select one now
              selectNewAddress(combinedAddresses);
            }
          } else {
            console.log('No addresses available');
            setSelectedAddress(null);
          }

          // Helper function to select a new address based on priority
          function selectNewAddress(addresses) {
            // Select the primary address initially
            const primaryAddress = addresses.find(addr => addr.isMain);
            if (primaryAddress) {
              console.log('Initially selecting primary address:', primaryAddress);
              setSelectedAddress(primaryAddress);
            } else if (addresses.length > 0) {
              console.log('No primary address found, selecting first address:', addresses[0]);
              setSelectedAddress(addresses[0]);
            } else {
              console.log('No addresses available');
              setSelectedAddress(null);
            }
          }
        } catch (error) {
          console.error('Error refreshing addresses on focus:', error);

          // If error, try to select a fallback address
          if (currentUser) {
            // Try to create combined addresses even in error state
            const fallbackAddresses = getCombinedAddresses(currentUser, addresses);
            if (fallbackAddresses.length > 0) {
              console.log('Using fallback addresses after error:', fallbackAddresses.length);
              // Select the primary address if available
              const primaryAddress = fallbackAddresses.find(addr => addr.isMain);
              if (primaryAddress) {
                console.log('Using primary address as fallback:', primaryAddress);
                setSelectedAddress(primaryAddress);
              } else {
                console.log('Using first address as fallback:', fallbackAddresses[0]);
                setSelectedAddress(fallbackAddresses[0]);
              }
            }
          }
        } finally {
          setAddressesLoading(false);
        }
      };

      refreshAddresses();

      return () => {
        // Cleanup function when screen loses focus
      };
    }, [])
  );

  // If we received a coupon from CartScreen, it's already handled in the state initialization

  const calculateTotal = () => {
    // If we have the subtotal from CartScreen, use it
    if (route.params?.subtotal) {
      return route.params.subtotal;
    }

    // Otherwise calculate it
    const total = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
    console.log('Cart items total calculation:', {
      itemCount: cartItems.length,
      items: cartItems.map(item => ({
        name: item.name,
        quantity: item.quantity,
        price: item.price,
        discount_price: item.discount_price,
        totalPrice: item.totalPrice
      })),
      calculatedTotal: total
    });
    return total;
  };

  const calculateTotalSavings = () => {
    // If we have the totalSavings from CartScreen, use it
    if (route.params?.totalSavings) {
      return route.params.totalSavings;
    }

    // Otherwise calculate it
    return cartItems.reduce((sum, item) => sum + (item.savings || 0), 0);
  };

  // Helper function to calculate the total amount consistently throughout the component
  const calculateFinalTotal = () => {
    // If we have the final total from CartScreen, use it
    if (route.params?.finalTotal) {
      return route.params.finalTotal;
    }

    // Otherwise calculate it
    const calculatedSubtotal = calculateTotal();
    const calculatedDeliveryFee = calculatedSubtotal >= 499 ? 0 : 49;
    // Calculate total including coupon discount and potential coins discount (using coinsToApply)
    const potentialCoinsDiscount = coinsToApply > 0 ? coinsToApply : 0; // Use the value passed from CartScreen

    const finalAmount = calculatedSubtotal + calculatedDeliveryFee - couponDiscount - potentialCoinsDiscount;

    // Log the calculation details for debugging
    console.log('Amount Calculation Details:', {
      subtotal: calculatedSubtotal,
      deliveryFee: calculatedDeliveryFee,
      couponDiscount,
      potentialCoinsDiscount,
      finalAmount
    });

    return finalAmount;
  };

  // Keep the old function for backward compatibility
  const getTotalAmount = () => {
    return calculateFinalTotal();
  };

  // Coupon functionality is handled via route params from CartScreen

  const handlePlaceOrder = () => {
    // Instead of Alert, show the custom modal
    setConfirmModalVisible(true);
  };

  const confirmOrder = async () => {
    setIsLoading(true);

    try {
      // Check if user is logged in
      if (!currentUser) {
        Alert.alert(
          "Authentication Error",
          "Please log in to place an order",
          [{ text: "OK" }]
        );
        setIsLoading(false);
        setConfirmModalVisible(false);
        return;
      }

      // Check if user has an ID (either _id or id)
      if (!currentUser._id && !currentUser.id) {
        Alert.alert(
          "Authentication Error",
          "Your session may have expired. Please log in again.",
          [{ text: "OK" }]
        );
        setIsLoading(false);
        setConfirmModalVisible(false);
        return;
      }

      // Validate that the selected slot is still available
      if (selectedSlot) {
        // Re-check available slots to ensure the selected one is still valid
        const currentAvailableSlots = getAvailableDeliverySlots();
        const isSlotStillAvailable = currentAvailableSlots.some(slot => slot.id === selectedSlot.id);

        if (!isSlotStillAvailable) {
          Alert.alert(
            "Delivery Slot Unavailable",
            "The selected delivery slot is no longer available. Please select another slot.",
            [{ text: "OK" }]
          );

          // Update available slots
          setAvailableSlots(currentAvailableSlots);

          // Select the first available slot if any
          if (currentAvailableSlots.length > 0) {
            setSelectedSlot(currentAvailableSlots[0]);
          }

          setIsLoading(false);
          setConfirmModalVisible(false);
          return;
        }
      }

      // Format the expected delivery date and time
      const deliveryInfo = selectedSlot ? {
          day: selectedSlot.day,
          time: selectedSlot.time,
          formattedDate: selectedSlot.day === "Today"
            ? new Date().toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
            : new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
        } : {
          day: "Tomorrow",
          time: "9:00 AM - 12:00 PM",
          formattedDate: new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
        };

      // Calculate total amount
      const totalAmount = getTotalAmount();

        // Coins earned will be calculated by the backend based on the final amount after actual discount

        // Validate that an address is selected
        if (!selectedAddress) {
        Alert.alert(
          "Address Required",
          "Please select a delivery address to continue",
          [{ text: "OK" }]
        );
        setIsLoading(false);
        setConfirmModalVisible(false);
        return;
      }

      // Format the address for the order
      let formattedAddress = {
        doorNo: selectedAddress.doorNo || selectedAddress.address || '',
        streetName: selectedAddress.streetName || selectedAddress.area || '',
        area: selectedAddress.area || selectedAddress.city || '',
        district: selectedAddress.district || selectedAddress.city || '',
        pincode: selectedAddress.pincode || '',
      };

      // Create the fullAddress string if it doesn't exist
      if (!selectedAddress.fullAddress) {
        formattedAddress.fullAddress = `${formattedAddress.doorNo}${formattedAddress.streetName ? `, ${formattedAddress.streetName}` : ''}${formattedAddress.area ? `, ${formattedAddress.area}` : ''}${formattedAddress.district ? `, ${formattedAddress.district}` : ''}${formattedAddress.pincode ? ` - ${formattedAddress.pincode}` : ''}`.replace(/\s+/g, ' ').trim();
      } else {
        formattedAddress.fullAddress = selectedAddress.fullAddress;
      }

      // Log the selected address to see if it has coordinates
      console.log('Selected address before formatting:', {
        id: selectedAddress._id || selectedAddress.id,
        type: selectedAddress.type,
        isMain: selectedAddress.isMain,
        hasCoordinatesObject: !!(selectedAddress.coordinates),
        coordinatesObject: selectedAddress.coordinates,
        hasDirectCoordinates: !!(selectedAddress.latitude && selectedAddress.longitude),
        latitude: selectedAddress.latitude,
        longitude: selectedAddress.longitude,
        fullAddress: selectedAddress.fullAddress
      });

      // Add coordinates to the formatted address
      // Check for coordinates in different possible locations
      if (selectedAddress.coordinates && selectedAddress.coordinates.latitude && selectedAddress.coordinates.longitude) {
        // If coordinates are in a nested coordinates object
        formattedAddress.coordinates = {
          latitude: parseFloat(selectedAddress.coordinates.latitude),
          longitude: parseFloat(selectedAddress.coordinates.longitude)
        };
        // Also add direct properties for backward compatibility
        formattedAddress.latitude = parseFloat(selectedAddress.coordinates.latitude);
        formattedAddress.longitude = parseFloat(selectedAddress.coordinates.longitude);
        console.log('Using coordinates from nested coordinates object:', formattedAddress.coordinates);
      } else if (selectedAddress.latitude && selectedAddress.longitude) {
        // If coordinates are direct properties
        formattedAddress.latitude = parseFloat(selectedAddress.latitude);
        formattedAddress.longitude = parseFloat(selectedAddress.longitude);
        // Also add nested coordinates for forward compatibility
        formattedAddress.coordinates = {
          latitude: parseFloat(selectedAddress.latitude),
          longitude: parseFloat(selectedAddress.longitude)
        };
        console.log('Using coordinates from direct properties:', {
          latitude: formattedAddress.latitude,
          longitude: formattedAddress.longitude
        });
      } else {
        console.log('WARNING: No coordinates found in selected address!');
      }

      // Debug log to verify coordinates are included
      console.log('Final formatted address with coordinates:', {
        address: formattedAddress.fullAddress,
        hasNestedCoordinates: !!(formattedAddress.coordinates && formattedAddress.coordinates.latitude && formattedAddress.coordinates.longitude),
        hasDirectCoordinates: !!(formattedAddress.latitude && formattedAddress.longitude),
        coordinates: formattedAddress.coordinates,
        latitude: formattedAddress.latitude,
        longitude: formattedAddress.longitude
      });

      console.log('Using delivery address:', formattedAddress);

      // Create order data object
      const orderData = {
        orderNumber: Math.floor(Math.random() * 1000000),
        date: new Date().toISOString(),
        total: totalAmount,
        items: cartItems.map(item => ({
          productId: item._id || item.id,
          name: item.name,
          price: item.price,
          discount_price: item.discount_price,
          quantity: item.quantity,
          totalPrice: item.totalPrice,
          image: item.image || null
        })),
        status: "PLACED",
        paymentMethod: selectedPayment.id,
        deliveryAddress: formattedAddress,
        expectedDelivery: deliveryInfo,
        couponDiscount: couponDiscount,
        appliedCoupon: appliedCoupon ? appliedCoupon.code : null,
        // coinsEarned: coinsEarned, // Backend calculates this
        coinsToApply: coinsToApply, // Pass coinsToApply to backend
        user: currentUser?._id || currentUser?.id || ''
      };

        // console.log('Placing order with data:', JSON.stringify(orderData, null, 2)); // This is the old format

      try {
        // Format the items to match the backend expectations
        const formattedItems = cartItems.map(item => ({
          productId: item._id || item.id,
          name: item.name,
          price: item.price,
          discount_price: item.discount_price || item.price,
          quantity: item.quantity,
          totalPrice: item.totalPrice || (item.quantity * (item.discount_price || item.price)),
          image: item.image || null
        }));

        // Format the expected delivery date as a proper Date object
        let expectedDeliveryDate;
        // For scheduled delivery, parse the date based on the selected slot
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Check if selectedSlot exists
        if (selectedSlot && selectedSlot.day && selectedSlot.time) {
          // Use the base date (today or tomorrow)
          const baseDate = selectedSlot.day === "Today" ? today : tomorrow;

          // Extract hours and minutes from the time slot (e.g., "11:00 AM - 1:00 PM")
          const timeMatch = selectedSlot.time.match(/(\d+):(\d+)\s*([AP]M)/i);
          if (timeMatch) {
            const hours = parseInt(timeMatch[1]);
            const minutes = parseInt(timeMatch[2]);
            const isPM = timeMatch[3].toUpperCase() === 'PM';

            // Set the hours and minutes
            baseDate.setHours(
              isPM && hours < 12 ? hours + 12 : (hours === 12 && !isPM ? 0 : hours),
              minutes,
              0,
              0
            );

            expectedDeliveryDate = baseDate;
          } else {
            // Fallback if time parsing fails
            expectedDeliveryDate = baseDate;
          }
        } else {
          // Fallback if selectedSlot is null or missing properties
          console.log('Selected slot is invalid, using default delivery date');
          expectedDeliveryDate = tomorrow; // Default to tomorrow
        }

        // Store the expected delivery info for display purposes
        const expectedDeliveryInfo = selectedSlot && selectedSlot.day && selectedSlot.time ? {
            day: selectedSlot.day,
            time: selectedSlot.time,
            formattedDate: selectedSlot.day === "Today"
              ? new Date().toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
              : new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
          } : {
            day: "Tomorrow",
            time: "9:00 AM - 12:00 PM",
            formattedDate: new Date(Date.now() + 86400000).toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" })
          };

        // Check if cart items exist before proceeding
        if (!cartItems || cartItems.length === 0) {
          Alert.alert(
            "Empty Cart",
            "Your cart is empty. Please add items to your cart before placing an order.",
            [{ text: "OK" }]
          );
          setIsLoading(false);
          setConfirmModalVisible(false);
          return;
        }

        // Get the final total using our consistent calculation function
        const finalTotal = calculateFinalTotal();
        const subtotal = calculateTotal();

        // Log the amount before creating the order
        console.log('Amount before order creation:', {
          finalTotal: finalTotal,
          subtotal: subtotal,
          cartItemsCount: cartItems.length,
          cartItems: cartItems.map(item => ({
            name: item.name,
            quantity: item.quantity,
            price: item.price,
            totalPrice: item.totalPrice
          }))
        });

        // Create a properly formatted order data object
        const formattedOrderData = {
          orderNumber: Math.floor(Math.random() * 1000000),
          date: new Date().toISOString(),
          totalAmount: finalTotal, // Use our consistent calculation
          originalAmount: subtotal, // Original price before discounts
          items: formattedItems,
          status: "PLACED",
          paymentMethod: selectedPayment.id,
          deliveryAddress: formattedAddress,
          // Add deliveryCoordinates as a separate field to ensure they're properly saved
          deliveryCoordinates: formattedAddress.coordinates || {
            latitude: formattedAddress.latitude || null,
            longitude: formattedAddress.longitude || null
          },
          expectedDelivery: expectedDeliveryDate.toISOString(), // Send as ISO string for proper date parsing
          // Store the display info in a separate field
          expectedDeliveryInfo: expectedDeliveryInfo,
          couponDiscount: couponDiscount || 0,
          // coinsDiscount: coinsDiscount || 0, // Backend calculates actual discount
          coinsToApply: coinsToApply || 0, // Pass coins user wants to apply
          appliedCoupon: appliedCoupon ? appliedCoupon.code : null,
          // coinsEarned: coinsEarned || 0, // Backend calculates this
          // usingCoins: usingCoins || false // Not needed by backend
        };

        console.log('Placing formatted order with data (sending coinsToApply):', JSON.stringify(formattedOrderData, null, 2));

        // Save order to database
        const createdOrder = await addOrder(formattedOrderData);
        console.log('Order created successfully:', createdOrder);

        // Log the amount after order creation to compare with the amount before
        console.log('Amount after order creation:', {
          sentAmount: finalTotal,
          receivedAmount: createdOrder.totalAmount || createdOrder.total,
          difference: (createdOrder.totalAmount || createdOrder.total) - finalTotal,
          cartItemsCount: cartItems.length,
          subtotal: subtotal
        });

        if (createdOrder) {
          setNewOrderData(createdOrder);

          // Refresh user data to update coins
          await refreshUserData();

          // Clear cart
          clearCart();

          // Show success toast
          Toast.show({
            type: 'success',
            text1: 'Order Placed Successfully',
            text2: `Order #${createdOrder.orderNumber || ''} has been placed`,
            position: 'bottom',
            visibilityTime: 4000,
          });

          // Close confirm modal and show success modal
          animateOut(() => {
            setConfirmModalVisible(false);
            setTimeout(() => {
              setSuccessModalVisible(true);
            }, 300);
          });
        } else {
          // Handle error
          console.error('Order creation returned null or undefined');
          Alert.alert(
            "Error",
            "Failed to create order. Please try again."
          );
          setConfirmModalVisible(false);
        }
      } catch (error) {
        console.error('Error creating order:', error);

        // Show more specific error message if available
        const errorMessage = error.response?.data?.message ||
                            error.message ||
                            "An error occurred while placing your order. Please try again.";

        Alert.alert(
          "Order Error",
          errorMessage
        );
        setConfirmModalVisible(false);
      } finally {
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error in order confirmation process:', error);
      Alert.alert(
        "Error",
        "An error occurred while processing your order. Please try again."
      );
      setConfirmModalVisible(false);
      setIsLoading(false);
    }
  };

  const handleSuccessClose = () => {
    animateOut(() => {
      setSuccessModalVisible(false);
      navigation.navigate("Orders", { newOrderId: newOrderData._id });
    });
  };

  // Navigation to add address is handled directly in the UI

  return (
    <View className="flex-1 bg-gray-50">
      {/* Modern Header - Adjusted Height */}
      <View className="bg-madder h-28 rounded-b-3xl shadow-md">
        <View className="flex-row justify-between items-center p-4 pt-10 h-full">
          <View className="flex-row items-center gap-3">
            <TouchableOpacity
              className="bg-white/20 p-2 rounded-full"
              onPress={() => navigation.goBack()}
            >
              <MaterialIcons name="arrow-back" size={22} color="white" />
            </TouchableOpacity>
            <Text className="text-xl text-white font-bold">Checkout</Text>
          </View>
        </View>
      </View>

      {/* Amount to pay section - Modern Design */}
      <View className="bg-white mx-4 p-4 rounded-2xl shadow-md mt-3 z-10 border border-gray-100">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-gray-500 text-xs">Amount to pay</Text>
            <Text className="text-base font-bold text-gray-800">{`₹${calculateFinalTotal()}`}</Text>
          </View>
          <View className="bg-madder/10 px-3 py-1.5 rounded-full">
            <Text className="text-madder font-medium text-sm">{cartItems.length} {cartItems.length === 1 ? 'item' : 'items'}</Text>
          </View>
        </View>
      </View>

      {/* Main Content */}
      <ScrollView
        className="flex-1 mx-4 mt-4"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 70 }}
      >
        {/* Payment Method Section - Clean Design */}
        <View className="mb-4">
          <View className="flex-row items-center mb-3">
            <View className="w-8 h-8 bg-madder/10 rounded-full items-center justify-center mr-2">
              <MaterialIcons name="payment" size={16} color="#A31621" />
            </View>
            <Text className="text-base font-bold text-gray-800">Payment Method</Text>
          </View>

          {/* UPI Payment Option - Disabled */}
          <View className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 mb-3">
            <TouchableOpacity
              className="flex-row items-center p-4 bg-gray-50"
              disabled={true}
            >
              <View className="w-8 h-8 mr-3 bg-gray-200 rounded-full items-center justify-center">
                <MaterialIcons name="payment" size={18} color="#9CA3AF" />
              </View>
              <View className="flex-1">
                <Text className="text-gray-500 text-sm font-medium">UPI Payment</Text>
                <Text className="text-gray-400 text-xs">Currently unavailable</Text>
              </View>
              <View className="h-5 w-5 rounded-full border border-gray-300 items-center justify-center bg-gray-100">
                <MaterialIcons name="block" size={12} color="#9CA3AF" />
              </View>
            </TouchableOpacity>
          </View>

          {/* COD Payment Option - Active */}
          <View className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
            <TouchableOpacity
              className={`flex-row items-center p-4 ${selectedPayment.id === "cod" ? 'bg-madder/5' : ''}`}
              onPress={() => setSelectedPayment(PAYMENT_METHODS[1])}
            >
              <View className="w-8 h-8 mr-3 bg-madder/10 rounded-full items-center justify-center">
                <MaterialIcons name="local-atm" size={18} color="#A31621" />
              </View>
              <View className="flex-1">
                <Text className="text-gray-800 text-sm font-medium">Cash on Delivery</Text>
                <Text className="text-gray-500 text-xs">Pay when you receive your order</Text>
              </View>
              <View className="h-5 w-5 rounded-full border border-gray-300 items-center justify-center">
                {selectedPayment.id === "cod" && (
                  <View className="h-3 w-3 rounded-full bg-madder" />
                )}
              </View>
            </TouchableOpacity>

            {/* COD Payment Info - Show when COD is selected */}
            {selectedPayment.id === "cod" && (
              <View className="p-3 bg-madder/5 border-t border-madder/10">
                <Text className="text-gray-600 text-xs">
                  Pay via cash or UPI to delivery partner
                </Text>
              </View>
            )}
          </View>
        </View>



        {/* Delivery Address Section - Modern Design */}
        <View className="mb-4">
          <View className="flex-row items-center justify-between mb-3">
            <View className="flex-row items-center">
              <View className="w-8 h-8 bg-madder/10 rounded-full items-center justify-center mr-2">
                <MaterialIcons name="location-on" size={16} color="#A31621" />
              </View>
              <Text className="text-base font-bold text-gray-800">Delivery Address</Text>
            </View>
            <TouchableOpacity
              onPress={() => navigation.navigate('AddressScreen')}
              className="bg-madder/10 px-2 py-1 rounded-lg"
            >
              <Text className="text-madder text-xs font-medium">Manage</Text>
            </TouchableOpacity>
          </View>

          {addressesLoading ? (
            <View className="bg-white rounded-2xl shadow-sm p-4 items-center justify-center border border-gray-100" style={{ minHeight: 150 }}>
              <ActivityIndicator size="small" color="#A31621" />
              <Text className="text-gray-500 text-sm mt-3">Loading addresses...</Text>
            </View>
          ) : currentUser ? (
            <View>
              {/* Horizontal scrollable address list */}
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="mb-2"
                contentContainerStyle={{ paddingRight: 20 }}
              >
                {getCombinedAddresses(currentUser, addresses).map((address, index) => (
                  <TouchableOpacity
                    key={address._id || index}
                    className={`bg-white rounded-2xl shadow-sm p-3 mr-3 ${
                      (selectedAddress && (
                        (selectedAddress._id && address._id && selectedAddress._id === address._id) ||
                        (selectedAddress.id && address.id && selectedAddress.id === address.id)
                      )) || (!selectedAddress && address.isMain)
                      ? 'border-2 border-madder' : 'border border-gray-100'
                    }`}
                    style={{ width: 250 }}
                    onPress={() => setSelectedAddress(address)}
                  >
                    <View className="flex-row justify-between items-start mb-2">
                      <View className="flex-row items-center">
                        <View className={`w-7 h-7 rounded-full ${address.isMain ? 'bg-madder' : 'bg-madder/10'} items-center justify-center mr-2`}>
                          <MaterialIcons
                            name={
                              address.isMain ? 'star' :
                              address.type === 'Work' ? 'work' :
                              address.type === 'Other' ? 'location-on' : 'home'
                            }
                            size={14}
                            color={address.isMain ? "white" : "#A31621"}
                          />
                        </View>
                        <Text className="text-sm font-bold">{address.type || 'Home'}</Text>
                      </View>
                      {address.isMain && (
                        <View className="bg-madder px-2 py-0.5 rounded-md">
                          <Text className="text-white text-xs">Primary</Text>
                        </View>
                      )}
                      {address.isDefault && !address.isMain && (
                        <View className="bg-madder/10 px-2 py-0.5 rounded-md">
                          <Text className="text-madder text-xs">Default</Text>
                        </View>
                      )}
                    </View>
                    <Text className="text-gray-700 text-xs">{currentUser?.name || 'User'}</Text>
                    <Text className="text-gray-500 text-xs">{currentUser?.number || ''}</Text>

                    {/* Handle both structured and legacy address formats */}
                    {address.doorNo ? (
                      <Text className="text-gray-700 text-xs mt-1 leading-4">
                        {address.doorNo}{address.streetName ? `, ${address.streetName}` : ''}
                        {address.area ? `, ${address.area}` : ''}
                        {address.district ? `, ${address.district}` : ''}
                        {address.pincode ? ` - ${address.pincode}` : ''}
                      </Text>
                    ) : address.fullAddress ? (
                      <Text className="text-gray-700 text-xs mt-1 leading-4">{address.fullAddress}</Text>
                    ) : (
                      <Text className="text-gray-700 text-xs mt-1 leading-4">
                        {address.address || ''}{address.city ? `, ${address.city}` : ''}
                        {address.state ? `, ${address.state}` : ''}
                        {address.pincode ? ` - ${address.pincode}` : ''}
                      </Text>
                    )}
                  </TouchableOpacity>
                ))}

                {/* Add new address card */}
                <TouchableOpacity
                  className="bg-white rounded-2xl shadow-sm p-3 mr-3 border border-dashed border-gray-300 items-center justify-center"
                  style={{ width: 120, minHeight: 120 }}
                  onPress={navigateToDeliveryAddressScreen}
                >
                  <View className="w-10 h-10 rounded-full bg-madder/10 items-center justify-center mb-2">
                    <MaterialIcons name="add-location" size={18} color="#A31621" />
                  </View>
                  <Text className="text-madder text-xs font-medium text-center">Add New Address</Text>
                </TouchableOpacity>
              </ScrollView>

              {/* Selected address summary */}
              {selectedAddress && (
                <View className="bg-snow rounded-lg p-3 mt-2 border border-madder/20">
                  <View className="flex-row items-center">
                    <MaterialIcons name="check-circle" size={14} color="#A31621" />
                    <Text className="font-medium text-gray-800 text-xs ml-1">Delivering to:</Text>
                  </View>
                  <Text className="text-madder text-sm font-medium mt-1">
                    {selectedAddress.type || 'Home'}
                  </Text>
                  {selectedAddress.doorNo ? (
                    <Text className="text-gray-700 text-xs">
                      {selectedAddress.doorNo}
                      {selectedAddress.streetName ? `, ${selectedAddress.streetName}` : ''}
                      {selectedAddress.area ? `, ${selectedAddress.area}` : ''}
                      {selectedAddress.district ? `, ${selectedAddress.district}` : ''}
                      {selectedAddress.pincode ? ` - ${selectedAddress.pincode}` : ''}
                    </Text>
                  ) : selectedAddress.fullAddress ? (
                    <Text className="text-gray-700 text-xs">{selectedAddress.fullAddress}</Text>
                  ) : (
                    <Text className="text-gray-700 text-xs">
                      {selectedAddress.address || ''}
                      {selectedAddress.city ? `, ${selectedAddress.city}` : ''}
                      {selectedAddress.state ? `, ${selectedAddress.state}` : ''}
                      {selectedAddress.pincode ? ` - ${selectedAddress.pincode}` : ''}
                    </Text>
                  )}
                </View>
              )}
            </View>
          ) : (
            <View className="bg-white rounded-2xl shadow-sm p-4 items-center justify-center border border-gray-100">
              <Text className="text-gray-500 text-xs mb-2">No address found</Text>
              <TouchableOpacity
                className="bg-madder py-2 px-4 rounded-lg"
                onPress={navigateToDeliveryAddressScreen}
              >
                <Text className="text-white text-xs font-medium">Add New Address</Text>
              </TouchableOpacity>
              <Text className="text-gray-500 text-xs mt-2">Uses precise location for delivery</Text>
            </View>
          )}
        </View>

        {/* Delivery Time Slot - Modern Design */}
        <View className="mb-4">
          <View className="flex-row items-center mb-3">
            <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-2">
              <MaterialIcons name="access-time" size={16} color="#3B82F6" />
            </View>
            <Text className="text-base font-bold text-gray-800">Delivery Time</Text>
          </View>
          <View className="bg-white rounded-2xl shadow-sm p-4 border border-gray-100">
            <View className="mb-3">
              <Text className="font-medium text-sm text-gray-800">Choose Delivery Slot</Text>
            </View>

            {/* Standard delivery slots */}
              <View>
                {/* Today's slots */}
                <View className="mb-3">
                  <View className="flex-row items-center mb-2">
                    <MaterialIcons name="today" size={14} color="#4B5563" />
                    <Text className="text-gray-700 text-xs font-medium ml-1">
                      Today -{" "}
                      {new Date().toLocaleDateString("en-IN", {
                        day: "numeric",
                        month: "short",
                      })}
                    </Text>
                  </View>

                  {availableSlots && availableSlots.filter(slot => slot.day === "Today").length > 0 ? (
                    <View className="flex-row space-x-2">
                      {availableSlots
                        .filter(slot => slot.day === "Today")
                        .map(slot => (
                          <TouchableOpacity
                            key={slot.id}
                            className={`flex-1 border rounded-lg p-2 ${
                              selectedSlot && selectedSlot.id === slot.id
                                ? "bg-blue-50 border-blue-300"
                                : "border-gray-200"
                            }`}
                            onPress={() => setSelectedSlot(slot)}
                          >
                            <Text
                              className={`text-center text-xs font-medium ${
                                selectedSlot && selectedSlot.id === slot.id
                                  ? "text-blue-600"
                                  : "text-gray-700"
                              }`}
                            >
                              {slot.label}
                            </Text>
                            <Text
                              className={`text-center text-xs mt-0.5 ${
                                selectedSlot && selectedSlot.id === slot.id
                                  ? "text-blue-600"
                                  : "text-gray-500"
                              }`}
                            >
                              {slot.time}
                            </Text>
                          </TouchableOpacity>
                        ))}
                    </View>
                  ) : (
                    <View className="bg-gray-50 p-2 rounded-lg border border-gray-100">
                      <Text className="text-gray-500 text-xs text-center">
                        No slots available for today
                      </Text>
                    </View>
                  )}
                </View>

                {/* Tomorrow's slots */}
                <View>
                  <View className="flex-row items-center mb-2">
                    <MaterialIcons name="event" size={14} color="#4B5563" />
                    <Text className="text-gray-700 text-xs font-medium ml-1">
                      Tomorrow -{" "}
                      {new Date(Date.now() + 86400000).toLocaleDateString(
                        "en-IN",
                        { day: "numeric", month: "short" }
                      )}
                    </Text>
                  </View>

                  {availableSlots && availableSlots.filter(slot => slot.day === "Tomorrow").length > 0 ? (
                    <View className="flex-row space-x-2">
                      {availableSlots
                        .filter(slot => slot.day === "Tomorrow")
                        .map(slot => (
                          <TouchableOpacity
                            key={slot.id}
                            className={`flex-1 border rounded-lg p-2 ${
                              selectedSlot && selectedSlot.id === slot.id
                                ? "bg-blue-50 border-blue-300"
                                : "border-gray-200"
                            }`}
                            onPress={() => setSelectedSlot(slot)}
                          >
                            <Text
                              className={`text-center text-xs font-medium ${
                                selectedSlot && selectedSlot.id === slot.id
                                  ? "text-blue-600"
                                  : "text-gray-700"
                              }`}
                            >
                              {slot.label}
                            </Text>
                            <Text
                              className={`text-center text-xs mt-0.5 ${
                                selectedSlot && selectedSlot.id === slot.id
                                  ? "text-blue-600"
                                  : "text-gray-500"
                              }`}
                            >
                              {slot.time}
                            </Text>
                          </TouchableOpacity>
                        ))}
                    </View>
                  ) : (
                    <View className="bg-gray-50 p-2 rounded-lg border border-gray-100">
                      <Text className="text-gray-500 text-xs text-center">
                        No slots available for tomorrow
                      </Text>
                    </View>
                  )}
                </View>
              </View>
          </View>
        </View>

        {/* Order Summary - Modern Design */}
        <View className="mb-4">
          <View className="flex-row items-center mb-3">
            <View className="w-8 h-8 bg-gray-100 rounded-full items-center justify-center mr-2">
              <MaterialIcons name="receipt" size={16} color="#4B5563" />
            </View>
            <Text className="text-base font-bold text-gray-800">Bill Details</Text>
          </View>
          <View className="bg-white rounded-2xl shadow-sm p-4 border border-gray-100">
            <View className="border-b border-gray-100 pb-3 mb-3">
              <Text className="font-medium text-sm mb-2 text-gray-800">Items ({cartItems.length})</Text>
              {cartItems.map((item, index) => (
                <View
                  key={`checkout-item-${item.id}-${index}`}
                  className="flex-row items-center mb-1.5"
                >
                  <Text className="text-gray-600 flex-1 text-xs">
                    {item.name} ({item.selectedWeight} × {item.quantity})
                  </Text>
                  <Text className="font-medium text-xs">₹{item.totalPrice}</Text>
                </View>
              ))}
            </View>

            <View className="space-y-2">
              <View className="flex-row justify-between items-center">
                <Text className="text-gray-600 text-xs">Item Total</Text>
                <Text className="font-medium text-xs text-gray-800">₹{calculateTotal()}</Text>
              </View>
              <View className="flex-row justify-between items-center my-2">
                <View className="flex-row items-center">
                  <View className="w-6 h-6 bg-blue-50 rounded-full items-center justify-center mr-2">
                    <MaterialIcons name="local-shipping" size={16} color="#3B82F6" />
                  </View>
                  <Text className="text-gray-600 text-xs">Delivery Fee</Text>
                </View>
                {calculateTotal() >= 499 ? (
                  <View className="flex-row items-center">
                    <Text className="font-medium line-through text-gray-400 text-xs mr-1">
                      ₹49
                    </Text>
                    <View className="bg-blue-50 px-2 py-0.5 rounded-md">
                      <Text className="font-medium text-blue-700 text-xs">FREE</Text>
                    </View>
                  </View>
                ) : (
                  <Text className="font-medium text-xs text-gray-800">
                    ₹49
                  </Text>
                )}
              </View>

              {/* Display coupon discount if available */}
              {couponDiscount > 0 && (
                <View className="flex-row justify-between items-center">
                  <View className="flex-row items-center">
                    <View className="w-6 h-6 bg-indigo-50 rounded-full items-center justify-center mr-2">
                      <MaterialIcons name="local-offer" size={16} color="#6366F1" />
                    </View>
                    <Text className="text-gray-600 text-xs">
                      Promo Code {appliedCoupon ? `(${appliedCoupon.code})` : ''}
                    </Text>
                  </View>
                  <Text className="text-green-600 font-medium text-xs">
                    -₹{couponDiscount}
                  </Text>
                </View>
              )}
              {/* Display potential coins discount */}
              {coinsToApply > 0 && (
                <View className="flex-row justify-between items-center">
                  <View className="flex-row items-center">
                    <View className="w-6 h-6 bg-amber-50 rounded-full items-center justify-center mr-2">
                      <MaterialIcons name="account-balance-wallet" size={16} color="#F59E0B" />
                    </View>
                    <Text className="text-gray-600 text-xs">Reward Coins</Text>
                  </View>
                  <Text className="text-green-600 font-medium text-xs">
                    -₹{coinsToApply}
                  </Text>
                </View>
              )}
              <View className="h-px bg-gray-100 my-2" />
              <View className="flex-row justify-between items-center">
                <Text className="text-gray-800 font-bold text-sm">To Pay</Text>
                <Text className="font-bold text-madder text-base">
                  ₹{calculateFinalTotal()}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView> {/* Ensure ScrollView is closed before Fixed Bottom Bar */}

      {/* Fixed Bottom Bar - Modern Design */}
      <View className="absolute bottom-0 left-0 right-0 bg-white p-3 border-t border-gray-100 shadow-md"
            style={{ elevation: 8, zIndex: 1000, backgroundColor: 'white', borderTopWidth: 1, borderTopColor: '#F3F4F6' }}>
        <View className="flex-row items-center justify-between">
          <View>
            <Text className="text-gray-500 text-xs">Total</Text>
            <Text className="text-lg font-bold text-gray-800">
              ₹{calculateFinalTotal()}
            </Text>
          </View>

          <TouchableOpacity
            className="bg-madder px-5 py-2.5 rounded-lg flex-row items-center shadow-sm"
            onPress={handlePlaceOrder}
          >
            <Text className="text-white font-medium text-sm mr-1.5">Place Order</Text>
            <MaterialIcons name="chevron-right" size={16} color="white" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Confirm Order Modal - Modern Design */}
      <Modal
        transparent={true}
        visible={confirmModalVisible}
        animationType="none"
        onRequestClose={() => setConfirmModalVisible(false)}
      >
        <View className="flex-1 bg-black/50 justify-center items-center p-5">
          <Animated.View
            className="bg-white w-full rounded-2xl p-5 shadow-md"
            style={{
              opacity: opacityAnim,
              transform: [{ scale: scaleAnim }]
            }}
          >
            <View className="items-center mb-4">
              <View className="w-14 h-14 bg-madder/10 rounded-full items-center justify-center mb-3">
                <MaterialIcons name="shopping-cart" size={24} color="#A31621" />
              </View>
              <Text className="text-lg font-bold text-gray-800">Confirm Order</Text>
              <Text className="text-gray-500 text-xs text-center mt-1">
                Please confirm your order details
              </Text>
            </View>

            {/* Order details */}
            <View className="bg-gray-50 rounded-lg p-3 mb-4 border border-gray-100">
              <View className="flex-row justify-between mb-1">
                <Text className="text-gray-600 text-xs">Total Items:</Text>
                <Text className="font-medium text-xs">{cartItems.length}</Text>
              </View>
              <View className="flex-row justify-between mb-1">
                <Text className="text-gray-600 text-xs">Delivery:</Text>
                <Text className="font-medium text-xs">
                  {selectedSlot && selectedSlot.day && selectedSlot.time
                    ? `${selectedSlot.day}, ${selectedSlot.time}`
                    : "Standard Delivery"}
                </Text>
              </View>
              <View className="flex-row justify-between mb-1">
                <Text className="text-gray-600 text-xs">Payment:</Text>
                <Text className="font-medium text-xs">{selectedPayment.name}</Text>
              </View>
              <View className="h-px bg-gray-200 my-1.5" />
              <View className="flex-row justify-between">
                <Text className="text-gray-800 font-bold text-xs">Total Amount:</Text>
                <Text className="font-bold text-madder text-sm">
                  ₹{calculateFinalTotal()}
                </Text>
              </View>
            </View>

            <View className="flex-row space-x-3">
              <TouchableOpacity
                className="flex-1 py-2.5 bg-gray-100 rounded-lg"
                onPress={() => animateOut(() => setConfirmModalVisible(false))}
              >
                <Text className="text-gray-800 text-sm font-medium text-center">Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className="flex-1 py-2.5 bg-madder rounded-lg"
                onPress={confirmOrder}
                disabled={isLoading}
              >
                {isLoading ? (
                  <View className="flex-row justify-center items-center">
                    <ActivityIndicator size="small" color="white" />
                    <Text className="text-white text-sm font-medium ml-2">Processing...</Text>
                  </View>
                ) : (
                  <Text className="text-white text-sm font-medium text-center">Confirm</Text>
                )}
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>

      {/* Success Modal - Modern Design */}
      <Modal
        transparent={true}
        visible={successModalVisible}
        animationType="none"
        onRequestClose={() => setSuccessModalVisible(false)}
      >
        <View className="flex-1 bg-black/50 justify-center items-center p-5">
          <Animated.View
            className="bg-white w-full rounded-2xl p-5 shadow-md"
            style={{
              opacity: opacityAnim,
              transform: [{ scale: scaleAnim }]
            }}
          >
            <View className="items-center mb-4">
              <View className="w-14 h-14 bg-green-50 rounded-full items-center justify-center mb-3">
                <MaterialIcons name="check-circle" size={24} color="#10B981" />
              </View>
              <Text className="text-lg font-bold text-gray-800">Order Placed!</Text>
              <Text className="text-gray-500 text-xs text-center mt-1">
                Your order has been placed successfully
              </Text>
            </View>

            {/* Order details */}
            {newOrderData && (
              <View className="bg-gray-50 rounded-lg p-3 mb-4 border border-gray-100">
                <View className="flex-row justify-between mb-1">
                  <Text className="text-gray-600 text-xs">Order Number:</Text>
                  <Text className="font-medium text-xs">#{newOrderData.orderNumber}</Text>
                </View>
                <View className="flex-row justify-between mb-1">
                  <Text className="text-gray-600 text-xs">Total Amount:</Text>
                  <Text className="font-medium text-xs">₹{newOrderData.totalAmount || newOrderData.total}</Text>
                </View>
                {/* Amount in success modal: total, totalAmount, displayedAmount */}
                <View className="flex-row justify-between items-start">
                  <Text className="text-gray-600 text-xs">Expected Delivery:</Text>
                  <View className="items-end">
                    {typeof newOrderData.expectedDelivery === 'string' ? (
                      <Text className="font-medium text-xs">{newOrderData.expectedDelivery}</Text>
                    ) : (
                      <>
                        <Text className="font-medium text-xs">{newOrderData.expectedDelivery.formattedDate}</Text>
                        <Text className="font-medium text-xs text-madder">{newOrderData.expectedDelivery.time}</Text>
                      </>
                    )}
                  </View>
                </View>
              </View>
            )}

            <TouchableOpacity
              className="py-2.5 bg-madder rounded-lg"
              onPress={handleSuccessClose}
            >
              <Text className="text-white text-sm font-medium text-center">View Order</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

export default CheckoutScreen;
