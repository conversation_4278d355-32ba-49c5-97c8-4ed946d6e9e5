import { View, Text, Image } from 'react-native';
import React, { useEffect, useState } from 'react';
import PopupAlert from '../components/PopupAlert';

const MainTabs = ({ route }) => {
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');
    const { successMessage } = route.params;

    useEffect(() => {
        if (successMessage) {
            setAlertMessage(successMessage);
            setShowAlert(true);
        }
    }, [successMessage]);

    return (
        <View className="flex-1 bg-snow">
            <View className="flex-1 items-center justify-center">
                <Image
                    source={require('../assets/logo.png')}
                    className="w-32 h-32 mb-8"
                    resizeMode="contain"
                    style={{ backgroundColor: 'transparent' }}
                />
                <Text className="text-2xl font-bold text-gray-800 mb-2">
                    Welcome to Meat Now
                </Text>
                <Text className="text-gray-500 text-center">
                    You have successfully logged in!
                </Text>
            </View>
            <PopupAlert
                visible={showAlert}
                message={alertMessage}
                type='success'
                onClose={() => setShowAlert(false)}
            />
        </View>
    );
};

export default MainTabs;
