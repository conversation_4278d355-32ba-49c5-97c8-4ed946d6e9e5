import axios from 'axios';
import { API_URL } from '../../config/constants';
import { getAuthToken } from '../authStorage';

// Get current user profile
export const getUserProfile = async () => {
    try {
        const token = await getAuthToken();

        // If no token, return early with a specific error
        if (!token) {
            console.log('No auth token available, user not logged in');
            return { user: null, message: 'User not authenticated' };
        }

        try {
            console.log(`Fetching user profile from database: ${API_URL}/users/profile`);

            // Add cache-busting parameter to ensure fresh data
            const timestamp = new Date().getTime();
            const response = await axios.get(`${API_URL}/users/profile?_=${timestamp}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                },
                timeout: 15000 // Increased timeout to 15 seconds
            });

            console.log('User profile data received from database:', response.data);
            return response.data;
        } catch (apiError) {
            // Check if it's a 401 or 403 error (authentication issues)
            if (apiError.response && (apiError.response.status === 401 || apiError.response.status === 403)) {
                console.log('Authentication error fetching profile:', apiError.response.status);
                return { user: null, message: 'Authentication failed' };
            }

            // For 404 errors, the user might not exist in the database yet
            if (apiError.response && apiError.response.status === 404) {
                console.log('User profile not found (404)');
                return { user: null, message: 'User profile not found' };
            }

            // For other errors, log and rethrow
            console.error('API error fetching user profile:', apiError);
            throw apiError;
        }
    } catch (error) {
        console.error('Error in getUserProfile function:', error);
        throw error;
    }
};

// Update user profile
export const updateUserProfile = async (profileData) => {
    try {
        const token = await getAuthToken();
        console.log('Updating user profile with endpoint:', `${API_URL}/users/profile`);
        console.log('Profile data:', JSON.stringify(profileData, null, 2));

        // If updating address, validate required fields
        if (profileData.address) {
            const requiredFields = ['doorNo', 'streetName', 'area', 'district', 'pincode'];
            const missingFields = requiredFields.filter(field =>
                !profileData.address[field] ||
                (typeof profileData.address[field] === 'string' && profileData.address[field].trim() === '')
            );

            if (missingFields.length > 0) {
                console.error('Missing required address fields in profile update:', missingFields);
                throw new Error(`Missing required address fields: ${missingFields.join(', ')}`);
            }
        }

        const response = await axios.put(`${API_URL}/users/profile`, profileData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            timeout: 15000 // Increased timeout to 15 seconds
        });

        console.log('Profile update response:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error updating user profile:', error);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
        throw error;
    }
};

// Add a new address
export const addUserAddress = async (addressData) => {
    try {
        const token = await getAuthToken();

        // Validate required fields before sending to API
        const requiredFields = ['doorNo', 'streetName', 'area', 'district', 'pincode'];
        const missingFields = requiredFields.filter(field => !addressData[field] || addressData[field].trim() === '');

        if (missingFields.length > 0) {
            console.error('Missing required fields:', missingFields);
            throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Always allow duplicate addresses
        addressData.allowDuplicate = true;

        console.log('Sending address data to API:', JSON.stringify(addressData, null, 2));

        const response = await axios.post(`${API_URL}/user/addresses`, addressData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            timeout: 15000 // Increased timeout to 15 seconds
        });

        console.log('Address added successfully:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error adding user address:', error);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
        throw error;
    }
};

// Get all user addresses
export const getUserAddresses = async () => {
    try {
        const token = await getAuthToken();

        // If no token, return early with a specific error
        if (!token) {
            console.log('No auth token available for addresses, user not logged in');
            return { addresses: [], message: 'User not authenticated' };
        }

        try {
            console.log('Fetching addresses from database...');

            // Add cache-busting parameter to ensure fresh data
            const timestamp = new Date().getTime();
            const response = await axios.get(`${API_URL}/user/addresses?_=${timestamp}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                },
                timeout: 15000 // Increased timeout to 15 seconds
            });

            console.log('Addresses fetched from database:', response.data);
            return response.data;
        } catch (apiError) {
            // Check if it's a 401 or 403 error (authentication issues)
            if (apiError.response && (apiError.response.status === 401 || apiError.response.status === 403)) {
                console.log('Authentication error fetching addresses:', apiError.response.status);
                return { addresses: [], message: 'Authentication failed' };
            }

            // For 404 errors, the user might not have any addresses yet
            if (apiError.response && apiError.response.status === 404) {
                console.log('User addresses not found (404)');
                return { addresses: [], message: 'No addresses found' };
            }

            // For other errors, log and rethrow
            console.error('API error fetching user addresses:', apiError);
            throw apiError;
        }
    } catch (error) {
        console.error('Error in getUserAddresses function:', error);
        throw error;
    }
};

// Update an existing address
export const updateUserAddress = async (addressId, addressData) => {
    try {
        const token = await getAuthToken();

        // Validate required fields before sending to API
        const requiredFields = ['doorNo', 'streetName', 'area', 'district', 'pincode'];
        const missingFields = requiredFields.filter(field => !addressData[field] || addressData[field].trim() === '');

        if (missingFields.length > 0) {
            console.error('Missing required fields:', missingFields);
            throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

        console.log(`Updating address ${addressId} with data:`, JSON.stringify(addressData, null, 2));

        // Make sure we're using the correct API endpoint
        const response = await axios.put(`${API_URL}/user/addresses/${addressId}`, addressData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            timeout: 15000 // Increased timeout to 15 seconds
        });

        console.log('Address updated successfully:', response.data);
        return response.data;
    } catch (error) {
        console.error(`Error updating address ${addressId}:`, error);

        // If we get a 404, the address might not exist
        if (error.response && error.response.status === 404) {
            console.error(`Address ${addressId} not found`);
            throw new Error(`Address with ID ${addressId} not found`);
        }

        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
        throw error;
    }
};

// Delete an address
export const deleteUserAddress = async (addressId) => {
    try {
        const token = await getAuthToken();
        const response = await axios.delete(`${API_URL}/user/addresses/${addressId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        return response.data;
    } catch (error) {
        console.error(`Error deleting address ${addressId}:`, error);
        throw error;
    }
};

// Cache for coin data to prevent excessive API calls
let coinCache = {
    data: null,
    timestamp: 0
};

// Get user coins/rewards with enhanced coin history and caching
export const getUserCoins = async (timestamp = null) => {
    try {
        // Check if we have cached data that's less than 30 seconds old
        const now = new Date().getTime();
        const cacheAge = now - coinCache.timestamp;

        // Use cache if it's less than 30 seconds old and we have data
        if (coinCache.data && cacheAge < 30000 && !timestamp) {
            console.log('Using cached coin data, age:', cacheAge, 'ms');
            return coinCache.data;
        }

        const token = await getAuthToken();
        console.log('Fetching user coins and history from database...');

        // Add cache-busting parameter only if explicitly provided
        const cacheBuster = timestamp || now;
        const response = await axios.get(`${API_URL}/users/coins?_=${cacheBuster}`, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            timeout: 10000 // 10 second timeout
        });

        console.log('Coins and history fetched from database:', response.data);

        // The response now includes:
        // - totalCoins: Total coins balance
        // - activeCoins: Currently active (not expired) coins
        // - usedCoins: Total coins used
        // - expiredCoins: Total expired coins
        // - refundedCoins: Total refunded coins
        // - coinsHistory: Detailed history of all coin transactions

        // Update the cache with the new data
        coinCache = {
            data: response.data,
            timestamp: now
        };

        return response.data;
    } catch (error) {
        console.error('Error fetching user coins:', error);

        // If we have cached data, use it even if it's older than 30 seconds
        if (coinCache.data) {
            console.log('API error, using cached coin data as fallback');
            return coinCache.data;
        }

        // Return empty data structure on error to prevent app crashes
        const emptyData = {
            totalCoins: 0,
            activeCoins: 0,
            usedCoins: 0,
            expiredCoins: 0,
            refundedCoins: 0,
            coinsHistory: []
        };

        // Cache the empty data to prevent repeated API calls on error
        coinCache = {
            data: emptyData,
            timestamp: new Date().getTime()
        };

        return emptyData;
    }
};

// Use coins for discount
export const useCoinsForDiscount = async (coinsToUse, orderId = null) => {
    try {
        const token = await getAuthToken();
        const response = await axios.post(`${API_URL}/user/coins/use`,
            { coinsToUse, orderId },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000 // 10 second timeout
            }
        );

        // Invalidate the coin cache since we've used coins
        coinCache = {
            data: null,
            timestamp: 0
        };

        return {
            success: true,
            discountAmount: response.data.discountAmount,
            remainingCoins: response.data.remainingCoins,
            coinsUsed: response.data.coinsUsed
        };
    } catch (error) {
        console.error('Error using coins for discount:', error);
        return {
            success: false,
            error: error.response?.data?.message || error.message || 'Failed to use coins'
        };
    }
};

// Restore previously used coins
export const restoreCoins = async () => {
    try {
        console.log('Attempting to restore coins...');
        const token = await getAuthToken();

        // Log the API URL for debugging
        console.log(`Calling API endpoint: ${API_URL}/user/coins/restore`);

        const response = await axios.post(`${API_URL}/user/coins/restore`,
            {},
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000 // 10 second timeout
            }
        );

        console.log('Restore coins response:', response.data);

        // Invalidate the coin cache since we've restored coins
        coinCache = {
            data: null,
            timestamp: 0
        };

        return {
            success: true,
            restoredCoins: response.data.restoredCoins || 0,
            availableCoins: response.data.availableCoins || 0
        };
    } catch (error) {
        console.error('Error restoring coins:', error);

        // If there's a 404 error, it means the endpoint doesn't exist or isn't properly registered
        if (error.response && error.response.status === 404) {
            console.log('Endpoint not found. Attempting fallback method...');

            // Fallback: Just return success with 0 coins restored
            return {
                success: true,
                restoredCoins: 0,
                availableCoins: 0,
                message: 'No coins to restore'
            };
        }

        // If there's a 400 error, it means there are no coins to restore
        if (error.response && error.response.status === 400) {
            console.log('No coins to restore:', error.response.data);
            return {
                success: true,
                restoredCoins: 0,
                availableCoins: 0,
                message: error.response.data.message || 'No coins to restore'
            };
        }

        return {
            success: false,
            error: error.response?.data?.message || error.message || 'Failed to restore coins'
        };
    }
};
