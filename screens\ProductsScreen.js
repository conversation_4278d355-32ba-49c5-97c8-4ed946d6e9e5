import { View, Text, ScrollView, Image, TouchableOpacity, StatusBar, RefreshControl } from 'react-native';
import React, { useState, useEffect } from 'react';
import { MaterialIcons } from "@expo/vector-icons";
import { getAllCategories } from '../utils/api/categoryApi';

// Simple static loading placeholder
const CategorySkeleton = () => {
    return (
        <View className="rounded-2xl h-32 w-32 items-center justify-center m-1">
            <View
                className="w-20 h-20 rounded-full bg-gray-200 mb-2"
            />
            <View
                className="w-15 h-4 bg-gray-200 rounded"
            />
        </View>
    );
};

const ProductsScreen = ({ navigation }) => {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [error, setError] = useState(null);

    // Function to fetch categories from the database
    const fetchCategories = async () => {
        try {
            setLoading(true);
            setError(null);
            console.log('Fetching categories from database...');

            const categoriesData = await getAllCategories();
            console.log('Categories fetched:', categoriesData);

            if (Array.isArray(categoriesData)) {
                setCategories(categoriesData);
            } else {
                console.log('Invalid categories data format:', categoriesData);
                setCategories([]);
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
            setError('Failed to load categories. Please try again.');
            setCategories([]);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    // Fetch categories when component mounts
    useEffect(() => {
        fetchCategories();
    }, []);

    // Handle refresh
    const handleRefresh = () => {
        setRefreshing(true);
        fetchCategories();
    };

    // Handle category click
    const handleCategoryClick = (category) => {
        console.log('Navigating to category:', category);
        navigation.navigate('CategoryScreen', { category });
    };

    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" />
            {/* Header */}
            <View className="bg-madder flex-row h-24 rounded-b-3xl p-6 pt-10 items-center">
                <View className="flex-row items-center gap-6">
                    <TouchableOpacity onPress={() => navigation.goBack()}>
                        <MaterialIcons name="arrow-back" size={20} color="white" />
                    </TouchableOpacity>
                    <Text className="text-2xl text-white font-bold">Categories</Text>
                </View>
            </View>

            <ScrollView
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={["#A31621"]}
                        tintColor="#A31621"
                        title="Refreshing categories..."
                        titleColor="#A31621"
                    />
                }
            >
                {error && (
                    <View className="p-4 m-4 bg-red-100 rounded-lg">
                        <Text className="text-red-700 text-center">{error}</Text>
                        <TouchableOpacity
                            className="mt-2 bg-madder py-2 rounded-lg"
                            onPress={handleRefresh}
                        >
                            <Text className="text-white text-center font-medium">Try Again</Text>
                        </TouchableOpacity>
                    </View>
                )}

                <View className="p-8 flex-row flex-wrap gap-2 justify-center">
                    {loading ? (
                        // Skeleton loading for categories
                        Array.from({ length: 6 }).map((_, index) => (
                            <CategorySkeleton key={`skeleton-${index}`} />
                        ))
                    ) : categories.length > 0 ? (
                        categories.map((category) => (
                            <TouchableOpacity
                                key={category._id}
                                className="rounded-2xl h-32 w-32"
                                onPress={() => handleCategoryClick(category)}
                            >
                                <View className="flex justify-center items-center">
                                    <Image
                                        source={typeof category.image === 'string' ? { uri: category.image } : require('../assets/logo.png')}
                                        className="w-24 h-24 rounded-full"
                                        resizeMode="contain"
                                    />
                                </View>
                                <View className="flex-1 justify-center items-center">
                                    <Text className="text-black font-medium text-base">{category.name}</Text>
                                </View>
                            </TouchableOpacity>
                        ))
                    ) : (
                        <View className="flex-1 justify-center items-center py-20">
                            <MaterialIcons name="inventory" size={64} color="#9CA3AF" />
                            <Text className="text-black text-lg font-bold mt-4 text-center">
                                No Categories Found
                            </Text>
                            <Text className="text-gray-600 text-center mt-2">
                                Pull down to refresh or try again later
                            </Text>
                        </View>
                    )}
                </View>
            </ScrollView>
        </View>
    );
};

export default ProductsScreen;
