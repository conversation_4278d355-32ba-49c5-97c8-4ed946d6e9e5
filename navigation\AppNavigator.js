import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MaterialIcons } from '@expo/vector-icons';
import AdminContextWrapper from '../Components/AdminContextWrapper';
import { ActivityIndicator, View } from 'react-native';
import { useAuth } from '../context/AuthContext';

// Import Screens
import HomeScreen from '../screens/HomeScreen';
import ProductsScreen from '../screens/ProductsScreen';
import CartScreen from '../screens/CartScreen';
import OrdersScreen from '../screens/OrdersScreen';
import UserProfileScreen from '../screens/UserProfileScreen'; // New improved Profile Screen
import CheckoutScreen from '../screens/CheckoutScreen';
import SettingsScreen from '../screens/SettingsScreen';
import AuthScreen from '../screens/AuthScreen';
import ProductDetailScreen from '../screens/ProductDetailScreen';
import CategoryScreen from '../screens/CategoryScreen';
import PreLoginScreen from '../screens/PreLoginScreen';
import OTPScreen from '../screens/OTPScreen';
import UsernameScreen from '../screens/UsernameScreen';
import AddressDetailsScreen from '../screens/AddressDetailsScreen';
import EditProfileScreen from '../screens/EditProfileScreen'; // Add this import

// User Profile Screens
import AddressScreen from '../screens/AddressScreen';
import AddEditAddressScreen from '../screens/AddEditAddressScreen';
import CoinsScreen from '../screens/CoinsScreen';
import ContactUsScreen from '../screens/ContactUsScreen';
import FAQScreen from '../screens/FAQScreen';
import LanguageScreen from '../screens/LanguageScreen';

import DeliveryAddressScreen from '../screens/DeliveryAddressScreen';

// Admin Screen Imports
import DashboardScreen from '../screens/admin/DashboardScreen';
import AdminProductsScreen from '../screens/admin/AdminProductsScreen';
import AdminOrdersScreen from '../screens/admin/AdminOrdersScreen';
import UsersScreen from '../screens/admin/UsersScreen';
import UserDetailScreen from '../screens/admin/UserDetailScreen';
import AdminProfileScreen from '../screens/admin/AdminProfileScreen';
import DeliveryPartnerManagementScreen from '../screens/admin/DeliveryPartnerManagementScreen';
import DeliveryPartnerDetailScreen from '../screens/admin/DeliveryPartnerDetailScreen';
import EditDeliveryPartnerScreen from '../screens/admin/EditDeliveryPartnerScreen';
import EditProductScreen from '../screens/admin/EditProductScreen';
import AddProductScreen from '../screens/admin/AddProductScreen';

// Delivery Partner Screen Imports
import DeliveryPartnerDashboard from '../screens/delivery/DeliveryPartnerDashboard';
import DeliveryPartnerOrdersScreen from '../screens/delivery/DeliveryPartnerOrdersScreen';
import DeliveryPartnerProfileScreen from '../screens/delivery/DeliveryPartnerProfileScreen';

import DeliveryNotificationsSettingsScreen from '../screens/delivery/NotificationsSettingsScreen';
import NotificationsSettingsScreen from '../screens/NotificationsSettingsScreen';
import DeliveryHelpSupportScreen from '../screens/delivery/HelpSupportScreen';
import HelpSupportScreen from '../screens/HelpSupportScreen';
import PolicyScreen from '../screens/PolicyScreen';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// **Bottom Tab Navigator**

function TabNavigator() {
    return (
        <AdminContextWrapper>
            <Tab.Navigator
            screenOptions={({ route }) => ({
                tabBarIcon: ({ color, size }) => {
                    let iconName;
                    switch (route.name) {
                        case 'Home': iconName = 'home-filled'; break;
                        case 'Category': iconName = 'restaurant-menu'; break;
                        case 'Cart': iconName = 'shopping-basket'; break;
                        case 'Profile': iconName = 'account-circle'; break;
                        default: iconName = 'help';
                    }
                    return <MaterialIcons name={iconName} size={size} color={color} />;
                },
                tabBarActiveTintColor: '#A31621',
                tabBarInactiveTintColor: 'gray',
                headerShown: false,
                tabBarStyle: {
                    height: 60,
                    paddingBottom: 10,
                    backgroundColor: 'white',
                    borderTopWidth: 1,
                    borderTopColor: '#f1f1f1',
                },
                tabBarHideOnKeyboard: true,
                tabBarLabelStyle: {
                    fontSize: 12,
                    fontWeight: '500',
                    paddingBottom: 5,
                },
                lazy: false, // Disable lazy loading for smoother tab switching
                unmountOnBlur: false, // Keep screens mounted for faster switching
            })}
        >
            <Tab.Screen name="Home" component={HomeScreen} />
            <Tab.Screen name="Category" component={ProductsScreen} />
            <Tab.Screen name="Cart" component={CartScreen} />
            <Tab.Screen name="Profile" component={UserProfileScreen} />
            </Tab.Navigator>
        </AdminContextWrapper>
    );
}

// **Main App Navigator**

// Admin Tab Navigator
function AdminTabNavigator() {
    return (
        <AdminContextWrapper>
            <Tab.Navigator
            screenOptions={({ route }) => ({
                tabBarIcon: ({ color, size }) => {
                    let iconName;
                    switch (route.name) {
                        case 'Dashboard': iconName = 'space-dashboard'; break;
                        case 'Products': iconName = 'inventory-2'; break;
                        case 'Orders': iconName = 'receipt-long'; break;
                        case 'Users': iconName = 'supervisor-account'; break;
                        case 'Profile': iconName = 'admin-panel-settings'; break;
                        default: iconName = 'help';
                    }
                    return <MaterialIcons name={iconName} size={size} color={color} />;
                },
                tabBarActiveTintColor: '#A31621',
                tabBarInactiveTintColor: 'gray',
                headerShown: false,
                tabBarStyle: {
                    height: 60,
                    paddingBottom: 10,
                    backgroundColor: 'white',
                    borderTopWidth: 1,
                    borderTopColor: '#E5E7EB',
                },
                tabBarHideOnKeyboard: true,
                lazy: false,
                unmountOnBlur: false,
            })}
        >
            <Tab.Screen name="Dashboard" component={DashboardScreen} />
            <Tab.Screen name="Products" component={AdminProductsScreen} />
            <Tab.Screen name="Orders" component={AdminOrdersScreen} />
            <Tab.Screen name="Users" component={UsersScreen} />
            <Tab.Screen name="Profile" component={AdminProfileScreen} />
            </Tab.Navigator>
        </AdminContextWrapper>
    );
}

// Delivery Partner Tab Navigator
function DeliveryPartnerTabNavigator() {
    return (
        <AdminContextWrapper>
            <Tab.Navigator
            screenOptions={({ route }) => ({
                tabBarIcon: ({ color, size }) => {
                    let iconName;
                    switch (route.name) {
                        case 'Dashboard': iconName = 'delivery-dining'; break;
                        case 'Orders': iconName = 'local-shipping'; break;
                        case 'Profile': iconName = 'account-box'; break;
                        default: iconName = 'help';
                    }
                    return <MaterialIcons name={iconName} size={size} color={color} />;
                },
                tabBarActiveTintColor: '#A31621',
                tabBarInactiveTintColor: 'gray',
                headerShown: false,
                tabBarStyle: {
                    height: 60,
                    paddingBottom: 10,
                    backgroundColor: 'white',
                    borderTopWidth: 1,
                    borderTopColor: '#E5E7EB',
                },
                tabBarHideOnKeyboard: true,
                lazy: false,
                unmountOnBlur: false,
            })}
        >
            <Tab.Screen name="Dashboard" component={DeliveryPartnerDashboard} />
            <Tab.Screen name="Orders" component={DeliveryPartnerOrdersScreen} />
            <Tab.Screen name="Profile" component={DeliveryPartnerProfileScreen} />
            </Tab.Navigator>
        </AdminContextWrapper>
    );
}

// Update the main navigator
const AppNavigator = () => {
    const { isLoggedIn, userType, loading, isAdmin, isDeliveryPartner } = useAuth();

    // Show loading screen while checking authentication
    if (loading) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#FCF7F8' }}>
                <ActivityIndicator size="large" color="#A31621" />
            </View>
        );
    }

    // Determine the initial route based on authentication state
    const getInitialRouteName = () => {
        if (loading) {
            console.log("Loading: Starting with Auth");
            return "Auth"; // Show AuthScreen while checking authentication
        }

        if (isLoggedIn) {
            // Check user type and navigate accordingly
            if (isAdmin) {
                console.log("Starting with AdminDashboard - User is admin");
                return "AdminDashboard";
            } else if (isDeliveryPartner) {
                console.log("Starting with DeliveryDashboard - User is delivery partner");
                return "DeliveryDashboard";
            } else {
                console.log("Starting with MainTabs - User is regular user");
                return "MainTabs";
            }
        } else {
            console.log("Not logged in: Starting with Auth");
            return "Auth"; // Always start with AuthScreen when not logged in
        }
    };

    const initialRoute = getInitialRouteName();
    console.log("Initial route:", initialRoute);

    return (
        <Stack.Navigator
            initialRouteName={initialRoute}
            screenOptions={{ headerShown: false }}
        >
            {/* Authentication Screens - Always available */}
            <Stack.Screen
                name="Auth"
                component={AuthScreen}
                options={{ gestureEnabled: false }}
            />

            {/* These screens need to be accessible regardless of login state */}
            <Stack.Screen
                name="PreLoginScreen"
                component={PreLoginScreen}
                options={{ gestureEnabled: false }}
            />
            <Stack.Screen
                name="OTPScreen"
                component={OTPScreen}
                options={{ gestureEnabled: false }}
            />
            <Stack.Screen
                name="UsernameScreen"
                component={UsernameScreen}
                options={{ gestureEnabled: false }}
            />
            <Stack.Screen
                name="AddressDetailsScreen"
                component={AddressDetailsScreen}
                options={{ gestureEnabled: false }}
            />

            {/* Main App Screens - These will replace the auth screens in the stack */}
            <Stack.Screen
                name="MainTabs"
                component={TabNavigator}
                options={{ gestureEnabled: false }}
            />
            <Stack.Screen
                name="AdminDashboard"
                component={AdminTabNavigator}
                options={{ gestureEnabled: false }}
            />
            <Stack.Screen
                name="DeliveryDashboard"
                component={DeliveryPartnerTabNavigator}
                options={{ gestureEnabled: false }}
            />
            <Stack.Screen name="Settings" component={SettingsScreen} />
            <Stack.Screen name="ProductDetailScreen" component={ProductDetailScreen} />
            <Stack.Screen name="Checkout" component={CheckoutScreen} />
            <Stack.Screen name="CategoryScreen" component={CategoryScreen} />
            <Stack.Screen name="Orders" component={OrdersScreen} />
            <Stack.Screen name="EditProfileScreen" component={EditProfileScreen} />

            {/* Delivery Partner Screens */}
            <Stack.Screen name="DeliveryNotificationsSettingsScreen" component={DeliveryNotificationsSettingsScreen} />
            <Stack.Screen name="NotificationsSettingsScreen" component={NotificationsSettingsScreen} />
            <Stack.Screen name="DeliveryHelpSupportScreen" component={DeliveryHelpSupportScreen} />
            <Stack.Screen name="HelpSupportScreen" component={HelpSupportScreen} />

            {/* Admin Management Screens */}
            <Stack.Screen name="DeliveryPartnerManagementScreen" component={DeliveryPartnerManagementScreen} />
            <Stack.Screen name="DeliveryPartnerDetailScreen" component={DeliveryPartnerDetailScreen} />
            <Stack.Screen name="EditDeliveryPartnerScreen" component={EditDeliveryPartnerScreen} />
            <Stack.Screen name="UsersScreen" component={UsersScreen} />
            <Stack.Screen name="UserDetailScreen" component={UserDetailScreen} />
            <Stack.Screen name="AdminOrdersScreen" component={AdminOrdersScreen} />
            <Stack.Screen name="EditProduct" component={EditProductScreen} />
            <Stack.Screen name="AddProduct" component={AddProductScreen} />

            {/* User Profile Screens */}
            <Stack.Screen name="AddressScreen" component={AddressScreen} />
            <Stack.Screen name="AddEditAddressScreen" component={AddEditAddressScreen} />
            <Stack.Screen name="CoinsScreen" component={CoinsScreen} />
            <Stack.Screen name="ContactUsScreen" component={ContactUsScreen} />
            <Stack.Screen name="FAQScreen" component={FAQScreen} />
            <Stack.Screen name="LanguageScreen" component={LanguageScreen} />
            <Stack.Screen name="PolicyScreen" component={PolicyScreen} />

            <Stack.Screen name="DeliveryAddressScreen" component={DeliveryAddressScreen} />
        </Stack.Navigator>
    );
};

export default AppNavigator;
