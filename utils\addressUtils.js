/**
 * Address Utility Functions
 *
 * This file contains utility functions for handling address-related operations
 * including validation, formatting, and standardization.
 */

/**
 * Combines the main address from currentUser.address and addresses from the addresses array
 * @param {Object} currentUser - The current user object
 * @param {Array} addresses - The addresses array
 * @returns {Array} - Combined array of addresses
 */
export const getCombinedAddresses = (currentUser, addresses = []) => {
    const combinedAddresses = [];

    // Add the main address from currentUser.address if it exists
    if (currentUser && currentUser.address) {
        const hasAddressData = currentUser.address.doorNo ||
                              currentUser.address.streetName ||
                              currentUser.address.area ||
                              currentUser.address.district ||
                              currentUser.address.pincode ||
                              currentUser.address.fullAddress;

        // Log the user's main address to check for coordinates
        console.log('User main address in getCombinedAddresses:', {
            hasAddressData,
            hasCoordinatesObject: !!(currentUser.address.coordinates),
            coordinatesObject: currentUser.address.coordinates,
            hasDirectCoordinates: !!(currentUser.address.latitude && currentUser.address.longitude),
            latitude: currentUser.address.latitude,
            longitude: currentUser.address.longitude
        });

        if (hasAddressData) {
            // Extract coordinates from the user's address
            const coordinates = currentUser.address.coordinates || {
                latitude: currentUser.address.latitude || null,
                longitude: currentUser.address.longitude || null
            };

            const latitude = currentUser.address.latitude || currentUser.address.coordinates?.latitude || null;
            const longitude = currentUser.address.longitude || currentUser.address.coordinates?.longitude || null;

            // Log the extracted coordinates
            console.log('Extracted coordinates for main address:', {
                fromCoordinatesObject: coordinates,
                fromDirectProperties: { latitude, longitude }
            });

            // Create a standardized address object from the user's main address field
            const formattedAddress = formatAddress({
                doorNo: currentUser.address.doorNo || '',
                streetName: currentUser.address.streetName || '',
                area: currentUser.address.area || '',
                district: currentUser.address.district || '',
                pincode: currentUser.address.pincode || '',
                coordinates: coordinates,
                latitude: latitude,
                longitude: longitude,
                addressType: currentUser.address.addressType || 'Home'
            });

            const mainAddress = {
                _id: 'main-address',
                type: formattedAddress.addressType,
                ...formattedAddress,
                isMain: true // Mark as main address
            };

            // Log the created main address
            console.log('Created main address with coordinates:', {
                hasCoordinatesObject: !!(mainAddress.coordinates && mainAddress.coordinates.latitude && mainAddress.coordinates.longitude),
                coordinatesObject: mainAddress.coordinates,
                hasDirectCoordinates: !!(mainAddress.latitude && mainAddress.longitude),
                latitude: mainAddress.latitude,
                longitude: mainAddress.longitude
            });

            combinedAddresses.push(mainAddress);
        }
    }

    // Add addresses from the addresses array if they exist
    if (addresses && addresses.length > 0) {
        // Log the addresses array
        console.log(`Processing ${addresses.length} addresses from addresses array`);

        // Add all addresses from the array, but don't add duplicates of the main address
        addresses.forEach((address, index) => {
            // Log each address to check for coordinates
            console.log(`Address ${index} in addresses array:`, {
                id: address._id || address.id,
                type: address.type,
                hasCoordinatesObject: !!(address.coordinates),
                coordinatesObject: address.coordinates,
                hasDirectCoordinates: !!(address.latitude && address.longitude),
                latitude: address.latitude,
                longitude: address.longitude,
                fullAddress: address.fullAddress
            });

            // Skip if this is the same as the main address (avoid duplicates)
            const isMainAddressDuplicate =
                combinedAddresses.length > 0 &&
                address.fullAddress === combinedAddresses[0].fullAddress;

            if (!isMainAddressDuplicate) {
                // Format the address to ensure standardized format
                const formattedAddress = formatAddress(address);

                // Preserve the original ID and any other special fields
                const standardizedAddress = {
                    _id: address._id || address.id,
                    ...formattedAddress,
                    isDefault: address.isDefault || false,
                    isPrimary: address.isPrimary || false,
                    isWithinDeliveryZone: address.isWithinDeliveryZone || true,
                    createdAt: address.createdAt || new Date()
                };

                console.log(`Standardized address ${index} format`);
                combinedAddresses.push(standardizedAddress);
            } else {
                console.log(`Skipping address ${index} as it's a duplicate of the main address`);
            }
        });
    }

    return combinedAddresses;
};

/**
 * Gets the count of all addresses (main address + addresses array)
 * @param {Object} currentUser - The current user object
 * @param {Array} addresses - The addresses array
 * @returns {Number} - Total count of addresses
 */
export const getTotalAddressCount = (currentUser, addresses = []) => {
    return getCombinedAddresses(currentUser, addresses).length;
};

/**
 * Calculates the total coins from user data
 * @param {Object|Array|Number} coins - The coins data (can be array of objects or a number)
 * @returns {Number} - Total coins count
 */
export const getTotalCoins = (coins) => {
    if (!coins) return 0;

    if (Array.isArray(coins)) {
        // If coins is an array of objects with amount property
        return coins.reduce((sum, coin) => sum + (coin.amount || 0), 0);
    } else if (typeof coins === 'number') {
        // If coins is a simple number
        return coins;
    }

    return 0;
};

/**
 * Validates if an address has all required fields
 * @param {Object} address - The address object to validate
 * @returns {Object} Object with isValid boolean and any error messages
 */
export const validateAddress = (address) => {
    const errors = {};

    // Check for required fields - handle both naming conventions
    const doorNo = address.doorNo || address.flatNumber || '';
    const streetName = address.streetName || address.street || '';
    const area = address.area || address.locality || '';
    const district = address.district || address.city || '';

    if (!doorNo.trim()) {
        errors.doorNo = 'Flat/House number is required';
    }

    if (!streetName.trim()) {
        errors.streetName = 'Street name is required';
    }

    if (!area.trim()) {
        errors.area = 'Area/Locality is required';
    }

    if (!district.trim()) {
        errors.district = 'District is required';
    }

    if (!address.pincode || !address.pincode.trim()) {
        errors.pincode = 'Pincode is required';
    } else if (!/^\d{6}$/.test(address.pincode.trim())) {
        errors.pincode = 'Pincode must be 6 digits';
    }

    return {
        isValid: Object.keys(errors).length === 0,
        errors
    };
};

/**
 * Formats an address object into a standardized format
 * @param {Object} address - The address object to format
 * @returns {Object} Standardized address object
 */
export const formatAddress = (address) => {
    // Ensure all fields are trimmed
    const doorNo = (address.doorNo || address.flatNumber || '').trim();
    const streetName = (address.streetName || address.street || '').trim();
    const area = (address.area || address.locality || '').trim();
    const district = (address.district || address.city || '').trim();
    const pincode = (address.pincode || '').trim();
    const addressType = address.addressType || address.type || 'Home';

    // Create standardized full address string
    const fullAddress = `${doorNo}, ${streetName}, ${area}, ${district}, ${pincode}`;

    // Handle coordinates in a standardized way
    const coordinates = {
        latitude: address.coordinates?.latitude || address.latitude || null,
        longitude: address.coordinates?.longitude || address.longitude || null
    };

    return {
        doorNo,
        streetName,
        area,
        district,
        pincode,
        fullAddress,
        coordinates,
        // For backward compatibility
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
        addressType
    };
};

/**
 * Converts between different address field naming conventions
 * @param {Object} address - The address object to convert
 * @param {String} format - The target format ('standard', 'ui', or 'db')
 * @returns {Object} Converted address object
 */
export const convertAddressFormat = (address, format = 'standard') => {
    if (format === 'ui') {
        // Convert from standard format to UI format
        return {
            flatNumber: address.doorNo || '',
            street: address.streetName || '',
            locality: address.area || '',
            district: address.district || '',
            pincode: address.pincode || '',
            addressType: address.addressType || 'Home',
            coordinates: address.coordinates || {
                latitude: address.latitude || null,
                longitude: address.longitude || null
            }
        };
    } else if (format === 'db') {
        // Convert from any format to database format
        return formatAddress(address);
    } else {
        // Convert to standard format
        return formatAddress(address);
    }
};
