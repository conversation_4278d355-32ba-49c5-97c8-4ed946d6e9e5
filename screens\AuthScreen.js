import { View, Text, TouchableOpacity, Image, ActivityIndicator, StatusBar } from 'react-native';
import React, { useState, useEffect } from 'react';
import login from '../assets/login.png';

const AuthScreen = ({ navigation }) => {
    const [loading, setLoading] = useState(false);

    // Remove automatic navigation - user must press button
    useEffect(() => {
        console.log('AuthScreen mounted - waiting for user interaction');
    }, []);

    const handleStart = () => {
        setLoading(true);
        // Navigate to PreLoginScreen when the button is pressed
        console.log('Start button pressed, navigating to PreLoginScreen');

        // Add a small delay for better UX
        setTimeout(() => {
            navigation.navigate('PreLoginScreen');
            setLoading(false);
        }, 500);
    };

    return (
        <View className="flex-1 bg-white">
            <StatusBar barStyle="light-content" backgroundColor="#A31621" />

            {/* Image takes up more space - about 75% of screen */}
            <View style={{ flex: 0.75 }}>
                <Image source={login} className="w-full h-full" resizeMode="cover" />
            </View>

            {/* Further reduced footer size - about 25% of screen */}
            <View className="bg-madder rounded-t-3xl p-4" style={{ flex: 0.25, marginTop: -20 }}>
                <View className="flex-1 justify-between">
                    {/* Content section */}
                    <View className="mt-1">
                        <Text className="text-white text-2xl font-bold mb-1">Welcome to MeatNow</Text>
                        <Text className="text-white text-lg font-semibold mb-1">Non Stop</Text>
                        <Text className="text-white text-lg font-semibold">Fast and Speed Delivery</Text>
                    </View>

                    {/* Button section */}
                    <View className="px-6 pb-3">
                        <TouchableOpacity
                            onPress={handleStart}
                            disabled={loading}
                            className="bg-white py-3 px-6 items-center rounded-xl shadow-lg"
                            style={{
                                elevation: 4,
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 2 },
                                shadowOpacity: 0.2,
                                shadowRadius: 4,
                            }}
                        >
                            {loading ? (
                                <ActivityIndicator size="small" color="#A31621" />
                            ) : (
                                <Text className="text-madder text-base font-semibold">Get Started</Text>
                            )}
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default AuthScreen;