/**
 * Utility functions for test mode authentication
 * This allows the app to work without a backend for testing purposes
 */
import { storeAuthData, getUserData } from './authStorage';
import { USER_TYPES } from '../config/constants';

/**
 * Create a test user with the given phone number
 * @param {string} phoneNumber - The user's phone number
 * @param {string} userType - The user's type (USER, ADMIN, DELIVERY_PARTNER)
 * @returns {Object} - The created user object
 */
export const createTestUser = async (phoneNumber, userType = USER_TYPES.USER) => {
    // Check for special phone numbers
    if (phoneNumber === '8825549901') {
        userType = 'ADMIN';
        console.log('Test mode: Admin number detected');
    } else if (phoneNumber === '9894258293' || phoneNumber === '9876543210' || phoneNumber === '9876543211') {
        userType = 'DELIVERY_PARTNER';
        console.log('Test mode: Delivery partner number detected');
    }

    // Check if user already exists in AsyncStorage
    try {
        const existingUserData = await getUserData();
        if (existingUserData && existingUserData.phoneNumber === phoneNumber) {
            console.log('Test mode: User already exists in AsyncStorage', existingUserData);

            // Update the user type if needed
            if (existingUserData.userType !== userType) {
                console.log(`Test mode: Updating user type from ${existingUserData.userType} to ${userType}`);
                existingUserData.userType = userType;
                await storeAuthData('test_token', 'test_refresh_token', existingUserData);
            }

            return existingUserData;
        }
    } catch (error) {
        console.log('Test mode: No existing user found in AsyncStorage');
    }

    // Create a mock user with dummy data
    const user = {
        id: `test_${Date.now()}`,
        _id: `test_${Date.now()}`,
        name: userType === 'ADMIN' ? 'Admin User' :
              userType === 'DELIVERY_PARTNER' ? 'Delivery Partner' : 'Test User',
        phoneNumber,
        phone: phoneNumber,
        number: phoneNumber,
        userType,
        address: userType === 'USER' ? {
            doorNo: '123',
            streetName: 'Test Street',
            area: 'VIT Campus',
            district: 'Vellore',
            pincode: '632014',
            fullAddress: '123, Test Street, VIT Campus, Vellore - 632014',
            coordinates: {
                latitude: 12.8997028,
                longitude: 79.136073
            },
            latitude: 12.8997028,
            longitude: 79.136073,
            addressType: 'Home'
        } : '',
        addresses: userType === 'USER' ? [
            {
                _id: 'test-address-1',
                type: 'Home',
                doorNo: '123',
                streetName: 'Test Street',
                area: 'VIT Campus',
                district: 'Vellore',
                pincode: '632014',
                fullAddress: '123, Test Street, VIT Campus, Vellore - 632014',
                coordinates: {
                    latitude: 12.8997028,
                    longitude: 79.136073
                },
                isDefault: true,
                createdAt: new Date().toISOString()
            },
            {
                _id: 'test-address-2',
                type: 'Work',
                doorNo: '456',
                streetName: 'Office Road',
                area: 'Katpadi',
                district: 'Vellore',
                pincode: '632006',
                fullAddress: '456, Office Road, Katpadi, Vellore - 632006',
                coordinates: {
                    latitude: 12.9165,
                    longitude: 79.1325
                },
                isDefault: false,
                createdAt: new Date().toISOString()
            }
        ] : [],
        email: userType === 'ADMIN' ? '<EMAIL>' :
               userType === 'DELIVERY_PARTNER' ? `delivery${phoneNumber.slice(-4)}@meathub.com` :
               '<EMAIL>',
        isTestUser: true,
        createdAt: new Date().toISOString()
    };

    console.log('Test mode: Creating new user', user);

    // Store the user data
    await storeAuthData('test_token', 'test_refresh_token', user);

    return user;
};

/**
 * Update a test user's profile
 * @param {string} userId - The user's ID
 * @param {string} name - The user's name
 * @param {string} address - The user's address
 * @returns {Object} - The updated user object
 */
export const updateTestUserProfile = async (userId, name, address) => {
    console.log('Test mode: Updating user profile', { userId, name, address });

    // Get the current user data
    try {
        const userData = await getUserData();

        if (!userData || userData.id !== userId) {
            console.error('Test mode: User not found', { userId, userData });
            throw new Error('User not found');
        }

        // Update the user data
        const updatedUser = {
            ...userData,
            name,
            address,
            updatedAt: new Date().toISOString()
        };

        console.log('Test mode: Updated user data', updatedUser);

        // Store the updated user data
        await storeAuthData('test_token', 'test_refresh_token', updatedUser);

        return updatedUser;
    } catch (error) {
        console.error('Test mode: Error updating user profile', error);

        // Fallback to creating a new user object
        const updatedUser = {
            id: userId,
            name,
            address,
            userType: USER_TYPES.USER,
            isTestUser: true,
            updatedAt: new Date().toISOString()
        };

        // Store the updated user data
        await storeAuthData('test_token', 'test_refresh_token', updatedUser);

        return updatedUser;
    }
};

/**
 * Check if the app is running in test mode
 * @returns {boolean} - True if the app is running in test mode
 */
export const isTestMode = () => {
    // PRODUCTION MODE: Test mode permanently disabled
    console.log('🚀 PRODUCTION MODE: Test mode disabled, using real backend');
    return false; // Always use real backend in production
};

/**
 * Get a test OTP for the given phone number
 * @param {string} phoneNumber - The phone number to generate an OTP for
 * @returns {string} - The generated OTP
 */
export const getTestOTP = (phoneNumber) => {
    // In test mode, always return a fixed OTP
    return '123456';
};
