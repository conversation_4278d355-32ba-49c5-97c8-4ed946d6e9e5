const { verifyToken } = require('../utils/jwtUtils');
const User = require('../models/User');
const Admin = require('../models/Admin');
const DeliveryPartner = require('../models/DeliveryPartner');

/**
 * Protect routes - Verify JWT token and set user in request
 */
const protect = async (req, res, next) => {
    let token;

    // Check if token exists in Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        try {
            // Get token from header
            token = req.headers.authorization.split(' ')[1];

            // Verify token (ignoring expiration)
            const decoded = verifyToken(token);

            if (!decoded) {
                return res.status(401).json({ message: 'Invalid token', code: 'INVALID_TOKEN' });
            }

            // Log token details for debugging
            console.log('Token payload:', decoded);
            console.log('User type in token:', decoded.userType);
            console.log('User ID in token:', decoded.id);

            // Set user in request based on user type (case-insensitive)
            let user;
            let userType = decoded.userType ? decoded.userType.toUpperCase() : null;

            // Normalize user type to uppercase for consistency
            console.log('Normalized user type:', userType);

            switch (userType) {
                case 'USER':
                    user = await User.findById(decoded.id).select('-otpCode -otpExpiry');
                    break;
                case 'ADMIN':
                    user = await Admin.findById(decoded.id).select('-otpCode -otpExpiry');
                    break;
                case 'DELIVERY_PARTNER':
                    user = await DeliveryPartner.findById(decoded.id).select('-otpCode -otpExpiry');
                    break;
                default:
                    console.log('Invalid user type in token:', userType);
                    return res.status(401).json({ message: 'Invalid user type', code: 'INVALID_USER_TYPE' });
            }

            // Check if user exists
            if (!user) {
                return res.status(401).json({ message: 'User not found' });
            }

            // Set user and user type in request
            req.user = user;
            req.userType = decoded.userType;

            next();
        } catch (error) {
            console.error('Auth error:', error);

            // Log the error for debugging
            console.error('Auth middleware error:', error);

            res.status(401).json({ message: 'Not authorized, token failed' });
        }
    } else {
        res.status(401).json({ message: 'Not authorized, no token' });
    }
};

/**
 * Admin only middleware
 */
const adminOnly = (req, res, next) => {
    // Normalize user type to uppercase for consistency
    const userType = req.userType ? req.userType.toUpperCase() : null;

    console.log('Admin check - User type:', req.userType, 'Normalized:', userType);

    if (userType !== 'ADMIN') {
        console.log('Access denied: Not an admin user. User type:', userType);
        return res.status(403).json({
            message: 'Not authorized, admin only',
            code: 'ADMIN_ONLY',
            userType: userType
        });
    }

    console.log('Admin access granted for user type:', userType);
    next();
};

/**
 * Delivery partner only middleware
 */
const deliveryPartnerOnly = (req, res, next) => {
    // Normalize user type to uppercase for consistency
    const userType = req.userType ? req.userType.toUpperCase() : null;

    console.log('Delivery partner check - User type:', req.userType, 'Normalized:', userType);

    if (userType !== 'DELIVERY_PARTNER') {
        console.log('Access denied: Not a delivery partner. User type:', userType);
        return res.status(403).json({
            message: 'Not authorized, delivery partner only',
            code: 'DELIVERY_PARTNER_ONLY',
            userType: userType
        });
    }

    console.log('Delivery partner access granted for user type:', userType);
    next();
};

module.exports = {
    protect,
    adminOnly,
    deliveryPartnerOnly
};
