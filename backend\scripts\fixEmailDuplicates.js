const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('../models/User');

// Load environment variables
dotenv.config();
console.log('MongoDB URI:', process.env.MONGO_URI ? 'URI is set' : 'URI is not set');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
    .then(async () => {
        console.log('MongoDB Connected');
        
        try {
            // Find all users with null email
            const usersWithNullEmail = await User.find({ email: null });
            console.log(`Found ${usersWithNullEmail.length} users with null email`);
            
            // Update each user to remove the email field
            for (const user of usersWithNullEmail) {
                console.log(`Updating user: ${user.name} (${user.number})`);
                
                // Remove the email field
                user.email = undefined;
                
                // Save the user
                await user.save();
                console.log(`Updated user: ${user.name} (${user.number})`);
            }
            
            console.log('All users with null email have been updated');
            process.exit(0);
        } catch (error) {
            console.error('Error fixing email duplicates:', error);
            process.exit(1);
        }
    })
    .catch(err => {
        console.error('MongoDB connection error:', err);
        process.exit(1);
    });
