import axios from 'axios';
import { API_URL } from '../../config/constants';
import { getAuthToken } from '../authStorage';

// Get all orders for the current user
export const getUserOrders = async () => {
    try {
        const token = await getAuthToken();
        console.log('Fetching user orders from database...');

        // Add cache-busting parameter to ensure fresh data
        const timestamp = new Date().getTime();
        const response = await axios.get(`${API_URL}/users/orders?_=${timestamp}`, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            timeout: 10000 // 10 second timeout
        });

        console.log(`Fetched ${response.data.length || 0} orders for user`);
        return response.data;
    } catch (error) {
        console.error('Error fetching user orders:', error);
        // Return empty array on error to prevent app crashes
        return [];
    }
};

// Get order by ID for the current user
export const getUserOrderById = async (orderId) => {
    try {
        if (!orderId) {
            console.error('Invalid order ID provided:', orderId);
            throw new Error('Invalid order ID provided');
        }

        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/orders/${orderId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        return response.data;
    } catch (error) {
        console.error(`Error fetching order ${orderId}:`, error);
        throw error;
    }
};

// Create a new order
export const createUserOrder = async (orderData) => {
    try {
        const token = await getAuthToken();
        console.log('API - Order amount before sending:', orderData.totalAmount);

        const response = await axios.post(`${API_URL}/orders`, orderData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            timeout: 10000 // 10 second timeout
        });

        console.log('API - Order amount after response:',
            response.data.order?.totalAmount || response.data.order?.total ||
            response.data.totalAmount || response.data.total);

        return response.data;
    } catch (error) {
        console.error('Error creating order:', error);
        throw error;
    }
};
