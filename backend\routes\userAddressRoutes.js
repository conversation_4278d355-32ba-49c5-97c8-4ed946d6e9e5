const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/authMiddleware');
const User = require('../models/User');
const mongoose = require('mongoose');

/**
 * @route GET /api/user/profile
 * @desc Get user profile
 * @access Private
 */
router.get('/profile', protect, async (req, res) => {
    try {
        const user = await User.findById(req.user._id)
            .select('-otpCode -otpExpiry -refreshToken');

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Convert to plain object first to avoid modifying the Mongoose document directly
        const userObj = user.toObject();

        // Ensure address is properly populated
        if (!userObj.address) {
            userObj.address = {
                doorNo: "",
                streetName: "",
                area: "",
                district: "",
                pincode: "",
                fullAddress: ""
            };
        }

        // Ensure addresses array is initialized
        if (!userObj.addresses || !Array.isArray(userObj.addresses)) {
            userObj.addresses = [];
        }

        // If user has an address in the address field but no addresses in the array,
        // create an address object from the address field and add it to the array
        if (user.addresses.length === 0 &&
            user.address &&
            (user.address.fullAddress || user.address.doorNo || user.address.streetName)) {

            console.log('Creating address from user profile for /profile endpoint');

            // Extract coordinates from the user's address field
            const lat = user.address.coordinates?.latitude || user.address.latitude || null;
            const lng = user.address.coordinates?.longitude || user.address.longitude || null;

            // Create an address object from the user's address field
            const addressFromUserProfile = {
                _id: new mongoose.Types.ObjectId(),
                type: 'Home',
                doorNo: user.address.doorNo || '',
                streetName: user.address.streetName || '',
                area: user.address.area || '',
                district: user.address.district || '',
                pincode: user.address.pincode || '',
                fullAddress: user.address.fullAddress ||
                    `${user.address.doorNo || ''}, ${user.address.streetName || ''}, ${user.address.area || ''}, ${user.address.district || ''}, ${user.address.pincode || ''}`.replace(/\s+/g, ' ').trim(),
                // Add coordinates in both formats for compatibility
                coordinates: {
                    latitude: lat,
                    longitude: lng
                },
                latitude: lat,
                longitude: lng,
                isDefault: true,
                isPrimary: true,
                createdAt: new Date()
            };

            // Add the address to the array
            user.addresses.push(addressFromUserProfile);

            // Save the updated user
            await user.save();
            console.log('Address added to user profile:', addressFromUserProfile);
        }

        // Calculate total coins based on the storage format
        let totalCoins = 0;

        if (user.coins !== undefined) {
            if (Array.isArray(user.coins)) {
                // If coins is an array of objects with amount property
                totalCoins = user.coins.reduce((sum, coin) => sum + (coin.amount || 0), 0);
                console.log('Calculated total coins from array:', totalCoins);
            } else if (typeof user.coins === 'number') {
                // If coins is a simple number
                totalCoins = user.coins;
                console.log('Using coins as number:', totalCoins);
            }
        }

        // Add totalCoins to the user object
        userObj.totalCoins = totalCoins;

        // Log the response for debugging
        console.log('Sending user profile with address:', userObj.address);
        console.log('Sending user profile with addresses array:', userObj.addresses);
        console.log('Sending user profile with totalCoins:', userObj.totalCoins);

        res.status(200).json({ user: userObj });
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route GET /api/user/addresses
 * @desc Get all addresses for the current user
 * @access Private
 */
router.get('/addresses', protect, async (req, res) => {
    try {
        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        console.log('Fetching addresses for user:', user._id);

        // Check if user has addresses in the addresses array
        if (user.addresses && Array.isArray(user.addresses) && user.addresses.length > 0) {
            console.log('User has addresses in array:', user.addresses.length);
            return res.status(200).json({ addresses: user.addresses });
        }

        // If no addresses array, but user has an address in the address field, convert it to an array
        if (user.address && (user.address.fullAddress || user.address.doorNo || user.address.streetName)) {
            console.log('Creating address from user profile for /addresses endpoint');

            // Extract coordinates from the user's address field
            const lat = user.address.coordinates?.latitude || user.address.latitude || null;
            const lng = user.address.coordinates?.longitude || user.address.longitude || null;

            // Create an address object from the user's address field
            const addressFromUserProfile = {
                _id: new mongoose.Types.ObjectId(),
                type: 'Home',
                doorNo: user.address.doorNo || '',
                streetName: user.address.streetName || '',
                area: user.address.area || '',
                district: user.address.district || '',
                pincode: user.address.pincode || '',
                fullAddress: user.address.fullAddress ||
                    `${user.address.doorNo || ''}, ${user.address.streetName || ''}, ${user.address.area || ''}, ${user.address.district || ''}, ${user.address.pincode || ''}`.replace(/\s+/g, ' ').trim(),
                // Add coordinates in both formats for compatibility
                coordinates: {
                    latitude: lat,
                    longitude: lng
                },
                latitude: lat,
                longitude: lng,
                isDefault: true,
                isPrimary: true,
                createdAt: new Date()
            };

            // Initialize the addresses array with this address
            if (!user.addresses) {
                user.addresses = [];
            }

            user.addresses.push(addressFromUserProfile);
            await user.save();

            console.log('Address created and saved:', addressFromUserProfile);
            return res.status(200).json({ addresses: user.addresses });
        }

        // No addresses found
        console.log('No addresses found for user');
        return res.status(200).json({ addresses: [] });
    } catch (error) {
        console.error('Error fetching user addresses:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route POST /api/user/addresses
 * @desc Add a new address for the current user
 * @access Private
 */
router.post('/addresses', protect, async (req, res) => {
    try {
        const {
            type,
            doorNo,
            streetName,
            area,
            district,
            pincode,
            fullAddress,
            isDefault,
            isPrimary,
            latitude,
            longitude,
            coordinates,
            allowDuplicate // New parameter to force adding even if duplicate
        } = req.body;

        if (!doorNo || !streetName || !area || !district || !pincode) {
            return res.status(400).json({ message: 'All address fields are required' });
        }

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Extract coordinates from either the coordinates object or individual lat/lng fields
        const lat = coordinates?.latitude || latitude || null;
        const lng = coordinates?.longitude || longitude || null;

        // Initialize addresses array if it doesn't exist
        if (!user.addresses) {
            user.addresses = [];
        }

        // We're now allowing duplicate addresses by default
        // No duplicate detection logic here - always create a new address

        // Create a new address object
        const newAddress = {
            _id: new mongoose.Types.ObjectId(),
            type: type || 'Home',
            doorNo,
            streetName,
            area,
            district,
            pincode,
            fullAddress: fullAddress || `${doorNo}, ${streetName}, ${area}, ${district}, ${pincode}`,
            // Add coordinates in both formats for compatibility
            coordinates: {
                latitude: lat,
                longitude: lng
            },
            latitude: lat,
            longitude: lng,
            isDefault: isDefault || false,
            isPrimary: isPrimary || false,
            createdAt: new Date()
        };

        // If this is the primary address, update all other addresses and the main address field
        if (newAddress.isPrimary) {
            user.addresses.forEach(addr => {
                addr.isPrimary = false;
            });

            // Update the main address field for backward compatibility
            user.address = {
                doorNo: newAddress.doorNo,
                streetName: newAddress.streetName,
                area: newAddress.area,
                district: newAddress.district,
                pincode: newAddress.pincode,
                fullAddress: newAddress.fullAddress,
                // Add coordinates to the main address object too
                coordinates: {
                    latitude: lat,
                    longitude: lng
                },
                latitude: lat,
                longitude: lng
            };
        }

        // Add the new address
        user.addresses.push(newAddress);

        // Save the user
        await user.save();

        res.status(201).json({ address: newAddress });
    } catch (error) {
        console.error('Error adding user address:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route PUT /api/user/addresses/:id
 * @desc Update an existing address
 * @access Private
 */
router.put('/addresses/:id', protect, async (req, res) => {
    try {
        const addressId = req.params.id;
        const {
            type,
            doorNo,
            streetName,
            area,
            district,
            pincode,
            fullAddress,
            isDefault,
            isPrimary,
            latitude,
            longitude,
            coordinates
        } = req.body;

        if (!doorNo || !streetName || !area || !district || !pincode) {
            return res.status(400).json({ message: 'All address fields are required' });
        }

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Find the address to update
        if (!user.addresses || !Array.isArray(user.addresses)) {
            return res.status(404).json({ message: 'No addresses found for this user' });
        }

        const addressIndex = user.addresses.findIndex(addr =>
            addr._id.toString() === addressId || addr.id === addressId
        );

        if (addressIndex === -1) {
            return res.status(404).json({ message: 'Address not found' });
        }

        // Extract coordinates from either the coordinates object or individual lat/lng fields
        const lat = coordinates?.latitude || latitude || user.addresses[addressIndex].latitude || user.addresses[addressIndex].coordinates?.latitude || null;
        const lng = coordinates?.longitude || longitude || user.addresses[addressIndex].longitude || user.addresses[addressIndex].coordinates?.longitude || null;

        // Check if this address was previously primary
        const wasPrimary = user.addresses[addressIndex].isPrimary;

        // Update the address - keep the existing _id
        const existingId = user.addresses[addressIndex]._id;

        // Update the address fields directly instead of creating a new object
        user.addresses[addressIndex].type = type || user.addresses[addressIndex].type;
        user.addresses[addressIndex].doorNo = doorNo;
        user.addresses[addressIndex].streetName = streetName;
        user.addresses[addressIndex].area = area;
        user.addresses[addressIndex].district = district;
        user.addresses[addressIndex].pincode = pincode;
        user.addresses[addressIndex].fullAddress = fullAddress || `${doorNo}, ${streetName}, ${area}, ${district}, ${pincode}`;
        user.addresses[addressIndex].isPrimary = isPrimary !== undefined ? isPrimary : user.addresses[addressIndex].isPrimary;

        // Update coordinates in both formats
        if (!user.addresses[addressIndex].coordinates) {
            user.addresses[addressIndex].coordinates = {};
        }
        user.addresses[addressIndex].coordinates.latitude = lat;
        user.addresses[addressIndex].coordinates.longitude = lng;
        user.addresses[addressIndex].latitude = lat;
        user.addresses[addressIndex].longitude = lng;

        // Reference to the updated address for later use
        const updatedAddress = user.addresses[addressIndex];

        // If this is being set as the primary address, update all other addresses
        if (updatedAddress.isPrimary && !wasPrimary) {
            user.addresses.forEach(addr => {
                addr.isPrimary = false;
            });

            // Update the main address field for backward compatibility
            user.address = {
                doorNo: updatedAddress.doorNo,
                streetName: updatedAddress.streetName,
                area: updatedAddress.area,
                district: updatedAddress.district,
                pincode: updatedAddress.pincode,
                fullAddress: updatedAddress.fullAddress,
                // Add coordinates to the main address object too
                coordinates: {
                    latitude: lat,
                    longitude: lng
                },
                latitude: lat,
                longitude: lng
            };
        }
        // If this was primary but is no longer primary, update the main address field
        else if (wasPrimary && !updatedAddress.isPrimary) {
            // Find another address to make primary
            if (user.addresses.length > 1) {
                // Make the first non-current address primary
                const otherIndex = user.addresses.findIndex((addr, idx) => idx !== addressIndex);
                if (otherIndex >= 0) {
                    user.addresses[otherIndex].isPrimary = true;

                    // Get coordinates from the new primary address
                    const otherLat = user.addresses[otherIndex].coordinates?.latitude || user.addresses[otherIndex].latitude || null;
                    const otherLng = user.addresses[otherIndex].coordinates?.longitude || user.addresses[otherIndex].longitude || null;

                    // Update the main address field
                    user.address = {
                        doorNo: user.addresses[otherIndex].doorNo,
                        streetName: user.addresses[otherIndex].streetName,
                        area: user.addresses[otherIndex].area,
                        district: user.addresses[otherIndex].district,
                        pincode: user.addresses[otherIndex].pincode,
                        fullAddress: user.addresses[otherIndex].fullAddress,
                        // Add coordinates to the main address object too
                        coordinates: {
                            latitude: otherLat,
                            longitude: otherLng
                        },
                        latitude: otherLat,
                        longitude: otherLng
                    };
                }
            }
        }
        // If this address is still primary, update the main address field
        else if (updatedAddress.isPrimary) {
            user.address = {
                doorNo: updatedAddress.doorNo,
                streetName: updatedAddress.streetName,
                area: updatedAddress.area,
                district: updatedAddress.district,
                pincode: updatedAddress.pincode,
                fullAddress: updatedAddress.fullAddress,
                // Add coordinates to the main address object too
                coordinates: {
                    latitude: lat,
                    longitude: lng
                },
                latitude: lat,
                longitude: lng
            };
        }

        // We've already updated the address in place, no need to replace it

        // Save the user
        await user.save();

        res.status(200).json({ address: updatedAddress });
    } catch (error) {
        console.error(`Error updating address ${req.params.id}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route DELETE /api/user/addresses/:id
 * @desc Delete an address
 * @access Private
 */
router.delete('/addresses/:id', protect, async (req, res) => {
    try {
        const addressId = req.params.id;

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Find the address to delete
        if (!user.addresses || !Array.isArray(user.addresses)) {
            return res.status(404).json({ message: 'No addresses found for this user' });
        }

        const addressIndex = user.addresses.findIndex(addr =>
            addr._id.toString() === addressId || addr.id === addressId
        );

        if (addressIndex === -1) {
            return res.status(404).json({ message: 'Address not found' });
        }

        // Check if this is the primary address
        const isPrimary = user.addresses[addressIndex].isPrimary;

        // Remove the address
        user.addresses.splice(addressIndex, 1);

        // If there are other addresses
        if (user.addresses.length > 0) {
            // If this was the primary address, set the first one as primary
            if (isPrimary) {
                user.addresses[0].isPrimary = true;

                // Get coordinates from the new primary address
                const lat = user.addresses[0].coordinates?.latitude || user.addresses[0].latitude || null;
                const lng = user.addresses[0].coordinates?.longitude || user.addresses[0].longitude || null;

                // Update the main address field
                user.address = {
                    doorNo: user.addresses[0].doorNo,
                    streetName: user.addresses[0].streetName,
                    area: user.addresses[0].area,
                    district: user.addresses[0].district,
                    pincode: user.addresses[0].pincode,
                    fullAddress: user.addresses[0].fullAddress,
                    // Add coordinates to the main address object too
                    coordinates: {
                        latitude: lat,
                        longitude: lng
                    },
                    latitude: lat,
                    longitude: lng
                };
            }
        } else if (isPrimary) {
            // If this was the primary address and there are no other addresses,
            // clear the main address field
            user.address = {
                doorNo: "",
                streetName: "",
                area: "",
                district: "",
                pincode: "",
                fullAddress: "",
                coordinates: {
                    latitude: null,
                    longitude: null
                },
                latitude: null,
                longitude: null
            };
        }

        // Save the user
        await user.save();

        res.status(200).json({ message: 'Address deleted successfully' });
    } catch (error) {
        console.error(`Error deleting address ${req.params.id}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route POST /api/user/addresses/set-primary
 * @desc Set an address as the primary address
 * @access Private
 */
router.post('/addresses/set-primary', protect, async (req, res) => {
    try {
        const { addressId } = req.body;

        if (!addressId) {
            return res.status(400).json({ message: 'Address ID is required' });
        }

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Find the address
        if (!user.addresses || !Array.isArray(user.addresses)) {
            return res.status(404).json({ message: 'No addresses found for this user' });
        }

        const addressIndex = user.addresses.findIndex(addr =>
            addr._id.toString() === addressId || addr.id === addressId
        );

        if (addressIndex === -1) {
            return res.status(404).json({ message: 'Address not found' });
        }

        // Check if this address is already primary
        if (user.addresses[addressIndex].isPrimary) {
            // Address is already primary, no need to update
            return res.status(200).json({
                message: 'Address is already set as primary',
                addresses: user.addresses
            });
        }

        // Set all addresses to non-primary
        user.addresses.forEach(addr => {
            addr.isPrimary = false;
        });

        // Set the selected address as primary
        user.addresses[addressIndex].isPrimary = true;

        // Get coordinates from the address
        const lat = user.addresses[addressIndex].coordinates?.latitude || user.addresses[addressIndex].latitude || null;
        const lng = user.addresses[addressIndex].coordinates?.longitude || user.addresses[addressIndex].longitude || null;

        // Also update the main address field for backward compatibility
        user.address = {
            doorNo: user.addresses[addressIndex].doorNo,
            streetName: user.addresses[addressIndex].streetName,
            area: user.addresses[addressIndex].area,
            district: user.addresses[addressIndex].district,
            pincode: user.addresses[addressIndex].pincode,
            fullAddress: user.addresses[addressIndex].fullAddress,
            // Add coordinates to the main address object too
            coordinates: {
                latitude: lat,
                longitude: lng
            },
            latitude: lat,
            longitude: lng
        };

        await user.save();

        res.status(200).json({
            message: 'Primary address updated successfully',
            addresses: user.addresses
        });
    } catch (error) {
        console.error('Error setting primary address:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route GET /api/user/coins
 * @desc Get user coins/rewards
 * @access Private
 */
router.get('/coins', protect, async (req, res) => {
    try {
        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Calculate total, available, and used coins based on the storage format
        let totalCoins = 0;
        let availableCoins = 0;
        let usedCoins = 0;
        let hasUsedCoins = false;

        if (user.coins !== undefined) {
            if (Array.isArray(user.coins)) {
                // If coins is an array of objects with amount property
                totalCoins = user.coins.reduce((sum, coin) => sum + (coin.amount || 0), 0);

                // Calculate available coins (not marked as used)
                availableCoins = user.coins
                    .filter(coin => !coin.isUsed)
                    .reduce((sum, coin) => sum + (coin.amount || 0), 0);

                // Calculate used coins
                usedCoins = user.coins
                    .filter(coin => coin.isUsed)
                    .reduce((sum, coin) => sum + (coin.amount || 0), 0);

                hasUsedCoins = usedCoins > 0;

                console.log('Calculated from array - total:', totalCoins, 'available:', availableCoins, 'used:', usedCoins);
            } else if (typeof user.coins === 'number') {
                // If coins is a simple number
                totalCoins = user.coins;
                usedCoins = user.usedCoins || 0;
                availableCoins = totalCoins - usedCoins;
                hasUsedCoins = usedCoins > 0;

                console.log('Using number format - total:', totalCoins, 'available:', availableCoins, 'used:', usedCoins);
            }
        }

        // Return both the raw coins data and the calculated values
        res.status(200).json({
            coins: user.coins || 0,
            totalCoins: totalCoins,
            availableCoins: availableCoins,
            usedCoins: usedCoins,
            hasUsedCoins: hasUsedCoins
        });
    } catch (error) {
        console.error('Error fetching user coins:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route POST /api/user/coins/use
 * @desc Use coins for discount
 * @access Private
 */
router.post('/coins/use', protect, async (req, res) => {
    try {
        const { coinsToUse, orderId } = req.body;

        if (!coinsToUse || coinsToUse <= 0) {
            return res.status(400).json({ message: 'Invalid coins amount' });
        }

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Calculate available coins (total coins - used coins)
        let availableCoins = 0;

        if (Array.isArray(user.coins)) {
            // For UserUpdated model with array of coin objects
            // Count only coins that are not marked as used
            availableCoins = user.coins
                .filter(coin => !coin.isUsed)
                .reduce((sum, coin) => sum + (coin.amount || 0), 0);

            // Check if user has enough available coins
            if (availableCoins < coinsToUse) {
                return res.status(400).json({
                    message: 'Not enough available coins',
                    availableCoins: availableCoins
                });
            }

            // Mark coins as used instead of removing them
            let remainingToUse = coinsToUse;

            // Sort coins by creation date (oldest first to use oldest coins first)
            user.coins.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

            // Mark coins as used
            for (let i = 0; i < user.coins.length && remainingToUse > 0; i++) {
                if (!user.coins[i].isUsed && user.coins[i].amount > 0) {
                    if (user.coins[i].amount <= remainingToUse) {
                        // Use entire coin entry
                        remainingToUse -= user.coins[i].amount;
                        user.coins[i].isUsed = true;
                        user.coins[i].usedFor = orderId || 'Discount';
                        user.coins[i].usedAt = new Date();
                    } else {
                        // Split the coin entry - mark part as used and keep part as unused
                        const usedAmount = remainingToUse;

                        // Update current entry to reduce amount
                        user.coins[i].amount -= usedAmount;

                        // Add new entry for used portion
                        user.coins.push({
                            amount: usedAmount,
                            source: user.coins[i].source,
                            createdAt: user.coins[i].createdAt,
                            isUsed: true,
                            usedFor: orderId || 'Discount',
                            usedAt: new Date()
                        });

                        remainingToUse = 0;
                    }
                }
            }

            // Calculate remaining available coins
            const remainingAvailableCoins = user.coins
                .filter(coin => !coin.isUsed)
                .reduce((sum, coin) => sum + (coin.amount || 0), 0);

            await user.save();

            res.status(200).json({
                message: 'Coins used successfully',
                remainingCoins: remainingAvailableCoins,
                discountAmount: coinsToUse, // 1 coin = 1 rupee discount
                coinsUsed: coinsToUse
            });
        } else {
            // For original User model with simple number
            availableCoins = user.coins - (user.usedCoins || 0);

            // Check if user has enough available coins
            if (availableCoins < coinsToUse) {
                return res.status(400).json({
                    message: 'Not enough available coins',
                    availableCoins: availableCoins
                });
            }

            // Track used coins instead of deducting them
            user.usedCoins = (user.usedCoins || 0) + coinsToUse;

            // Store information about this coin usage
            user.lastCoinUse = {
                amount: coinsToUse,
                orderId: orderId || 'Discount',
                date: new Date()
            };

            await user.save();

            res.status(200).json({
                message: 'Coins used successfully',
                remainingCoins: user.coins - user.usedCoins,
                discountAmount: coinsToUse, // 1 coin = 1 rupee discount
                coinsUsed: coinsToUse
            });
        }
    } catch (error) {
        console.error('Error using coins:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route POST /api/user/coins/restore
 * @desc Restore previously used coins that were not used for orders
 * @access Private
 */
router.post('/coins/restore', protect, async (req, res) => {
    try {
        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        if (Array.isArray(user.coins)) {
            // For UserUpdated model with array of coin objects
            // Find coins that are marked as used but NOT used for an order
            // We identify this by checking if usedFor is 'Discount' or null, not an order ID
            const restorableCoins = user.coins.filter(coin =>
                coin.isUsed && (!coin.usedFor || coin.usedFor === 'Discount')
            );

            if (restorableCoins.length === 0) {
                return res.status(400).json({ message: 'No restorable coins found' });
            }

            // Calculate total restorable coins
            const totalRestorableCoins = restorableCoins.reduce((sum, coin) => sum + (coin.amount || 0), 0);

            // Mark only restorable coins as unused
            user.coins.forEach(coin => {
                if (coin.isUsed && (!coin.usedFor || coin.usedFor === 'Discount')) {
                    coin.isUsed = false;
                    coin.usedFor = null;
                    coin.usedAt = null;
                }
            });

            await user.save();

            // Calculate new available coins
            const availableCoins = user.coins
                .filter(coin => !coin.isUsed)
                .reduce((sum, coin) => sum + (coin.amount || 0), 0);

            res.status(200).json({
                message: 'Coins restored successfully',
                restoredCoins: totalRestorableCoins,
                availableCoins: availableCoins
            });
        } else {
            // For original User model with simple number and coinsHistory
            // Check if we have coinsHistory to work with
            if (Array.isArray(user.coinsHistory) && user.coinsHistory.length > 0) {
                // Find 'USED' entries that are linked to orders
                const orderLinkedEntries = user.coinsHistory.filter(
                    entry => entry.type === 'USED' && entry.orderId
                );

                // Calculate coins used for orders
                const coinsUsedForOrders = orderLinkedEntries.reduce(
                    (sum, entry) => sum + Math.abs(entry.amount), 0
                );

                // Calculate restorable coins (total used coins minus coins used for orders)
                const totalUsedCoins = user.usedCoins || 0;
                const restorableCoins = Math.max(0, totalUsedCoins - coinsUsedForOrders);

                if (restorableCoins <= 0) {
                    return res.status(400).json({
                        message: 'All used coins were spent on orders and cannot be restored'
                    });
                }

                // Update used coins counter
                user.usedCoins = coinsUsedForOrders;

                // Clear last coin use info only if it's not for an order
                if (user.lastCoinUse && (!user.lastCoinUse.orderId || user.lastCoinUse.orderId === 'Discount')) {
                    user.lastCoinUse = {
                        amount: 0,
                        orderId: null,
                        date: null
                    };
                }

                await user.save();

                res.status(200).json({
                    message: 'Coins restored successfully',
                    restoredCoins: restorableCoins,
                    availableCoins: user.coins - user.usedCoins
                });
            } else {
                // Legacy behavior if no coinsHistory is available
                const usedCoins = user.usedCoins || 0;

                if (usedCoins <= 0) {
                    return res.status(400).json({ message: 'No used coins to restore' });
                }

                // Reset used coins
                user.usedCoins = 0;

                // Clear last coin use info
                user.lastCoinUse = {
                    amount: 0,
                    orderId: null,
                    date: null
                };

                await user.save();

                res.status(200).json({
                    message: 'Coins restored successfully',
                    restoredCoins: usedCoins,
                    availableCoins: user.coins
                });
            }
        }
    } catch (error) {
        console.error('Error restoring coins:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

module.exports = router;
