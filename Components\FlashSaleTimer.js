import React, { useState, useEffect, useRef } from 'react';
import { View, Text, Animated, StyleSheet } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';

/**
 * An anime-style timer component for flash sales with delivery info displayed separately
 *
 * @param {Object} props
 * @param {Object} props.deliverySlot - The delivery slot to count down to
 * @param {Function} props.onTimerEnd - Optional callback when timer ends
 * @param {boolean} props.showDeliveryInfo - Whether to show delivery info (default: true)
 * @param {string} props.containerStyle - Optional additional styles for container
 */
const FlashSaleTimer = ({
    deliverySlot,
    onTimerEnd,
    showDeliveryInfo = true,
    containerStyle = ""
}) => {
    // Timer state with more detailed values for smoother updates
    const [timerValues, setTimerValues] = useState({
        hours: '00',
        minutes: '00',
        seconds: '00'
    });

    // Refs for tracking target time and interval
    const targetTimeRef = useRef(null);
    const intervalRef = useRef(null);

    // Calculate target time when delivery slot changes
    useEffect(() => {
        // Clear any existing interval
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
        }

        if (!deliverySlot) return;

        // Calculate the target time based on delivery slot
        const calculateTargetTime = () => {
            const now = new Date();
            let targetTime = new Date();

            // Set target time based on the delivery slot
            if (deliverySlot.day === "Today") {
                targetTime.setHours(deliverySlot.startHour - 1, 0, 0); // 1 hour before slot starts
            } else {
                // For tomorrow slots
                targetTime = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Add 1 day
                targetTime.setHours(deliverySlot.startHour - 1, 0, 0); // 1 hour before slot starts
            }

            // If target time is in the past, set it to a default end time
            if (targetTime <= now) {
                // If no more slots available, set a default end time (end of day)
                targetTime = new Date();
                targetTime.setHours(23, 59, 59);
            }

            return targetTime;
        };

        // Set the target time
        targetTimeRef.current = calculateTargetTime();

        // Start the timer
        startTimer();

        // Cleanup on unmount or when delivery slot changes
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [deliverySlot]);

    // Start the timer
    const startTimer = () => {
        // Initial update
        updateTimer();

        // Update every second for smoother countdown
        intervalRef.current = setInterval(updateTimer, 1000);
    };

    // Update the timer
    const updateTimer = () => {
        if (!targetTimeRef.current) return;

        const now = new Date();
        const diff = targetTimeRef.current - now;

        if (diff <= 0) {
            // Timer expired
            clearInterval(intervalRef.current);
            setTimerValues({ hours: '00', minutes: '00', seconds: '00' });

            // Call the onTimerEnd callback if provided
            if (onTimerEnd) {
                // Pass true to indicate timer reached zero
                onTimerEnd(true);
            }
            return;
        }

        // Convert to hours, minutes, seconds
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);

        // Format with leading zeros
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');
        const formattedSeconds = seconds.toString().padStart(2, '0');

        // Update the state
        setTimerValues({
            hours: formattedHours,
            minutes: formattedMinutes,
            seconds: formattedSeconds
        });
    };

    // If no delivery slot, don't render anything
    if (!deliverySlot) return null;

    // Render the timer component
    return (
        <View style={styles.container}>
            {/* Main component with timer only */}
            <View style={styles.mainContainer}>
                {/* Timer display with anime-style */}
                <LinearGradient
                    colors={['#FF3A4B', '#A31621', '#7D0A13']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={[
                        styles.timerContainer,
                    ]}
                    className={`${containerStyle}`}
                >
                    <Animated.View style={styles.glowOverlay} />

                    {/* Fire icon at the left */}
                    <View style={styles.timerContent}>
                        <MaterialIcons name="local-fire-department" size={16} color="#FFF" style={styles.fireIcon} />

                        <View style={styles.timerRow}>
                            <View style={styles.timeUnitGroup}>
                                <Text style={styles.timeValue}>{timerValues.hours}</Text>
                                <Text style={styles.timeLabel}>h</Text>
                            </View>
                            <View style={styles.spacer} />
                            <View style={styles.timeUnitGroup}>
                                <Text style={styles.timeValue}>{timerValues.minutes}</Text>
                                <Text style={styles.timeLabel}>m</Text>
                            </View>
                            <View style={styles.spacer} />
                            <View style={styles.timeUnitGroup}>
                                <Text style={styles.timeValue}>{timerValues.seconds}</Text>
                                <Text style={styles.timeLabel}>s</Text>
                            </View>
                        </View>
                    </View>
                </LinearGradient>
            </View>

            {/* Delivery slot info displayed separately if showDeliveryInfo is true */}
            {showDeliveryInfo && (
                <View style={styles.slotInfoContainer}>
                    <MaterialIcons name="access-time" size={12} color={MADDER_COLOR} />
                    <Text style={styles.slotInfoText}>
                        For delivery <Text style={styles.slotHighlight}>{deliverySlot.day.toLowerCase()}</Text> at{' '}
                        <Text style={styles.slotHighlight}>{deliverySlot.time}</Text>{' '}
                        <Text style={styles.slotLabel}>({deliverySlot.label})</Text>
                    </Text>
                </View>
            )}
        </View>
    );
};

// Define the madder color to match the app theme
const MADDER_COLOR = '#A31621';

// Styles for anime-inspired UI
const styles = StyleSheet.create({
    container: {
        marginLeft: 0,
    },
    mainContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    timerContainer: {
        borderRadius: 8,
        paddingVertical: 6,
        paddingHorizontal: 10,
        // Anime-style shadow effect
        shadowColor: '#FF3A4B',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.9,
        shadowRadius: 10,
        elevation: 8,
        // Anime-style border
        borderWidth: 2,
        borderColor: '#fff',
        // Position for glow overlay
        position: 'relative',
        overflow: 'hidden',
    },
    glowOverlay: {
        position: 'absolute',
        top: -15,
        left: -15,
        right: -15,
        bottom: -15,
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        borderRadius: 25,
        // Add a subtle pulse animation
        opacity: 0.7,
    },
    timerContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    fireIcon: {
        marginRight: 6,
        // Anime-style text shadow
        textShadowColor: 'rgba(255, 255, 0, 0.8)',
        textShadowOffset: { width: 0, height: 0 },
        textShadowRadius: 5,
    },
    timerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    timeUnitGroup: {
        flexDirection: 'row',
        alignItems: 'center',
        minWidth: 20,
    },
    timeValue: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
        textAlign: 'center',
        // Anime-style text shadow
        textShadowColor: 'rgba(0, 0, 0, 0.7)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
        letterSpacing: 1,
    },
    timeSeparator: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
        marginHorizontal: 2,
        // Anime-style text shadow
        textShadowColor: 'rgba(0, 0, 0, 0.7)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
    },
    timeLabel: {
        color: 'white',
        fontSize: 10,
        fontWeight: '700',
        marginLeft: 2,
        marginBottom: 1, // Slight adjustment to align with the time value
        // Anime-style text shadow
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 1,
    },
    spacer: {
        width: 8,
    },
    slotInfoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 4,
        marginLeft: 4,
    },
    slotInfoText: {
        color: '#666',
        fontSize: 10,
        marginLeft: 4,
        lineHeight: 14,
        flex: 1,
        flexWrap: 'wrap',
    },
    slotHighlight: {
        fontWeight: '600',
        color: '#444',
    },
    slotLabel: {
        color: MADDER_COLOR,
        fontWeight: '600',
    }
});

export default FlashSaleTimer;
