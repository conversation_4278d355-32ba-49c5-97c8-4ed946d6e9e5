import React from 'react';
import { View, Text, TouchableOpacity, Modal, Animated } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const PincodeAlert = ({
    visible,
    onClose,
    onBrowseProducts,
    pincodeMessage,
    showBrowseOption = true
}) => {
    const getIconAndColor = () => {
        switch (pincodeMessage?.type) {
            case 'success':
                return { icon: 'check-circle', color: '#10B981', bgColor: '#D1FAE5' };
            case 'coming_soon':
                return { icon: 'rocket-launch', color: '#A31621', bgColor: '#FCF7F8' };
            case 'not_available':
                return { icon: 'location-off', color: '#6B7280', bgColor: '#F3F4F6' };
            default:
                return { icon: 'error', color: '#EF4444', bgColor: '#FEE2E2' };
        }
    };

    const { icon, color, bgColor } = getIconAndColor();

    if (!visible || !pincodeMessage) return null;

    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="fade"
            onRequestClose={onClose}
        >
            <View className="flex-1 bg-black/50 justify-center items-center px-6">
                <Animated.View className="bg-white rounded-2xl p-6 w-full max-w-sm shadow-2xl">
                    {/* Icon and Title */}
                    <View className="items-center mb-4">
                        <View
                            className="w-16 h-16 rounded-full items-center justify-center mb-3"
                            style={{ backgroundColor: bgColor }}
                        >
                            <MaterialIcons name={icon} size={32} color={color} />
                        </View>
                        <Text className="text-xl font-bold text-gray-900 text-center">
                            {pincodeMessage.title}
                        </Text>
                    </View>

                    {/* Message */}
                    <Text className="text-gray-600 text-center mb-6 leading-6">
                        {pincodeMessage.message}
                    </Text>

                    {/* Action Buttons */}
                    <View className="space-y-3">
                        {/* Browse Products Button - Only show if allowed */}
                        {showBrowseOption && pincodeMessage.canBrowse && (
                            <TouchableOpacity
                                className="bg-madder py-3 px-6 rounded-xl flex-row items-center justify-center"
                                onPress={onBrowseProducts}
                                activeOpacity={0.8}
                            >
                                <MaterialIcons name="shopping-bag" size={20} color="white" />
                                <Text className="text-white font-semibold ml-2">
                                    Browse Products
                                </Text>
                            </TouchableOpacity>
                        )}

                        {/* Close Button */}
                        <TouchableOpacity
                            className="bg-gray-100 py-3 px-6 rounded-xl"
                            onPress={onClose}
                            activeOpacity={0.8}
                        >
                            <Text className="text-gray-700 font-medium text-center">
                                {pincodeMessage.type === 'success' ? 'Continue' : 'Close'}
                            </Text>
                        </TouchableOpacity>
                    </View>

                    {/* Additional Info for Success */}
                    {pincodeMessage.type === 'success' && pincodeMessage.areaInfo && (
                        <View className="mt-4 p-3 bg-green-50 rounded-lg">
                            <Text className="text-green-800 text-sm text-center font-medium">
                                📍 Serving: {pincodeMessage.areaInfo.areas.join(', ')}
                            </Text>
                        </View>
                    )}


                </Animated.View>
            </View>
        </Modal>
    );
};

export default PincodeAlert;
