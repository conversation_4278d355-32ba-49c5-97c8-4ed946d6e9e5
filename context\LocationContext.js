import React, { createContext, useContext, useState, useEffect } from 'react';
import * as Location from 'expo-location';
import { Alert, Linking, Platform } from 'react-native';
import { requestLocationPermission, getCurrentLocation } from '../utils/locationUtils';

// Create the context
const LocationContext = createContext();

// Create a provider component
export const LocationProvider = ({ children }) => {
    // State for location permissions and data
    const [locationPermissionStatus, setLocationPermissionStatus] = useState(null);
    const [currentLocation, setCurrentLocation] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Request location permission when the app starts
    useEffect(() => {
        requestInitialLocationPermission();
    }, []);

    // Function to request location permission when the app starts
    const requestInitialLocationPermission = async () => {
        try {
            setLoading(true);
            setError(null);

            console.log('LocationContext: Checking initial location permission');
            const { status } = await Location.getForegroundPermissionsAsync();
            setLocationPermissionStatus(status);

            console.log('LocationContext: Location permission status:', status);

            if (status === 'granted') {
                // If permission is granted, get the current location
                try {
                    const location = await Location.getCurrentPositionAsync({
                        accuracy: Location.Accuracy.High
                    });

                    setCurrentLocation({
                        latitude: location.coords.latitude,
                        longitude: location.coords.longitude
                    });

                    console.log('LocationContext: Initial location obtained:', {
                        latitude: location.coords.latitude,
                        longitude: location.coords.longitude
                    });
                } catch (locationError) {
                    console.error('LocationContext: Error getting initial location:', locationError);
                    setError('Could not get your location');
                }
            } else {
                console.log('LocationContext: Initial location permission denied');
            }
        } catch (error) {
            console.error('LocationContext: Error requesting initial location permission:', error);
            setError('Error requesting location permission');
        } finally {
            setLoading(false);
        }
    };

    // Function to request location permission again (can be called from components)
    const requestPermission = async () => {
        try {
            setLoading(true);
            const result = await requestLocationPermission();
            setLocationPermissionStatus(result ? 'granted' : 'denied');

            if (result) {
                // If permission is granted, get the current location
                try {
                    const location = await getCurrentLocation();
                    if (location) {
                        setCurrentLocation(location);
                    }
                } catch (locationError) {
                    console.error('LocationContext: Error getting location after permission granted:', locationError);
                    setError('Could not get your location');
                }
            }

            return result;
        } catch (error) {
            console.error('LocationContext: Error in requestPermission:', error);
            setError('Error requesting location permission');
            return false;
        } finally {
            setLoading(false);
        }
    };

    // Function to get the current location (can be called from components)
    const getLocation = async () => {
        try {
            setLoading(true);
            setError(null);

            // Check if we have permission
            if (locationPermissionStatus !== 'granted') {
                const hasPermission = await requestPermission();
                if (!hasPermission) {
                    return null;
                }
            }

            // Get current location
            const location = await getCurrentLocation();
            if (location) {
                setCurrentLocation(location);
            }

            return location;
        } catch (error) {
            console.error('LocationContext: Error in getLocation:', error);
            setError('Error getting location');
            return null;
        } finally {
            setLoading(false);
        }
    };

    // Value to be provided to consumers
    const value = {
        locationPermissionStatus,
        currentLocation,
        loading,
        error,
        requestPermission,
        getLocation,
        requestInitialLocationPermission
    };

    return (
        <LocationContext.Provider value={value}>
            {children}
        </LocationContext.Provider>
    );
};

// Custom hook to use the location context
export const useLocation = () => {
    const context = useContext(LocationContext);
    if (context === undefined) {
        throw new Error('useLocation must be used within a LocationProvider');
    }
    return context;
};
