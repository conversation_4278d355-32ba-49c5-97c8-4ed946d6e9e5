const express = require('express');
const router = express.Router();
const { protect, deliveryPartnerOnly } = require('../middleware/authMiddleware');
const {
    getDeliveryPartnerOrders,
    updateOrderStatus,
    updateAvailabilityStatus,
    getDeliveryPartnerProfile,
    getUnassignedOrders,
    assignOrderToSelf
} = require('../controllers/deliveryController');

// All routes are protected and delivery-partner-only
router.use(protect, deliveryPartnerOnly);

// Order management
router.get('/orders', getDeliveryPartnerOrders);
router.put('/orders/:id/status', updateOrderStatus);
router.get('/unassigned-orders', getUnassignedOrders);
router.put('/orders/:id/assign', assignOrderToSelf);

// Profile management
router.get('/profile', getDeliveryPartnerProfile);
router.put('/availability', updateAvailabilityStatus);

module.exports = router;