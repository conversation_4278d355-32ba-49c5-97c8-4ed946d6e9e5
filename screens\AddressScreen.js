import React, { useState, useRef, useMemo, useCallback } from 'react';
import { View, Text, TouchableOpacity, FlatList, Animated, Alert, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useUser } from '../context/UserContext';
import { addUserAddress } from '../utils/api/userProfileApi';
import { API_URL } from '../config/constants';
import { getAuthToken } from '../utils/authStorage';

const AddressScreen = () => {
    const navigation = useNavigation();
    const { addresses, deleteUserAddress, refreshUserData, currentUser, loading: userLoading } = useUser();

    // Loading state - use context loading initially for faster perceived performance
    const [loading, setLoading] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Memoized function to combine both address types into a single array for better performance
    const getCombinedAddresses = useMemo(() => {
        const combinedAddresses = [];

        // Add the main address from currentUser.address if it exists
        if (currentUser && currentUser.address) {
            const hasAddressData = currentUser.address.doorNo ||
                                  currentUser.address.streetName ||
                                  currentUser.address.area ||
                                  currentUser.address.district ||
                                  currentUser.address.pincode ||
                                  currentUser.address.fullAddress;

            if (hasAddressData) {
                // Extract coordinates from the user's address
                const lat = currentUser.address.coordinates?.latitude || currentUser.address.latitude || null;
                const lng = currentUser.address.coordinates?.longitude || currentUser.address.longitude || null;

                // Create an address object from the user's main address field
                const mainAddress = {
                    _id: 'main-address',
                    type: 'Main',
                    addressType: currentUser.address.addressType || 'Home', // Add addressType field to match schema
                    doorNo: currentUser.address.doorNo || '',
                    streetName: currentUser.address.streetName || '',
                    area: currentUser.address.area || '',
                    district: currentUser.address.district || '',
                    pincode: currentUser.address.pincode || '',
                    fullAddress: currentUser.address.fullAddress ||
                        `${currentUser.address.doorNo || ''}, ${currentUser.address.streetName || ''}, ${currentUser.address.area || ''}, ${currentUser.address.district || ''}, ${currentUser.address.pincode || ''}`.replace(/\s+/g, ' ').trim(),
                    // Include coordinates in both formats for compatibility
                    coordinates: {
                        latitude: lat,
                        longitude: lng
                    },
                    latitude: lat,
                    longitude: lng,
                    isMain: true // Mark as main address
                };
                combinedAddresses.push(mainAddress);
            }
        }

        // Add addresses from the addresses array if they exist
        if (addresses && addresses.length > 0) {
            // Add all addresses from the array, but don't add duplicates of the main address
            addresses.forEach(address => {
                // Skip if this is the same as the main address (avoid duplicates)
                const isMainAddressDuplicate =
                    combinedAddresses.length > 0 &&
                    address.fullAddress === combinedAddresses[0].fullAddress;

                if (!isMainAddressDuplicate) {
                    combinedAddresses.push(address);
                }
            });
        }

        return combinedAddresses;
    }, [addresses, currentUser]);

    // Optimized fetch addresses function with caching
    const fetchAddresses = useCallback(async (forceRefresh = false) => {
        try {
            if (forceRefresh) {
                setIsRefreshing(true);
            } else {
                setLoading(true);
            }

            console.log('Fetching addresses from database...');

            // Only refresh if we don't have data or force refresh is requested
            if (!addresses || addresses.length === 0 || forceRefresh) {
                await refreshUserData();
            }

            console.log('Addresses fetched:', addresses);
            console.log('Current user data:', currentUser);

            // If no addresses in the array but user has address in profile, create one
            if ((!addresses || addresses.length === 0) &&
                currentUser &&
                currentUser.address) {

                // Check if the address object has any data
                const hasAddressData = currentUser.address.doorNo ||
                                      currentUser.address.streetName ||
                                      currentUser.address.area ||
                                      currentUser.address.district ||
                                      currentUser.address.pincode ||
                                      currentUser.address.fullAddress;

                if (hasAddressData) {
                    console.log('Creating address from user profile:', currentUser.address);

                    // Extract coordinates from the user's address
                    const lat = currentUser.address.coordinates?.latitude || currentUser.address.latitude || null;
                    const lng = currentUser.address.coordinates?.longitude || currentUser.address.longitude || null;

                    // Create an address object from the user's address field
                    const addressFromProfile = {
                        _id: 'profile-address',
                        type: 'Home',
                        addressType: 'Home', // Add addressType field to match schema
                        doorNo: currentUser.address.doorNo || '',
                        streetName: currentUser.address.streetName || '',
                        area: currentUser.address.area || '',
                        district: currentUser.address.district || '',
                        pincode: currentUser.address.pincode || '',
                        fullAddress: currentUser.address.fullAddress ||
                            `${currentUser.address.doorNo || ''}${currentUser.address.streetName ? `, ${currentUser.address.streetName}` : ''}${currentUser.address.area ? `, ${currentUser.address.area}` : ''}${currentUser.address.district ? `, ${currentUser.address.district}` : ''}${currentUser.address.pincode ? ` - ${currentUser.address.pincode}` : ''}`.replace(/\s+/g, ' ').trim(),
                        // Include coordinates in both formats for compatibility
                        coordinates: {
                            latitude: lat,
                            longitude: lng
                        },
                        latitude: lat,
                        longitude: lng,
                        isDefault: true
                    };

                    // Add this address to the database
                    try {
                        await addUserAddress(addressFromProfile);
                        console.log('Address created from profile and added to database');
                        // Refresh again to get the newly added address
                        await refreshUserData();
                    } catch (addError) {
                        console.error('Error adding address from profile:', addError);
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching addresses:', error);
            showToast('Failed to load addresses', 'error');
        } finally {
            setLoading(false);
            setIsRefreshing(false);
        }
    }, [addresses, currentUser, refreshUserData]);

    // Fetch addresses when screen is focused - optimized with dependency array
    useFocusEffect(
        useCallback(() => {
            // Only fetch if we don't have data or if user context is not loading
            if (!userLoading && (!addresses || addresses.length === 0)) {
                fetchAddresses();
            }
        }, [fetchAddresses, userLoading, addresses])
    );

    // Animation for toast notification
    const toastAnimation = useRef(new Animated.Value(0)).current;
    const [toastVisible, setToastVisible] = useState(false);
    const [toastMessage, setToastMessage] = useState('');
    const [toastType, setToastType] = useState('success');

    // Show toast notification
    const showToast = (message, type = 'success') => {
        setToastMessage(message);
        setToastType(type);
        setToastVisible(true);

        Animated.sequence([
            // Animate in
            Animated.timing(toastAnimation, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            // Hold
            Animated.delay(2000),
            // Animate out
            Animated.timing(toastAnimation, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            })
        ]).start(() => {
            setToastVisible(false);
        });
    };

    const handleAddAddress = useCallback(() => {
        // Navigate to DeliveryAddressScreen for precise location selection
        navigation.navigate('DeliveryAddressScreen');
    }, [navigation]);

    const handleEditAddress = useCallback((address) => {
        // If it's the main address, we need to handle it differently
        if (address.isMain) {
            // Create a copy of the address without the isMain flag for editing
            const addressForEdit = {
                ...address,
                isMain: undefined, // Remove the isMain flag
                _id: undefined, // Remove the temporary ID
                type: 'Home' // Default to Home type
            };

            // Navigate to edit screen with special flag for main address
            navigation.navigate('AddEditAddressScreen', {
                mode: 'edit',
                address: addressForEdit,
                isMainAddress: true
            });
        } else {
            // Ask user if they want to edit with map or form
            Alert.alert(
                "Edit Address",
                "How would you like to edit this address?",
                [
                    {
                        text: "Edit with Map",
                        onPress: () => {
                            // Navigate to DeliveryAddressScreen for map-based editing
                            navigation.navigate('DeliveryAddressScreen', { address });
                        }
                    },
                    {
                        text: "Edit Form",
                        onPress: () => {
                            // Regular address from the addresses array
                            navigation.navigate('AddEditAddressScreen', { mode: 'edit', address });
                        }
                    },
                    {
                        text: "Cancel",
                        style: "cancel"
                    }
                ]
            );
        }
    }, [navigation]);

    const handleDeleteAddress = useCallback((addressId) => {
        Alert.alert(
            "Delete Address",
            "Are you sure you want to delete this address?",
            [
                { text: "Cancel", style: "cancel" },
                {
                    text: "Delete",
                    style: "destructive",
                    onPress: async () => {
                        try {
                            setLoading(true);
                            console.log('Deleting address with ID:', addressId);
                            await deleteUserAddress(addressId);
                            showToast("Address deleted successfully");
                            // Refresh addresses after deletion
                            await refreshUserData();
                        } catch (error) {
                            console.error('Error deleting address:', error);
                            showToast("Failed to delete address", "error");
                        } finally {
                            setLoading(false);
                        }
                    }
                }
            ]
        );
    }, [deleteUserAddress, refreshUserData]);

    // Function to set an address as primary - optimized with useCallback
    const handleSetPrimary = useCallback(async (addressId) => {
        try {
            setLoading(true);
            console.log('Setting address as primary:', addressId);

            // Call the API to set the address as primary
            const response = await fetch(`${API_URL}/user/addresses/set-primary`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${await getAuthToken()}`
                },
                body: JSON.stringify({ addressId })
            });

            if (!response.ok) {
                throw new Error('Failed to set primary address');
            }

            // Refresh user data to get the updated addresses
            await refreshUserData();
            showToast("Primary address updated successfully");
        } catch (error) {
            console.error('Error setting primary address:', error);
            showToast("Failed to set primary address", "error");
        } finally {
            setLoading(false);
        }
    }, [refreshUserData]);

    const renderAddressItem = useCallback(({ item }) => (
        <View className={`bg-white rounded-xl p-4 mb-4 shadow-sm ${item.isMain ? 'border-2 border-madder' : ''}`}>
            <View className="flex-row justify-between items-start">
                <View className="flex-row items-center">
                    <View className={`w-10 h-10 rounded-full ${item.isMain ? 'bg-madder' : 'bg-madder/10'} items-center justify-center mr-3`}>
                        <MaterialIcons
                            name={
                                item.isMain ? 'star' :
                                item.type === 'Home' ? 'home' :
                                item.type === 'Work' ? 'work' :
                                'location-on'
                            }
                            size={20}
                            color={item.isMain ? "white" : "#A31621"}
                        />
                    </View>
                    <View className="flex-1">
                        <View className="flex-row items-center">
                            <Text className="text-lg font-bold text-gray-800">{item.type}</Text>
                            {(item.isMain || item.isPrimary) && (
                                <View className="ml-2 px-2 py-0.5 bg-madder rounded-full">
                                    <Text className="text-xs text-white font-medium">Primary</Text>
                                </View>
                            )}
                        </View>
                        {/* Display structured address with only essential components */}
                        {item.doorNo ? (
                            <>
                                <Text className="text-gray-600 mt-1">
                                    {item.doorNo}
                                </Text>
                                {item.streetName && (
                                    <Text className="text-gray-600">
                                        {item.streetName}
                                    </Text>
                                )}
                                {item.area && (
                                    <Text className="text-gray-600">
                                        {item.area}
                                    </Text>
                                )}
                                {item.district && (
                                    <Text className="text-gray-600">
                                        {item.district}
                                    </Text>
                                )}
                                {item.pincode && (
                                    <Text className="text-gray-600">
                                        Pincode: {item.pincode}
                                    </Text>
                                )}
                            </>
                        ) : item.fullAddress ? (
                            <Text className="text-gray-600 mt-1">{item.fullAddress}</Text>
                        ) : (
                            <Text className="text-gray-600 mt-1">No address details available</Text>
                        )}
                    </View>
                </View>
            </View>

            <View className="flex-row justify-end mt-4 pt-2 border-t border-gray-100">
                {/* Set as Primary button - only show for non-primary addresses */}
                {!item.isMain && !item.isPrimary && (
                    <TouchableOpacity
                        className="mr-4 flex-row items-center"
                        onPress={() => handleSetPrimary(item._id || item.id)}
                    >
                        <MaterialIcons name="star" size={18} color="#A31621" />
                        <Text className="ml-1 text-madder font-medium">Set Primary</Text>
                    </TouchableOpacity>
                )}

                <TouchableOpacity
                    className="mr-4 flex-row items-center"
                    onPress={() => handleEditAddress(item)}
                >
                    <MaterialIcons name="edit" size={18} color="#A31621" />
                    <Text className="ml-1 text-madder font-medium">Edit</Text>
                </TouchableOpacity>

                {/* Only show delete button for non-main addresses */}
                {!item.isMain && (
                    <TouchableOpacity
                        className="flex-row items-center"
                        onPress={() => handleDeleteAddress(item._id || item.id)}
                    >
                        <MaterialIcons name="delete" size={18} color="#A31621" />
                        <Text className="ml-1 text-madder font-medium">Delete</Text>
                    </TouchableOpacity>
                )}
            </View>
        </View>
    ), [handleSetPrimary, handleEditAddress, handleDeleteAddress]);

    // Toast animation styles
    const toastTranslateY = toastAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [100, 0],
    });

    return (
        <View className="flex-1 bg-snow">
            {/* Enhanced Header with Radius */}
            <View className="bg-madder pt-16 pb-6">
                <View className="px-6 flex-row items-center">
                    <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                        <MaterialIcons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">My Addresses</Text>
                </View>
                {/* Rounded bottom corners */}
                <View
                    className="bg-madder h-6 -mb-6"
                    style={{
                        borderBottomLeftRadius: 24,
                        borderBottomRightRadius: 24,
                    }}
                />
            </View>

            <View className="p-4 flex-1">
                {(loading || userLoading) && !isRefreshing ? (
                    // Loading indicator - only show when not refreshing
                    <View className="flex-1 items-center justify-center">
                        <ActivityIndicator size="large" color="#A31621" />
                        <Text className="text-gray-600 mt-4">Loading addresses...</Text>
                    </View>
                ) : (
                    <>
                        {/* Optimized FlatList with memoized data */}
                        <FlatList
                            data={getCombinedAddresses}
                            renderItem={renderAddressItem}
                            keyExtractor={item => item._id || item.id}
                            showsVerticalScrollIndicator={false}
                            contentContainerStyle={{ paddingBottom: 20 }}
                            refreshing={isRefreshing}
                            onRefresh={() => fetchAddresses(true)}
                            removeClippedSubviews={true}
                            maxToRenderPerBatch={10}
                            windowSize={10}
                            initialNumToRender={5}
                            getItemLayout={(data, index) => ({
                                length: 120, // Approximate item height
                                offset: 120 * index,
                                index,
                            })}
                            ListEmptyComponent={() => (
                                // Empty state
                                <View className="flex-1 items-center justify-center py-20">
                                    <View className="w-20 h-20 rounded-full bg-madder/10 items-center justify-center mb-4">
                                        <MaterialIcons name="location-off" size={40} color="#A31621" />
                                    </View>
                                    <Text className="text-lg font-bold text-gray-800 mb-2">No Addresses Found</Text>
                                    <Text className="text-gray-600 text-center mb-6">You haven't added any addresses yet.</Text>
                                </View>
                            )}
                        />
                    </>
                )}

                <TouchableOpacity
                    className="bg-madder py-4 rounded-xl items-center mb-4"
                    onPress={handleAddAddress}
                >
                    <Text className="text-white font-bold">Add New Address</Text>
                </TouchableOpacity>
            </View>

            {/* Toast Notification */}
            {toastVisible && (
                <Animated.View
                    className={`absolute bottom-20 left-5 right-5 ${toastType === 'success' ? 'bg-green-500' : 'bg-red-500'} rounded-lg p-4 flex-row items-center`}
                    style={{
                        transform: [{ translateY: toastTranslateY }],
                        opacity: toastAnimation,
                        shadowColor: "#000",
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.25,
                        shadowRadius: 3.84,
                        elevation: 5,
                    }}
                >
                    <MaterialIcons
                        name={toastType === 'success' ? 'check-circle' : 'error'}
                        size={24}
                        color="white"
                    />
                    <Text className="text-white font-medium ml-2 flex-1">{toastMessage}</Text>
                </Animated.View>
            )}
        </View>
    );
};

export default AddressScreen;
