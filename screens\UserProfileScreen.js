import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    RefreshControl,
    Alert,
    ActivityIndicator,
    Modal,
    Animated
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect } from '@react-navigation/native';
import { useUser } from '../context/UserContext';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';

const UserProfileScreen = () => {
    const navigation = useNavigation();
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [showLogoutModal, setShowLogoutModal] = useState(false);

    // Animation refs for logout modal
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.8)).current;

    // Get contexts
    const { currentUser, refreshUserData } = useUser();
    const { clearCart } = useCart();
    const { logout: authLogout } = useAuth();

    // Optimized user data processing with memoization
    const processedUser = useMemo(() => {
        if (!currentUser) return null;

        return {
            ...currentUser,
            addresses: currentUser.addresses || [],
            coins: currentUser.coins || 0,
            totalCoins: currentUser.totalCoins || currentUser.coins || 0,
            availableCoins: currentUser.availableCoins || currentUser.totalCoins || currentUser.coins || 0
        };
    }, [currentUser]);

    // Optimized address count calculation
    const addressCount = useMemo(() => {
        if (!processedUser) return 0;

        let count = processedUser.addresses?.length || 0;

        // Only add main address if it exists, has meaningful data, and is not already counted in addresses array
        const hasMainAddress = processedUser.address && (
            processedUser.address.doorNo ||
            processedUser.address.streetName ||
            processedUser.address.area ||
            processedUser.address.fullAddress
        );

        // Check if main address is already included in addresses array
        const mainAddressInArray = processedUser.addresses?.some(addr =>
            addr.isMain ||
            addr._id === 'main-address' ||
            addr._id === 'profile-address' ||
            (addr.doorNo === processedUser.address?.doorNo &&
             addr.streetName === processedUser.address?.streetName &&
             addr.area === processedUser.address?.area)
        );

        // Only add 1 if main address exists and is not already counted
        if (hasMainAddress && !mainAddressInArray) {
            count += 1;
        }

        return count;
    }, [processedUser]);

    // Optimized coins count calculation
    const coinsCount = useMemo(() => {
        if (!processedUser) return 0;

        if (typeof processedUser.totalCoins === 'number') {
            return processedUser.totalCoins;
        }

        if (Array.isArray(processedUser.coins)) {
            return processedUser.coins.reduce((sum, coin) => sum + (coin.amount || 0), 0);
        }

        return typeof processedUser.coins === 'number' ? processedUser.coins : 0;
    }, [processedUser]);

    // Effect to update loading state when currentUser changes
    useEffect(() => {
        if (currentUser) {
            setLoading(false);
            setError(null);
        }
    }, [currentUser]);

    // Optimized focus effect - only refresh if no user data
    useFocusEffect(
        useCallback(() => {
            if (!currentUser && !loading) {
                setLoading(true);
                setError(null);

                refreshUserData().catch((error) => {
                    setError('Failed to load profile data');
                    setLoading(false);
                });
            }
        }, [currentUser, loading, refreshUserData])
    );

    // Optimized pull-to-refresh
    const onRefresh = useCallback(async () => {
        setRefreshing(true);
        setError(null);

        try {
            await refreshUserData();
        } catch (error) {
            setError('Failed to refresh profile data');
        } finally {
            setRefreshing(false);
        }
    }, [refreshUserData]);

    // Handle edit profile
    const handleEditProfile = useCallback(() => {
        if (!processedUser) {
            Alert.alert("Error", "Unable to edit profile. Please try again later.");
            return;
        }
        navigation.navigate('EditProfileScreen');
    }, [processedUser, navigation]);

    // Optimized logout handler with animated modal
    const handleLogout = useCallback(() => {
        setShowLogoutModal(true);

        // Animate the modal appearance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 8,
                tension: 40,
                useNativeDriver: true,
            })
        ]).start();
    }, [fadeAnim, scaleAnim]);

    const confirmLogout = useCallback(async () => {
        try {
            clearCart();
            await authLogout();
            navigation.reset({
                index: 0,
                routes: [{ name: 'PreLoginScreen' }],
            });
        } catch (error) {
            navigation.reset({
                index: 0,
                routes: [{ name: 'PreLoginScreen' }],
            });
        }
    }, [clearCart, authLogout, navigation]);

    const cancelLogout = useCallback(() => {
        // Animate the modal disappearance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start(() => {
            setShowLogoutModal(false);
        });
    }, [fadeAnim, scaleAnim]);

    // Optimized menu items with memoization
    const menuItems = useMemo(() => [
        {
            id: 'orders',
            title: 'My Orders',
            subtitle: 'View your order history',
            icon: 'receipt-long',
            onPress: () => {
                if (!processedUser) {
                    Alert.alert("Error", "Please log in to view your orders");
                    return;
                }
                navigation.navigate('Orders');
            }
        },
        {
            id: 'addresses',
            title: 'My Addresses',
            subtitle: `${addressCount} saved ${addressCount === 1 ? 'address' : 'addresses'}`,
            icon: 'location-on',
            onPress: () => {
                if (!processedUser) {
                    Alert.alert("Error", "Please log in to manage your addresses");
                    return;
                }
                navigation.navigate('AddressScreen');
            }
        },
        {
            id: 'coins',
            title: 'Fresh Coins',
            subtitle: `${coinsCount} coins available to use`,
            icon: 'monetization-on',
            onPress: () => {
                if (!processedUser) {
                    Alert.alert("Error", "Please log in to view your coins");
                    return;
                }
                navigation.navigate('CoinsScreen');
            }
        },

        {
            id: 'settings',
            title: 'Settings',
            subtitle: 'App preferences and account settings',
            icon: 'settings',
            onPress: () => navigation.navigate('Settings')
        }
    ], [processedUser, addressCount, coinsCount, navigation]);

    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" />

            {/* Fixed Header */}
            <View className="bg-madder pt-12 pb-4 rounded-b-3xl px-4">
                <View className="flex-row items-center justify-between mb-2">
                    <Text className="text-xl text-white font-bold">My Profile</Text>
                    <TouchableOpacity
                        className="bg-white/20 p-2 rounded-full"
                        onPress={() => navigation.navigate('Settings')}
                    >
                        <MaterialIcons name="settings" size={22} color="white" />
                    </TouchableOpacity>
                </View>

                {loading ? (
                    <View className="flex-row items-center py-2">
                        <View className="w-16 h-16 rounded-full bg-white/30 items-center justify-center mr-4">
                            <ActivityIndicator size="small" color="white" />
                        </View>
                        <View className="flex-1">
                            <View className="bg-white/30 h-5 w-32 rounded-md mb-2"></View>
                            <View className="bg-white/20 h-4 w-24 rounded-md"></View>
                        </View>
                    </View>
                ) : (
                    <View className="flex-row items-center">
                        <View className="w-16 h-16 rounded-full bg-white items-center justify-center mr-4">
                            <Text className="text-madder text-2xl font-bold">
                                {processedUser?.name ? processedUser.name.charAt(0).toUpperCase() : '?'}
                            </Text>
                        </View>
                        <View className="flex-1">
                            <Text className="text-white text-lg font-bold">
                                {processedUser?.name || 'Guest User'}
                            </Text>
                            <Text className="text-white/80">
                                {processedUser?.phone || processedUser?.phoneNumber || processedUser?.number || ''}
                            </Text>
                            {processedUser?.address && (processedUser.address.fullAddress ||
                                (processedUser.address.doorNo || processedUser.address.streetName || processedUser.address.area)) && (
                                <Text className="text-white/60 text-xs mt-1 italic" numberOfLines={1}>
                                    {processedUser.address.fullAddress ||
                                    `${processedUser.address.doorNo || ''}${processedUser.address.streetName ? `, ${processedUser.address.streetName}` : ''}${processedUser.address.area ? `, ${processedUser.address.area}` : ''}${processedUser.address.district ? `, ${processedUser.address.district}` : ''}${processedUser.address.pincode ? ` - ${processedUser.address.pincode}` : ''}`.trim()}
                                </Text>
                            )}
                        </View>
                        <TouchableOpacity
                            className="bg-white/20 px-4 py-2 rounded-lg"
                            onPress={handleEditProfile}
                            disabled={loading || !processedUser}
                        >
                            <Text className="text-white font-medium">Edit</Text>
                        </TouchableOpacity>
                    </View>
                )}

                {error && (
                    <View className="mt-2 bg-red-500/20 p-2 rounded-lg">
                        <Text className="text-white text-xs">{error}</Text>
                    </View>
                )}
            </View>

            {/* Main Content */}
            <ScrollView
                className="flex-1"
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={["#A31621"]}
                        tintColor="#A31621"
                    />
                }
            >
                {/* Stats Card */}
                <View className="mx-4 mt-3 bg-white rounded-xl shadow-sm overflow-hidden">
                    <View className="flex-row">
                        <View className="flex-1 p-4 items-center border-r border-gray-100">
                            <Text className="text-2xl font-bold text-madder">
                                {loading ? '...' : coinsCount}
                            </Text>
                            <Text className="text-gray-600 text-sm">Available Coins</Text>
                        </View>
                        <View className="flex-1 p-4 items-center">
                            <Text className="text-2xl font-bold text-madder">
                                {loading ? '...' : addressCount}
                            </Text>
                            <Text className="text-gray-600 text-sm">Addresses</Text>
                        </View>
                    </View>
                </View>

                {/* Error Message */}
                {error && (
                    <View className="mx-4 mt-3 bg-red-50 p-4 rounded-xl">
                        <View className="flex-row items-center">
                            <MaterialIcons name="error-outline" size={24} color="#EF4444" />
                            <Text className="ml-2 text-red-700 font-medium">{error}</Text>
                        </View>
                        <TouchableOpacity
                            className="mt-2 bg-red-100 py-2 rounded-lg items-center"
                            onPress={onRefresh}
                        >
                            <Text className="text-red-700 font-medium">Retry</Text>
                        </TouchableOpacity>
                    </View>
                )}

                {/* Menu Items */}
                <View className="mt-3">
                    {loading ? (
                        // Loading skeleton for menu items
                        Array(4).fill(0).map((_, index) => (
                            <View
                                key={`skeleton-${index}`}
                                className="bg-white mx-4 mb-2.5 p-4 rounded-xl shadow-sm flex-row items-center"
                            >
                                <View className="w-12 h-12 rounded-full bg-gray-100 items-center justify-center mr-4" />
                                <View className="flex-1">
                                    <View className="bg-gray-100 h-4 w-24 rounded-md mb-2" />
                                    <View className="bg-gray-100 h-3 w-32 rounded-md" />
                                </View>
                                <View className="w-6 h-6 rounded-full bg-gray-100" />
                            </View>
                        ))
                    ) : (
                        // Actual menu items
                        menuItems.map((item) => (
                            <TouchableOpacity
                                key={item.id}
                                className={`bg-white mx-4 mb-2.5 p-4 rounded-xl shadow-sm flex-row items-center ${!processedUser && item.id !== 'help' && item.id !== 'settings' ? 'opacity-70' : ''}`}
                                onPress={item.onPress}
                                activeOpacity={0.7}
                                disabled={loading}
                            >
                                <View className="w-12 h-12 rounded-full bg-madder/10 items-center justify-center mr-4">
                                    <MaterialIcons name={item.icon} size={24} color="#A31621" />
                                </View>
                                <View className="flex-1">
                                    <Text className="text-gray-800 font-bold text-base">{item.title}</Text>
                                    <Text className="text-gray-500 text-xs mt-1">{item.subtitle}</Text>
                                </View>
                                <MaterialIcons name="chevron-right" size={24} color="#A31621" />
                            </TouchableOpacity>
                        ))
                    )}
                </View>

                {/* Logout Button */}
                <TouchableOpacity
                    className={`bg-white mx-4 mt-2 mb-6 p-4 rounded-xl shadow-sm flex-row items-center justify-center ${loading ? 'opacity-70' : ''}`}
                    onPress={handleLogout}
                    activeOpacity={0.7}
                    disabled={loading}
                >
                    <MaterialIcons name="logout" size={20} color="#A31621" />
                    <Text className="text-madder font-bold ml-2">Logout</Text>
                </TouchableOpacity>

                {/* App Version */}
                <View className="items-center mb-6">
                    <Text className="text-gray-400 text-xs">Meat Now v1.0.0</Text>
                </View>
            </ScrollView>

            {/* Logout Modal */}
            <Modal
                visible={showLogoutModal}
                transparent={true}
                animationType="none"
                onRequestClose={cancelLogout}
            >
                <Animated.View
                    className="flex-1 justify-center items-center bg-black/30"
                    style={{ opacity: fadeAnim }}
                >
                    <Animated.View
                        className="bg-white rounded-2xl p-6 m-4 items-center"
                        style={{
                            transform: [{ scale: scaleAnim }],
                            width: '80%',
                            maxWidth: 320
                        }}
                    >
                        <View className="w-16 h-16 bg-madder/10 rounded-full items-center justify-center mb-4">
                            <MaterialIcons name="logout" size={32} color="#A31621" />
                        </View>
                        <Text className="text-xl font-bold text-gray-800 mb-2">Logout</Text>
                        <Text className="text-gray-600 text-center mb-6">Are you sure you want to logout from your account?</Text>

                        <View className="flex-row w-full">
                            <TouchableOpacity
                                className="flex-1 bg-gray-200 py-3 rounded-xl mr-2 items-center"
                                onPress={cancelLogout}
                            >
                                <Text className="font-medium text-gray-700">Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                className="flex-1 bg-madder py-3 rounded-xl ml-2 items-center"
                                onPress={confirmLogout}
                            >
                                <Text className="font-medium text-white">Logout</Text>
                            </TouchableOpacity>
                        </View>
                    </Animated.View>
                </Animated.View>
            </Modal>
        </View>
    );
};

export default UserProfileScreen;
