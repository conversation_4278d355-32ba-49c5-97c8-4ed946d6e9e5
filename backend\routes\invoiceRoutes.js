/**
 * Invoice Routes
 *
 * Routes for handling invoice operations.
 */

const express = require('express');
const router = express.Router();
const { processOrderInvoice, retryInvoiceEmail } = require('../services/invoiceService');
const { protect, adminOnly } = require('../middleware/authMiddleware');

/**
 * @route POST /api/invoices/send/:orderId
 * @desc Send invoice for an order
 * @access Private (Admin only)
 */
router.post('/send/:orderId', protect, adminOnly, async (req, res) => {
    try {
        const { orderId } = req.params;

        const result = await processOrderInvoice(orderId);

        if (!result.success) {
            return res.status(400).json({
                success: false,
                message: 'Failed to send invoice',
                error: result.error
            });
        }

        res.status(200).json({
            success: true,
            message: 'Invoice sent successfully'
        });
    } catch (error) {
        console.error('Error in send invoice route:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
});

/**
 * @route POST /api/invoices/retry/:orderId
 * @desc Retry sending invoice for an order
 * @access Private (Admin only)
 */
router.post('/retry/:orderId', protect, adminOnly, async (req, res) => {
    try {
        const { orderId } = req.params;

        const result = await retryInvoiceEmail(orderId);

        if (!result.success) {
            return res.status(400).json({
                success: false,
                message: 'Failed to retry invoice',
                error: result.error
            });
        }

        res.status(200).json({
            success: true,
            message: 'Invoice retry successful'
        });
    } catch (error) {
        console.error('Error in retry invoice route:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
});



module.exports = router;
