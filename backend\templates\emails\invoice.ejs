<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Meat Now Invoice - Order #<%= typeof orderNumber !== 'undefined' ? orderNumber : orderId %></title>
    <style>
        /* Base styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: white;
        }

        /* Container */
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .header h1 {
            color: #A31621;
            margin-bottom: 5px;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            color: #6b7280;
            margin: 0;
        }

        /* Logo */
        .logo {
            text-align: center;
            margin-bottom: 10px;
        }
        .logo img {
            max-width: 100px;
            height: auto;
        }

        /* Invoice details */
        .invoice-details {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .invoice-details h3 {
            margin-top: 0;
            color: #A31621;
            font-size: 18px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .invoice-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .invoice-details td {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .invoice-details tr:last-child td {
            border-bottom: none;
        }
        .invoice-details .label {
            font-weight: 600;
            width: 150px;
            color: #4b5563;
        }

        /* Customer details */
        .customer-details {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .customer-details h3 {
            margin-top: 0;
            color: #A31621;
            font-size: 18px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .customer-details p {
            margin: 5px 0;
            line-height: 1.8;
        }

        /* Items table */
        .items-table-container {
            margin-bottom: 30px;
        }
        .items-table-container h3 {
            color: #A31621;
            font-size: 18px;
            margin-bottom: 15px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        .items-table th {
            background-color: #f3f4f6;
            text-align: left;
            padding: 12px;
            font-weight: 600;
            color: #4b5563;
            border-bottom: 2px solid #e5e7eb;
        }
        .items-table td {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table tr:last-child td {
            border-bottom: none;
        }
        .items-table .amount {
            text-align: right;
        }
        .items-table .quantity {
            text-align: center;
        }
        .items-table .discount {
            color: #16a34a;
        }

        /* Price summary */
        .price-summary {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .price-summary h3 {
            margin-top: 0;
            color: #A31621;
            font-size: 18px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .price-summary table {
            width: 100%;
            border-collapse: collapse;
        }
        .price-summary td {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .price-summary tr:last-child td {
            border-bottom: none;
        }
        .price-summary .label {
            font-weight: normal;
            color: #4b5563;
        }
        .price-summary .amount {
            text-align: right;
            font-weight: 600;
        }
        .price-summary .discount {
            color: #16a34a;
        }
        .price-summary .total-row {
            border-top: 2px solid #e5e7eb;
            font-weight: 700;
        }
        .price-summary .total-row .label {
            font-weight: 700;
            color: #111827;
        }
        .price-summary .total-row .amount {
            color: #A31621;
            font-size: 18px;
        }

        /* Coins info */
        .coins-info {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #fffbeb;
            border-radius: 8px;
            border: 1px solid #fcd34d;
            border-left: 4px solid #f59e0b;
        }
        .coins-info h4 {
            color: #d97706;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .coins-info p {
            margin: 5px 0;
        }

        /* Thank you */
        .thank-you {
            text-align: center;
            margin: 40px 0;
        }
        .thank-you p {
            font-size: 18px;
            color: #A31621;
            font-weight: 600;
        }

        /* Footer */
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="https://res.cloudinary.com/dmjwppr6t/image/upload/v1747812882/logo.png" alt="MeatNow Logo">
        </div>

        <div class="header">
            <h1>INVOICE</h1>
            <p>Thank you for your order with MeatNow!</p>
        </div>

        <div class="invoice-details">
            <h3>Invoice Information</h3>
            <table>
                <tr>
                    <td class="label">Invoice Number:</td>
                    <td><%= invoiceNumber %></td>
                </tr>
                <tr>
                    <td class="label">Order Number:</td>
                    <td><strong>#<%= typeof orderNumber !== 'undefined' ? orderNumber : orderId %></strong></td>
                </tr>
                <tr>
                    <td class="label">Order Date:</td>
                    <td><%= orderDate %></td>
                </tr>
                <tr>
                    <td class="label">Payment Method:</td>
                    <td><%= paymentMethod.toUpperCase() %></td>
                </tr>
            </table>
        </div>

        <div class="customer-details">
            <h3>Customer Details</h3>
            <p>
                <strong>Name:</strong> <%= customerName %><br>
                <% if (customerEmail) { %>
                <strong>Email:</strong> <%= customerEmail %><br>
                <% } %>
                <strong>Phone:</strong> <%= customerPhone %><br>
                <strong>Delivery Address:</strong><br>
                <%= deliveryAddress %>
            </p>
        </div>

        <div class="items-table-container">
            <h3>Order Items</h3>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Item</th>
                        <th class="quantity">Qty</th>
                        <th>Price</th>
                        <th class="amount">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <% items.forEach(item => { %>
                    <tr>
                        <td><%= item.name %></td>
                        <td class="quantity"><%= item.quantity %></td>
                        <td>
                            <%
                            // Ensure price and discount_price are defined and valid numbers
                            const price = typeof item.price === 'number' ? item.price : 0;
                            const discountPrice = typeof item.discount_price === 'number' ? item.discount_price : price;

                            if (discountPrice && discountPrice < price) {
                            %>
                                <span style="text-decoration: line-through; color: #9ca3af;">₹<%= price.toFixed(2) %></span>
                                <span class="discount">₹<%= discountPrice.toFixed(2) %></span>
                            <% } else { %>
                                ₹<%= price.toFixed(2) %>
                            <% } %>
                        </td>
                        <td class="amount">₹<%= (item.quantity * (discountPrice || price)).toFixed(2) %></td>
                    </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <div class="price-summary">
            <h3>Price Details</h3>
            <table>
                <tr>
                    <td class="label">Discounted Price:</td>
                    <td class="amount">₹<%= originalAmount.toFixed(2) %></td>
                </tr>
                <% if (couponDiscount > 0) { %>
                <tr>
                    <td class="label">Coupon Discount <% if (appliedCoupon) { %>(<%= appliedCoupon %>)<% } %>:</td>
                    <td class="amount discount">-₹<%= couponDiscount.toFixed(2) %></td>
                </tr>
                <% } %>
                <% if (coinsDiscount > 0) { %>
                <tr>
                    <td class="label">Coins Discount:</td>
                    <td class="amount discount">-₹<%= coinsDiscount.toFixed(2) %></td>
                </tr>
                <% } %>
                <% if (typeof deliveryFee !== 'undefined' && deliveryFee > 0) { %>
                <tr>
                    <td class="label">Delivery Fee:</td>
                    <td class="amount">₹<%= deliveryFee.toFixed(2) %></td>
                </tr>
                <% } %>
                <tr class="total-row">
                    <td class="label">Grand Total:</td>
                    <td class="amount">₹<%= totalAmount.toFixed(2) %></td>
                </tr>
            </table>
        </div>



        <div class="thank-you">
            <p>Thank you for your order!</p>
        </div>

        <div class="footer">
            <p>
                MeatNow - Fresh Meat Delivered to Your Doorstep<br>
                Contact: <EMAIL> | +91 8825549901<br>
                © <%= new Date().getFullYear() %> MeatNow. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
