import React, { useState, useEffect, useRef } from 'react';
import { View, TextInput, StyleSheet, Animated, Text } from 'react-native';
import { SearchBar } from 'react-native-elements';
import { MaterialIcons } from '@expo/vector-icons';

/**
 * Animated search bar with changing placeholder text that slides from top
 * with smooth transitions between words
 *
 * @param {Object} props
 * @param {string} props.value - Current search value
 * @param {Function} props.onChangeText - Function to call when text changes
 * @param {Function} props.onSubmitEditing - Function to call when search is submitted
 * @param {Function} props.onClear - Function to call when search is cleared
 * @param {Array} props.placeholders - Array of placeholder texts to cycle through
 * @param {number} props.animationDuration - Duration of placeholder animation in ms (default: 3000)
 * @param {string} props.containerStyle - Additional styles for container
 */
const AnimatedSearchBar = ({
    value,
    onChangeText,
    onSubmitEditing,
    onClear,
    placeholders = ["Chicken", "<PERSON>tton", "<PERSON>", "Prawn"],
    animationDuration = 3000,
    containerStyle = ""
}) => {
    // Animation values
    const slideAnim = useRef(new Animated.Value(0)).current;
    const opacityAnim = useRef(new Animated.Value(1)).current;
    const scaleAnim = useRef(new Animated.Value(1)).current;
    const rotateAnim = useRef(new Animated.Value(0)).current;
    const [currentPlaceholder, setCurrentPlaceholder] = useState(placeholders[0]);
    const [nextPlaceholder, setNextPlaceholder] = useState(placeholders[1]);
    const [placeholderIndex, setPlaceholderIndex] = useState(0);

    // Start the placeholder animation cycle - only change text without animation
    useEffect(() => {
        let isMounted = true;

        // Initialize animation values to static position
        slideAnim.setValue(0);
        opacityAnim.setValue(1);
        scaleAnim.setValue(1);
        rotateAnim.setValue(0);

        const changePlaceholder = () => {
            if (!isMounted) return;

            // Simply update placeholder text without animation
            const nextIndex = (placeholderIndex + 1) % placeholders.length;
            setPlaceholderIndex(nextIndex);
            setCurrentPlaceholder(placeholders[nextIndex]);
            setNextPlaceholder(placeholders[(nextIndex + 1) % placeholders.length]);
        };

        // Set up interval for changing placeholder text
        const interval = setInterval(() => {
            if (isMounted && !value) { // Only change when there's no search text
                changePlaceholder();
            }
        }, animationDuration);

        // Clean up
        return () => {
            isMounted = false;
            clearInterval(interval);
        };
    }, [placeholderIndex, placeholders, value, animationDuration]);

    // Handle search clear
    const handleClear = () => {
        if (onClear) {
            onClear();
        }
    };

    return (
        <View style={[styles.container, containerStyle]}>
            <SearchBar
                placeholder={`Search for "${currentPlaceholder}"`}
                onChangeText={onChangeText}
                value={value}
                containerStyle={styles.searchBarContainer}
                inputContainerStyle={styles.inputContainer}
                inputStyle={styles.input}
                searchIcon={
                    <MaterialIcons name="search" size={24} color="#A31621" />
                }
                clearIcon={
                    value ?
                    <MaterialIcons
                        name="clear"
                        size={24}
                        color="#A31621"
                        onPress={handleClear}
                    /> : null
                }
                placeholderTextColor="#888"
                onSubmitEditing={onSubmitEditing}
                platform="default"
            />

            {/* Static placeholder overlay (only visible when no text) */}
            {!value && (
                <View
                    style={styles.placeholderOverlay}
                    pointerEvents="none"
                >
                    <Text style={styles.placeholderText}>
                        Search for "{currentPlaceholder}"
                    </Text>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        width: '100%',
        overflow: 'visible', // Changed from 'hidden' to allow shadow to be visible
        paddingBottom: 6, // Add padding to accommodate shadow
        paddingRight: 4, // Add padding for right shadow
    },
    searchBarContainer: {
        backgroundColor: 'transparent',
        borderTopWidth: 0,
        borderBottomWidth: 0,
        paddingHorizontal: 0,
        marginTop: 0,
        marginBottom: 0,
        borderRadius: 12, // Match input container radius
    },
    inputContainer: {
        backgroundColor: '#fff', // Brighter background
        borderRadius: 12, // More rounded corners
        height: 46, // Slightly taller
        borderWidth: 1,
        borderColor: '#eee', // Subtle border
        // Enhanced shadow styling - focused on right and end sides
        shadowColor: '#A31621', // Using madder color for shadow
        shadowOffset: { width: 3, height: 2 }, // Shadow only on right and bottom
        shadowOpacity: 0.15, // Subtle shadow
        shadowRadius: 4, // Soft shadow edge
        elevation: 4, // For Android
    },
    input: {
        fontSize: 15,
        color: '#333',
    },
    placeholderOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'flex-start', // Left align text
        paddingLeft: 52, // Adjust based on your search icon position
        zIndex: -1,
        borderRadius: 12, // Match input container radius
    },
    placeholderText: {
        color: '#555', // Even darker for better contrast
        fontSize: 15,
        fontWeight: '600', // Bolder for better visibility during animation
        letterSpacing: 0.5, // More spaced letters for better readability
    }
});

export default AnimatedSearchBar;
