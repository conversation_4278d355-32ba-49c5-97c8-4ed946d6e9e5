// Utility functions for admin screens

/**
 * Safely format a number value
 * @param {any} value - The value to format
 * @param {number} defaultValue - Default value if invalid
 * @returns {number} - Formatted number
 */
export const safeNumber = (value, defaultValue = 0) => {
    if (value === undefined || value === null || !isFinite(value) || isNaN(value)) {
        return defaultValue;
    }
    return Number(value);
};

/**
 * Format currency value
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency symbol
 * @returns {string} - Formatted currency string
 */
export const formatCurrency = (amount, currency = '₹') => {
    const safeAmount = safeNumber(amount);
    return `${currency}${safeAmount.toFixed(0)}`;
};

/**
 * Format date for display
 * @param {string|Date} dateString - Date to format
 * @param {object} options - Formatting options
 * @returns {string} - Formatted date string
 */
export const formatDate = (dateString, options = { year: 'numeric', month: 'long', day: 'numeric' }) => {
    try {
        return new Date(dateString).toLocaleDateString(undefined, options);
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid Date';
    }
};

/**
 * Get status color classes for UI
 * @param {string} status - Status value
 * @returns {object} - Color classes object
 */
export const getStatusColors = (status) => {
    const statusColors = {
        'pending': { bg: 'bg-yellow-100', text: 'text-yellow-700', icon: 'schedule' },
        'processing': { bg: 'bg-blue-100', text: 'text-blue-700', icon: 'hourglass-empty' },
        'preparing': { bg: 'bg-amber-100', text: 'text-amber-700', icon: 'restaurant' },
        'out_for_delivery': { bg: 'bg-indigo-100', text: 'text-indigo-700', icon: 'local-shipping' },
        'out-for-delivery': { bg: 'bg-indigo-100', text: 'text-indigo-700', icon: 'local-shipping' },
        'delivered': { bg: 'bg-green-100', text: 'text-green-700', icon: 'check-circle' },
        'cancelled': { bg: 'bg-red-100', text: 'text-red-700', icon: 'cancel' }
    };

    return statusColors[status] || { bg: 'bg-gray-100', text: 'text-gray-700', icon: 'help' };
};

/**
 * Get display name for status
 * @param {string} status - Status value
 * @returns {string} - Display name
 */
export const getStatusDisplayName = (status) => {
    const displayNames = {
        'pending': 'Pending',
        'processing': 'Processing',
        'preparing': 'Preparing',
        'out_for_delivery': 'Out for Delivery',
        'out-for-delivery': 'Out for Delivery',
        'delivered': 'Delivered',
        'cancelled': 'Cancelled'
    };

    return displayNames[status] || status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown';
};

/**
 * Map API status to UI status
 * @param {string} status - API status
 * @returns {string} - UI status
 */
export const mapStatusToUI = (status) => {
    if (!status) return 'pending';

    const statusMap = {
        'PENDING': 'pending',
        'PLACED': 'pending',
        'CONFIRMED': 'processing',
        'PROCESSING': 'processing',
        'PREPARING': 'preparing',
        'OUT_FOR_DELIVERY': 'out_for_delivery',
        'DELIVERED': 'delivered',
        'CANCELLED': 'cancelled'
    };

    return statusMap[status.toUpperCase()] || status.toLowerCase();
};

/**
 * Validate product data
 * @param {object} productData - Product data to validate
 * @returns {object} - Validation result
 */
export const validateProductData = (productData) => {
    const errors = [];

    if (!productData.name?.trim()) {
        errors.push('Product name is required');
    }

    if (!productData.price || parseFloat(productData.price) <= 0) {
        errors.push('Valid price is required');
    }

    if (productData.discountPercentage && (parseFloat(productData.discountPercentage) < 0 || parseFloat(productData.discountPercentage) > 100)) {
        errors.push('Discount percentage must be between 0 and 100');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Calculate discount price
 * @param {number} price - Original price
 * @param {number} discountPercentage - Discount percentage
 * @returns {string} - Calculated discount price
 */
export const calculateDiscountPrice = (price, discountPercentage) => {
    if (!price || !discountPercentage) return '';
    const priceValue = parseFloat(price);
    const discountValue = parseFloat(discountPercentage);
    if (isNaN(priceValue) || isNaN(discountValue)) return '';

    const discountedPrice = priceValue * (1 - discountValue / 100);
    return discountedPrice.toFixed(2);
};

/**
 * Calculate discount percentage
 * @param {number} price - Original price
 * @param {number} discountPrice - Discount price
 * @returns {string} - Calculated discount percentage
 */
export const calculateDiscountPercentage = (price, discountPrice) => {
    if (!price || !discountPrice) return '0';
    const priceValue = parseFloat(price);
    const discountPriceValue = parseFloat(discountPrice);
    if (isNaN(priceValue) || isNaN(discountPriceValue) || priceValue <= 0 || discountPriceValue >= priceValue) return '0';

    const discountPercentage = ((priceValue - discountPriceValue) / priceValue) * 100;
    return discountPercentage.toFixed(0);
};

/**
 * Generate offer text
 * @param {number} discountPercentage - Discount percentage
 * @returns {string} - Offer text
 */
export const generateOfferText = (discountPercentage) => {
    if (!discountPercentage || parseFloat(discountPercentage) <= 0) return '';
    return `${parseFloat(discountPercentage).toFixed(0)}% OFF`;
};

/**
 * Debounce function for search inputs
 * @param {function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {function} - Debounced function
 */
export const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

/**
 * Handle async operations with error handling
 * @param {function} asyncOperation - Async function to execute
 * @param {function} onSuccess - Success callback
 * @param {function} onError - Error callback
 * @param {function} onFinally - Finally callback
 */
export const handleAsyncOperation = async (asyncOperation, onSuccess, onError, onFinally) => {
    try {
        const result = await asyncOperation();
        if (onSuccess) onSuccess(result);
        return result;
    } catch (error) {
        console.error('Async operation failed:', error);
        if (onError) onError(error);
        throw error;
    } finally {
        if (onFinally) onFinally();
    }
};

/**
 * Format phone number for display
 * @param {string} phone - Phone number
 * @returns {string} - Formatted phone number
 */
export const formatPhoneNumber = (phone) => {
    if (!phone) return 'No phone';
    
    // Remove any non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Format Indian phone numbers
    if (cleaned.length === 10) {
        return `+91 ${cleaned.slice(0, 5)} ${cleaned.slice(5)}`;
    } else if (cleaned.length === 12 && cleaned.startsWith('91')) {
        return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 7)} ${cleaned.slice(7)}`;
    }
    
    return phone;
};
