/**
 * Migration script to convert single address objects to entries in the addresses array
 * Run this script with: node backend/scripts/migrateAddresses.js
 */

const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB with direct URI
const MONGO_URI = 'mongodb+srv://MeatHub:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

mongoose.connect(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected for migration'))
.catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
});

async function migrateAddresses() {
    try {
        console.log('Starting address migration...');

        // Find users with a single address but no entries in addresses array
        const users = await User.find({
            'address': { $exists: true, $ne: null },
            $or: [
                { 'addresses': { $exists: false } },
                { 'addresses': { $size: 0 } }
            ]
        });

        console.log(`Found ${users.length} users with single address but no addresses array entries`);

        let migratedCount = 0;
        let skippedCount = 0;

        for (const user of users) {
            // Check if the address has any data
            const hasAddressData = user.address.doorNo ||
                                  user.address.streetName ||
                                  user.address.area ||
                                  user.address.district ||
                                  user.address.pincode ||
                                  user.address.fullAddress;

            if (hasAddressData) {
                // Create address object from single address
                const primaryAddress = {
                    type: 'Home',
                    doorNo: user.address.doorNo || '',
                    streetName: user.address.streetName || '',
                    area: user.address.area || '',
                    district: user.address.district || '',
                    pincode: user.address.pincode || '',
                    fullAddress: user.address.fullAddress ||
                        `${user.address.doorNo || ''}, ${user.address.streetName || ''}, ${user.address.area || ''}, ${user.address.district || ''}, ${user.address.pincode || ''}`.replace(/\s+/g, ' ').trim(),
                    isPrimary: true,
                    isDefault: false, // Keep for backward compatibility
                    createdAt: new Date()
                };

                // Initialize addresses array if it doesn't exist
                if (!user.addresses) {
                    user.addresses = [];
                }

                // Add to addresses array
                user.addresses.push(primaryAddress);

                // Save the user
                await user.save();
                migratedCount++;
                console.log(`Migrated address for user: ${user._id} (${user.name || 'unnamed'})`);
            } else {
                skippedCount++;
                console.log(`Skipped user: ${user._id} - No valid address data`);
            }
        }

        // Find users with addresses in the array but none marked as primary
        const usersWithoutPrimary = await User.find({
            'addresses': { $exists: true, $ne: [] },
            'addresses': { $not: { $elemMatch: { isPrimary: true } } }
        });

        console.log(`Found ${usersWithoutPrimary.length} users with addresses but none marked as primary`);

        let primarySetCount = 0;

        for (const user of usersWithoutPrimary) {
            // Check if any address is marked as default
            const defaultAddress = user.addresses.find(addr => addr.isDefault);

            if (defaultAddress) {
                // Set the default address as primary
                defaultAddress.isPrimary = true;
            } else if (user.addresses.length > 0) {
                // Set the first address as primary
                user.addresses[0].isPrimary = true;
            }

            // Save the user
            await user.save();
            primarySetCount++;
            console.log(`Set primary address for user: ${user._id} (${user.name || 'unnamed'})`);
        }

        console.log('\nMigration Summary:');
        console.log(`- ${migratedCount} users had their single address migrated to the addresses array`);
        console.log(`- ${skippedCount} users were skipped due to invalid address data`);
        console.log(`- ${primarySetCount} users had a primary address designated in their addresses array`);
        console.log('\nMigration completed successfully!');

    } catch (error) {
        console.error('Error during migration:', error);
    } finally {
        // Close the MongoDB connection
        mongoose.connection.close();
        console.log('MongoDB connection closed');
    }
}

// Run the migration
migrateAddresses();
