import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, KeyboardAvoidingView, Platform, ScrollView, ActivityIndicator, Alert } from 'react-native';
import axios from 'axios';
import { API_URL } from '../config/constants';
import { getAuthToken } from '../utils/authStorage';

const UsernameScreen = ({ route, navigation }) => {
    const [username, setUsername] = useState('');
    const [email, setEmail] = useState('');
    const [error, setError] = useState('');
    const [emailError, setEmailError] = useState('');
    const [loading, setLoading] = useState(false);
    const { phoneNumber, userId } = route.params || {};

    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleContinue = async () => {
        // Reset errors
        setError('');
        setEmailError('');

        // Validate inputs
        if (!username.trim()) {
            setError('Please enter your name');
            return;
        }

        if (!email.trim()) {
            setEmailError('Please enter your email');
            return;
        } else if (!validateEmail(email.trim())) {
            setEmailError('Please enter a valid email address');
            return;
        }

        setLoading(true);
        console.log('UsernameScreen: Updating profile with', { username, email, phoneNumber, userId });

        try {
            // Get the auth token from AsyncStorage
            const token = await getAuthToken();
            console.log('UsernameScreen: Auth token', token ? 'exists' : 'does not exist');

            // Use the token to update the profile with name and email
            console.log('Sending update-profile request with email:', email.trim());
            const response = await axios.post(
                `${API_URL}/auth/update-profile`,
                {
                    name: username.trim(),
                    email: email.trim()
                },
                {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                }
            );
            console.log('Update profile response:', response.data);

            // Navigate to the address details screen
            navigation.navigate('AddressDetailsScreen', {
                name: username.trim(),
                email: email.trim(),
                phoneNumber,
                userId
            });
        } catch (error) {
            console.error('Error updating profile:', error);
            let errorMessage = 'Failed to update profile. Please try again.';

            if (error.response && error.response.data && error.response.data.message) {
                errorMessage = error.response.data.message;
            }

            Alert.alert(
                "Error",
                errorMessage,
                [{ text: "OK" }]
            );
        } finally {
            setLoading(false);
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            className="flex-1 bg-white"
        >
            {/* Unique asymmetric design elements */}
            <View className="absolute top-0 right-0 w-[70%] h-[220px] overflow-hidden">
                <View className="absolute w-[300px] h-[300px] rounded-full bg-madder opacity-[0.08] top-[-100px] right-[-50px]" />
            </View>

            <View className="absolute bottom-0 left-0 w-[50%] h-[200px] overflow-hidden">
                <View className="absolute w-[250px] h-[250px] rounded-full bg-madder opacity-[0.05] bottom-[-100px] left-[-50px]" />
            </View>

            <ScrollView
                className="flex-1"
                contentContainerClassName="px-6 pt-12 pb-6"
                keyboardShouldPersistTaps="handled"
            >
                {/* Logo with unique presentation */}
                <View className="items-center mt-16 mb-10">
                    <View className="w-[80px] h-[80px] rounded-xl bg-white items-center justify-center shadow-md"
                        style={{
                            transform: [{ rotate: '-5deg' }],
                            elevation: 4,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.1,
                            shadowRadius: 4,
                        }}>
                        <Image
                            source={require('../assets/logo.png')}
                            className="w-[60px] h-[60px]"
                            resizeMode="contain"
                        />
                    </View>
                </View>

                {/* Header text */}
                <View className="mb-6">
                    <Text className="text-2xl font-bold text-gray-800 text-center mb-2">
                        Nice to Meat You!
                    </Text>
                    <Text className="text-sm text-gray-600 text-center">
                        Please enter your details to continue
                    </Text>
                </View>

                {/* Name Input */}
                <View className="mb-5">
                    <Text className="text-sm text-gray-700 mb-1 ml-9">Full Name</Text>
                    <View className="border border-gray-200 rounded-xl px-5 py-3 bg-white shadow-sm mx-8"
                        style={{
                            elevation: 2,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 1 },
                            shadowOpacity: 0.05,
                            shadowRadius: 2,
                        }}>
                        <TextInput
                            placeholder="Enter your full name"
                            className="flex-1 text-base text-gray-800"
                            value={username}
                            onChangeText={(text) => {
                                setUsername(text);
                                setError('');
                            }}
                            autoCapitalize="words"
                            placeholderTextColor="#9CA3AF"
                        />
                    </View>
                    {error ? (
                        <Text className="text-red-500 text-sm mt-1 ml-9">{error}</Text>
                    ) : null}
                </View>

                {/* Email Input */}
                <View className="mb-8">
                    <Text className="text-sm text-gray-700 mb-1 ml-9">Email Address</Text>
                    <View className="border border-gray-200 rounded-xl px-5 py-3 bg-white shadow-sm mx-8"
                        style={{
                            elevation: 2,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 1 },
                            shadowOpacity: 0.05,
                            shadowRadius: 2,
                        }}>
                        <TextInput
                            placeholder="Enter your email address"
                            className="flex-1 text-base text-gray-800"
                            value={email}
                            onChangeText={(text) => {
                                setEmail(text);
                                setEmailError('');
                            }}
                            autoCapitalize="none"
                            keyboardType="email-address"
                            placeholderTextColor="#9CA3AF"
                        />
                    </View>
                    {emailError ? (
                        <Text className="text-red-500 text-sm mt-1 ml-9">{emailError}</Text>
                    ) : null}
                </View>

                {/* Continue Button */}
                <View className="items-center justify-center mb-6">
                    <TouchableOpacity
                        className="bg-madder py-4 px-8 rounded-lg w-[65%] items-center shadow-md"
                        style={{
                            elevation: 3,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.1,
                            shadowRadius: 3,
                        }}
                        onPress={handleContinue}
                        disabled={loading}
                    >
                        {loading ? (
                            <ActivityIndicator size="small" color="#FFFFFF" />
                        ) : (
                            <View className="flex-row items-center">
                                <Text className="text-white text-base font-semibold tracking-wide">Next Step</Text>
                                <Text className="text-white text-lg ml-2">→</Text>
                            </View>
                        )}
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default UsernameScreen;
