import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    ActivityIndicator,
    Alert,
    RefreshControl,
    PanResponder,
    Dimensions
} from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useAdmin } from '../../context/AdminContext';
import { getAllUsers } from '../../utils/api/userApi';
import { getOrdersByUserId } from '../../utils/api/orderApi';

const UsersScreen = () => {
    const navigation = useNavigation();
    const { orders: allOrders } = useAdmin();

    // Initialize users state
    const [users, setUsers] = useState([]);
    const [userOrders, setUserOrders] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [refreshing, setRefreshing] = useState(false);

    const [searchQuery, setSearchQuery] = useState('');
    const [filterStatus, setFilterStatus] = useState('all'); // Keep this for now to avoid errors

    // Reference for FlatList
    const flatListRef = useRef(null);

    // Screen width for swipe calculations
    const screenWidth = Dimensions.get('window').width;

    // Fetch users data from the API
    const fetchUsers = useCallback(async (showFullLoading = true) => {
        try {
            if (showFullLoading) {
                setLoading(true);
            }
            setError(null);

            // Fetch users from the API
            const response = await getAllUsers();

            if (response && response.users) {
                console.log(`Fetched ${response.users.length} users from API`);
                const fetchedUsers = response.users;
                setUsers(fetchedUsers);

                // Fetch orders for each user
                const ordersMap = {};
                for (const user of fetchedUsers) {
                    const userId = user.id || user._id;
                    try {
                        console.log(`Fetching orders for user: ${userId}`);
                        const ordersResponse = await getOrdersByUserId(userId);
                        if (ordersResponse && ordersResponse.orders) {
                            ordersMap[userId] = ordersResponse.orders;
                            console.log(`Found ${ordersResponse.orders.length} orders for user ${userId}`);
                        } else {
                            ordersMap[userId] = [];
                        }
                    } catch (orderError) {
                        console.error(`Error fetching orders for user ${userId}:`, orderError);
                        ordersMap[userId] = [];
                    }
                }

                setUserOrders(ordersMap);
            } else {
                console.log('No users returned from API');
                setUsers([]);
                setUserOrders({});
            }
        } catch (error) {
            console.error('Error fetching users:', error);
            setError('Failed to load users. Please try again.');
            setUsers([]);
            setUserOrders({});
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, []);

    // Initial fetch on mount
    useEffect(() => {
        fetchUsers();

        // Cleanup function to prevent memory leaks
        return () => {
            setUsers([]);
            setUserOrders({});
            setLoading(false);
            setRefreshing(false);
        };
    }, [fetchUsers]);

    // Refresh data when screen comes into focus
    useFocusEffect(
        useCallback(() => {
            fetchUsers(false);
            return () => {};
        }, [fetchUsers])
    );

    // Handle pull-to-refresh
    const handleRefresh = useCallback(() => {
        setRefreshing(true);
        fetchUsers(false);
    }, [fetchUsers]);

    // Simple pan responder for scrolling
    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => false,
            onMoveShouldSetPanResponder: () => false
        })
    ).current;

    // Filter users based on search query only with null checks - memoized for performance
    const filteredUsers = React.useMemo(() => {
        if (!users || users.length === 0) return [];

        if (!searchQuery) return users;

        return users.filter(user => {
            // Add null checks for all properties
            const userName = user?.name || '';
            const userPhone = user?.phone || user?.number || '';
            const userEmail = user?.email || '';
            const userId = user?.id || user?._id || '';

            const searchQueryLower = searchQuery.toLowerCase();

            const matchesSearch =
                userName.toLowerCase().includes(searchQueryLower) ||
                userPhone.includes(searchQuery) ||
                (userEmail && userEmail.toLowerCase().includes(searchQueryLower)) ||
                userId.toLowerCase().includes(searchQueryLower);

            return matchesSearch;
        });
    }, [users, searchQuery]);

    // Get order stats for a user - memoized for performance
    const getUserOrderStats = useCallback((userId) => {
        if (!userId || !userOrders[userId]) {
            return {
                totalOrders: 0,
                lastOrderText: 'Never'
            };
        }

        const orders = userOrders[userId];

        // If we have orders, calculate the last order date
        if (orders.length > 0) {
            try {
                const sortedOrders = [...orders].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                const lastOrder = sortedOrders[0];
                const lastOrderDate = new Date(lastOrder.createdAt);
                const now = new Date();
                const diffTime = Math.abs(now - lastOrderDate);
                const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

                let lastOrderText = '';
                if (diffDays === 0) {
                    lastOrderText = 'Today';
                } else if (diffDays === 1) {
                    lastOrderText = 'Yesterday';
                } else if (diffDays < 7) {
                    lastOrderText = `${diffDays} days ago`;
                } else if (diffDays < 30) {
                    const weeks = Math.floor(diffDays / 7);
                    lastOrderText = `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
                } else {
                    const months = Math.floor(diffDays / 30);
                    lastOrderText = `${months} ${months === 1 ? 'month' : 'months'} ago`;
                }

                return {
                    totalOrders: orders.length,
                    lastOrderText
                };
            } catch (error) {
                console.error('Error calculating order stats:', error);
                return {
                    totalOrders: orders.length,
                    lastOrderText: 'Unknown'
                };
            }
        }

        return {
            totalOrders: 0,
            lastOrderText: 'Never'
        };
    }, [userOrders]);

    const renderUserItem = ({ item }) => {
        // Handle different property names from API with additional null checks
        const userId = item.id || item._id || '';
        const userName = item.name || item.fullName || 'Unknown';
        const userPhone = item.phone || item.phoneNumber || item.number || 'No phone';
        const userEmail = item.email || '';

        const { totalOrders, lastOrderText } = getUserOrderStats(userId);

        return (
            <TouchableOpacity
                className="bg-white rounded-xl mb-4 overflow-hidden shadow-sm"
                style={{
                    shadowColor: "#000",
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.1,
                    shadowRadius: 3,
                    elevation: 3
                }}
                onPress={() => navigation.navigate('UserDetailScreen', { userId: userId })}
                activeOpacity={0.7}
            >
                <View className="p-4">
                    <View className="flex-row items-center">
                        <View className="w-14 h-14 rounded-full bg-madder/10 items-center justify-center mr-4">
                            <Text className="text-2xl font-bold text-madder">
                                {userName && userName.length > 0 ? userName.charAt(0).toUpperCase() : '?'}
                            </Text>
                        </View>

                        <View className="flex-1">
                            <View className="flex-row items-center justify-between">
                                <Text className="text-lg font-bold text-gray-800">{userName}</Text>
                            </View>

                            <View className="flex-row items-center mt-1">
                                <MaterialIcons name="phone" size={14} color="#666" />
                                <Text className="text-gray-600 ml-1">{userPhone}</Text>
                            </View>

                            {userEmail && (
                                <View className="flex-row items-center mt-1">
                                    <MaterialIcons name="email" size={14} color="#666" />
                                    <Text className="text-gray-600 ml-1">{userEmail}</Text>
                                </View>
                            )}
                        </View>
                    </View>
                </View>

                <View className="bg-madder/5 px-4 py-3 flex-row justify-between items-center">
                    <View className="flex-row items-center">
                        <MaterialIcons name="shopping-bag" size={16} color="#A31621" />
                        <Text className="ml-1 text-gray-700 font-medium">{totalOrders} orders</Text>
                    </View>

                    <View className="flex-row items-center">
                        <MaterialIcons name="access-time" size={16} color="#A31621" />
                        <Text className="ml-1 text-gray-700">Last: {lastOrderText}</Text>
                    </View>

                    <View className="flex-row items-center">
                        <Text className="text-madder mr-1 text-sm font-medium">Details</Text>
                        <MaterialIcons name="chevron-right" size={16} color="#A31621" />
                    </View>
                </View>
            </TouchableOpacity>
        );
    };

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-4 pt-16 rounded-b-3xl pb-4">
                <View className="flex-row items-center justify-between mb-4">
                    <View className="flex-row items-center">
                        <TouchableOpacity
                            onPress={() => navigation.goBack()}
                            className="mr-4 bg-white/20 p-2 rounded-full"
                        >
                            <MaterialIcons name="arrow-back" size={24} color="white" />
                        </TouchableOpacity>
                        <Text className="text-2xl text-white font-bold">Users</Text>
                    </View>
                </View>

                <View
                    className="bg-snow rounded-xl flex-row items-center px-4 mr-1 mb-1"
                    style={{
                        shadowColor: '#A31621',
                        shadowOffset: { width: 3, height: 2 },
                        shadowOpacity: 0.15,
                        shadowRadius: 4,
                        elevation: 4,
                    }}
                >
                    <MaterialIcons name="search" size={24} color="#A31621" />
                    <TextInput
                        placeholder="Search by name, phone or email..."
                        className="flex-1 p-4"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor="#9CA3AF"
                        style={{ color: "#333" }}
                    />
                    {searchQuery.length > 0 && (
                        <TouchableOpacity
                            onPress={() => setSearchQuery('')}
                            className="bg-gray-100 p-1 rounded-full"
                        >
                            <MaterialIcons name="close" size={20} color="#A31621" />
                        </TouchableOpacity>
                    )}
                </View>
            </View>

            <View className="bg-white mx-4 mt-4 mb-2 rounded-xl p-1 shadow-sm flex-row">
                <View className="flex-1 py-3 rounded-lg bg-madder">
                    <Text className="text-center font-medium text-white">
                        All Users
                    </Text>
                </View>
            </View>

            {loading ? (
                <View className="flex-1 justify-center items-center">
                    <ActivityIndicator size="large" color="#A31621" />
                    <Text className="text-gray-600 mt-4">Loading users...</Text>
                </View>
            ) : error ? (
                <View className="flex-1 justify-center items-center p-4">
                    <MaterialIcons name="error-outline" size={48} color="#A31621" />
                    <Text className="text-gray-700 mt-4 text-center text-lg">{error}</Text>
                    <TouchableOpacity
                        className="mt-6 bg-madder px-6 py-3 rounded-xl"
                        onPress={() => fetchUsers()}
                    >
                        <Text className="text-white font-medium">Retry</Text>
                    </TouchableOpacity>
                </View>
            ) : (
                <FlatList
                    ref={flatListRef}
                    className="px-4 pt-2"
                    data={filteredUsers}
                    keyExtractor={item => item.id || item._id}
                    renderItem={renderUserItem}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: 100 }}
                    {...panResponder.panHandlers}
                    ListEmptyComponent={
                        <View className="items-center justify-center py-10 bg-white rounded-xl mt-4 shadow-sm">
                            <MaterialIcons name="person-off" size={64} color="#A31621" opacity={0.2} />
                            <Text className="text-gray-700 mt-4 text-lg font-bold">No users found</Text>
                            <Text className="text-gray-500 text-center mt-2 px-6">
                                {searchQuery
                                    ? "Try a different search term or clear the filter"
                                    : "No users in the system yet"
                                }
                            </Text>
                        </View>
                    }
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={handleRefresh}
                            colors={["#A31621"]}
                            tintColor="#A31621"
                        />
                    }
                />
            )}

            {filteredUsers.length > 0 && !loading && (
                <View className="absolute bottom-6 left-6 right-6 flex-row justify-center">
                    <View className="bg-white/90 backdrop-blur-md px-4 py-2 rounded-full shadow-sm">
                        <Text className="text-gray-700 font-medium">
                            {filteredUsers.length} {filteredUsers.length === 1 ? 'user' : 'users'} found
                        </Text>
                    </View>
                </View>
            )}
        </View>
    );
};

export default UsersScreen;