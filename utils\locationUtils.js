/**
 * Location Utility Functions
 *
 * This file contains utility functions for handling location-based operations
 * including geofencing, distance calculations, and location permissions.
 */

import * as Location from 'expo-location';
import { Alert, Linking, Platform } from 'react-native';
import { validPincodes, centralPoints, maxDeliveryRadius, deliveryZonePolygons, pincodeAreas } from '../config/deliveryZones';

/**
 * Request location permissions from the user for map usage only
 * @returns {Promise<boolean>} Whether permission was granted
 */
export const requestLocationPermission = async () => {
  try {
    const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();

    if (foregroundStatus !== 'granted') {
      Alert.alert(
        "Location Permission for Map",
        "We need your location only when you're selecting your delivery address on the map. No background tracking.",
        [
          {
            text: "Cancel",
            style: "cancel"
          },
          {
            text: "Open Settings",
            onPress: () => {
              // Open app settings
              if (Platform.OS === 'ios') {
                Linking.openURL('app-settings:');
              } else {
                Linking.openSettings();
              }
            }
          }
        ]
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
};

/**
 * Get the current location of the user
 * @returns {Promise<Object|null>} Location object or null if unavailable
 */
export const getCurrentLocation = async () => {
  try {
    const hasPermission = await requestLocationPermission();

    if (!hasPermission) {
      return null;
    }

    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.High
    });

    return {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude
    };
  } catch (error) {
    console.error('Error getting current location:', error);
    return null;
  }
};

/**
 * Calculate distance between two coordinates using the Haversine formula
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} Distance in kilometers
 */
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  return distance;
};

/**
 * Convert degrees to radians
 * @param {number} deg - Degrees
 * @returns {number} Radians
 */
const deg2rad = (deg) => {
  return deg * (Math.PI/180);
};

/**
 * Check if a location is within the delivery radius of any central point
 * @param {number} latitude - Latitude to check
 * @param {number} longitude - Longitude to check
 * @returns {boolean} Whether the location is within delivery radius
 */
export const isWithinDeliveryRadius = (latitude, longitude) => {
  if (!latitude || !longitude) return false;

  // Check distance from each central point
  for (const point of centralPoints) {
    const distance = calculateDistance(
      point.latitude,
      point.longitude,
      latitude,
      longitude
    );

    // If within radius of any central point, return true
    if (distance <= maxDeliveryRadius) {
      return true;
    }
  }

  // Not within radius of any central point
  return false;
};

/**
 * Check if a point is inside a polygon using ray casting algorithm
 * @param {Object} point - Point with latitude and longitude
 * @param {Array} polygon - Array of points forming a polygon
 * @returns {boolean} Whether the point is inside the polygon
 */
export const isPointInPolygon = (point, polygon) => {
  if (!point || !point.latitude || !point.longitude) return false;

  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].latitude;
    const yi = polygon[i].longitude;
    const xj = polygon[j].latitude;
    const yj = polygon[j].longitude;

    const intersect = ((yi > point.longitude) !== (yj > point.longitude)) &&
      (point.latitude < (xj - xi) * (point.longitude - yi) / (yj - yi) + xi);

    if (intersect) inside = !inside;
  }

  return inside;
};

/**
 * Check if a location is within any delivery zone
 * @param {Object} location - Location with latitude and longitude
 * @returns {boolean} Whether the location is within any delivery zone
 */
export const isWithinDeliveryZone = (location) => {
  if (!location || !location.latitude || !location.longitude) return false;

  // Check if within radius of any central point
  const withinRadius = isWithinDeliveryRadius(location.latitude, location.longitude);

  // Check if within any polygon (more precise boundary)
  let withinPolygon = false;
  for (const polygon of deliveryZonePolygons) {
    if (isPointInPolygon(location, polygon)) {
      withinPolygon = true;
      break;
    }
  }

  // Location is valid if it's within either the radius or any polygon
  return withinRadius || withinPolygon;
};

/**
 * Check if a pincode is valid for delivery
 * @param {string} pincode - Pincode to check
 * @returns {boolean} Whether the pincode is valid
 */
export const isValidPincode = (pincode) => {
  return validPincodes.includes(pincode);
};

/**
 * Get friendly message for invalid pincode
 * @param {string} pincode - Pincode to check
 * @returns {Object} Message object with title, message, and type
 */
export const getPincodeMessage = (pincode) => {
  if (!pincode || pincode.length !== 6) {
    return {
      title: "Invalid Pincode",
      message: "Please enter a valid 6-digit pincode.",
      type: "error",
      canBrowse: false
    };
  }

  if (isValidPincode(pincode)) {
    const areaInfo = pincodeAreas[pincode];
    const areaName = areaInfo ? areaInfo.mainArea : 'your area';
    const specificAreas = areaInfo ? areaInfo.areas.join(', ') : '';

    return {
      title: "Service Available ✅",
      message: `Great! We deliver to ${areaName}${specificAreas ? ` (${specificAreas})` : ''}. You can proceed with your order.`,
      type: "success",
      canBrowse: true,
      areaInfo: areaInfo
    };
  }

  // Check if it's a nearby area that might get service soon
  const nearbyAreas = [
    { prefix: '632', area: 'Vellore District' },
    { prefix: '635', area: 'Dharmapuri District' },
    { prefix: '636', area: 'Salem District' },
    { prefix: '637', area: 'Namakkal District' },
    { prefix: '638', area: 'Erode District' },
    { prefix: '641', area: 'Coimbatore District' },
    { prefix: '642', area: 'Coimbatore Rural' },
    { prefix: '643', area: 'Tirupur District' },
    { prefix: '60', area: 'Chennai Metro' }
  ];

  const nearbyArea = nearbyAreas.find(area => pincode.startsWith(area.prefix));

  if (nearbyArea) {
    // Special handling for other Vellore pincodes
    if (pincode.startsWith('632')) {
      return {
        title: "Expanding in Vellore! 🚀",
        message: `We're currently serving select areas in Vellore District and are rapidly expanding to cover more locations including yours. You can browse our products and we'll notify you as soon as delivery becomes available in your area.`,
        type: "coming_soon",
        canBrowse: true,
        area: 'Vellore District'
      };
    }

    return {
      title: "Coming Soon to Your Area! 🚀",
      message: `We're excited to announce that we'll be expanding our services to ${nearbyArea.area} very soon! You can still browse our products and we'll notify you once delivery is available in your area.`,
      type: "coming_soon",
      canBrowse: true,
      area: nearbyArea.area
    };
  }

  return {
    title: "Service Not Available Yet 📍",
    message: "We're not currently delivering to your area, but we're constantly expanding! You can still browse our products and we'll notify you when we start delivering to your location.",
    type: "not_available",
    canBrowse: true
  };
};

/**
 * Get area information for a specific pincode
 * @param {string} pincode - Pincode to get area info for
 * @returns {Object|null} Area information object or null if not found
 */
export const getPincodeAreaInfo = (pincode) => {
  return pincodeAreas[pincode] || null;
};

/**
 * Get all supported areas as a formatted string
 * @returns {string} Formatted string of all supported areas
 */
export const getSupportedAreasText = () => {
  const areas = Object.values(pincodeAreas).map(area =>
    `${area.mainArea} (${area.areas.join(', ')})`
  );
  return areas.join('\n• ');
};

/**
 * Check if a pincode is in Vellore district
 * @param {string} pincode - Pincode to check
 * @returns {boolean} Whether the pincode is in Vellore district
 */
export const isVellorePincode = (pincode) => {
  return pincode && pincode.startsWith('632');
};

/**
 * Open maps application with directions to an address
 * @param {string} address - Destination address
 * @param {Object} coordinates - Optional coordinates for more precise navigation
 * @param {boolean} preferCoordinates - Whether to prioritize coordinates over address (default: true)
 */
export const openMapsWithDirections = (address, coordinates = null, preferCoordinates = true) => {
  try {
    let url;
    let usingCoordinates = false;

    // Log the inputs for debugging
    console.log('openMapsWithDirections called with:', {
      address: address,
      coordinates: coordinates,
      preferCoordinates: preferCoordinates
    });

    // If we have coordinates and we prefer using them, use them for more precise navigation
    if (preferCoordinates && coordinates && coordinates.latitude && coordinates.longitude) {
      usingCoordinates = true;
      console.log('Using coordinates for navigation:', coordinates);

      // Create a label for the marker (use address if available)
      const label = address ? encodeURIComponent(address) : "Delivery Location";

      if (Platform.OS === 'ios') {
        // iOS format: maps:?ll=lat,lng&q=Label@lat,lng
        url = `maps:?ll=${coordinates.latitude},${coordinates.longitude}&q=${label}@${coordinates.latitude},${coordinates.longitude}`;
      } else {
        // Android format: geo:lat,lng?q=lat,lng(Label)
        url = `geo:${coordinates.latitude},${coordinates.longitude}?q=${coordinates.latitude},${coordinates.longitude}(${label})`;
      }
    } else {
      // Otherwise use the address string
      console.log('Using address for navigation:', address);
      const encodedAddress = encodeURIComponent(address || "Delivery Location");

      if (Platform.OS === 'ios') {
        url = `maps:?q=${encodedAddress}`;
      } else {
        url = `geo:0,0?q=${encodedAddress}`;
      }
    }

    console.log('Opening maps with URL:', url);

    // Check if the URL can be opened
    Linking.canOpenURL(url)
      .then(supported => {
        if (supported) {
          console.log('Native maps app supported, opening URL');
          return Linking.openURL(url);
        } else {
          // Fallback to Google Maps web URL
          console.log('Native maps app not supported, falling back to web URL');
          const fallbackUrl = usingCoordinates
            ? `https://www.google.com/maps/search/?api=1&query=${coordinates.latitude},${coordinates.longitude}`
            : `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address || "Delivery Location")}`;

          console.log('Opening fallback URL:', fallbackUrl);
          return Linking.openURL(fallbackUrl);
        }
      })
      .catch(err => {
        console.error('Error opening maps app:', err);
        Alert.alert(
          "Navigation Error",
          "Could not open maps application. Please try again.",
          [{ text: "OK", style: 'default' }],
          { cancelable: true }
        );
      });
  } catch (error) {
    console.error('Error in openMapsWithDirections:', error);
  }
};
