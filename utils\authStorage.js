import AsyncStorage from '@react-native-async-storage/async-storage';
import { AUTH_TOKEN_KEY, REFRESH_TOKEN_KEY, USER_DATA_KEY } from '../config/constants';

/**
 * Store authentication tokens and user data
 * @param {string} token - JWT access token
 * @param {string} refreshToken - JWT refresh token
 * @param {Object} userData - User data object
 */
export const storeAuthData = async (token, refreshToken, userData) => {
    try {
        await AsyncStorage.multiSet([
            [AUTH_TOKEN_KEY, token],
            [REFRESH_TOKEN_KEY, refreshToken],
            [USER_DATA_KEY, JSON.stringify(userData)]
        ]);
        return true;
    } catch (error) {
        console.error('Error storing auth data:', error);
        return false;
    }
};

/**
 * Get the stored authentication token
 * @returns {Promise<string|null>} The stored token or null if not found
 */
export const getAuthToken = async () => {
    try {
        return await AsyncStorage.getItem(AUTH_TOKEN_KEY);
    } catch (error) {
        console.error('Error getting auth token:', error);
        return null;
    }
};

/**
 * Get the stored refresh token
 * @returns {Promise<string|null>} The stored refresh token or null if not found
 */
export const getRefreshToken = async () => {
    try {
        return await AsyncStorage.getItem(REFRESH_TOKEN_KEY);
    } catch (error) {
        console.error('Error getting refresh token:', error);
        return null;
    }
};

/**
 * Get the stored user data
 * @returns {Promise<Object|null>} The stored user data or null if not found
 */
export const getUserData = async () => {
    try {
        const userData = await AsyncStorage.getItem(USER_DATA_KEY);
        return userData ? JSON.parse(userData) : null;
    } catch (error) {
        console.error('Error getting user data:', error);
        return null;
    }
};

/**
 * Clear all authentication data
 * @returns {Promise<boolean>} True if successful, false otherwise
 */
export const clearAuthData = async () => {
    try {
        await AsyncStorage.multiRemove([AUTH_TOKEN_KEY, REFRESH_TOKEN_KEY, USER_DATA_KEY]);
        return true;
    } catch (error) {
        console.error('Error clearing auth data:', error);
        return false;
    }
};

/**
 * Check if the user is authenticated
 * @returns {Promise<boolean>} True if authenticated, false otherwise
 */
export const isAuthenticated = async () => {
    try {
        const token = await getAuthToken();
        return !!token;
    } catch (error) {
        console.error('Error checking authentication:', error);
        return false;
    }
};
