import axios from 'axios';
import { API_URL } from '../../config/constants';
import { getAuthToken, getUserData } from '../authStorage';

// Enhanced helper function to handle API requests with retries and validation
const apiRequestWithRetry = async (requestFn, maxRetries = 2, validateFn = null) => {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            if (attempt > 0) {
                console.log(`API retry attempt ${attempt}/${maxRetries}`);
            }

            const response = await requestFn();

            // Validate response if validation function is provided
            if (validateFn) {
                const validationResult = validateFn(response);
                if (!validationResult.isValid) {
                    throw new Error(`Response validation failed: ${validationResult.error}`);
                }
            }

            return response;
        } catch (error) {
            lastError = error;

            // Don't retry if it's a 401 (unauthorized) or 403 (forbidden)
            if (error.response && (error.response.status === 401 || error.response.status === 403)) {
                console.error('Authentication error, not retrying:', error.response.status);
                throw error;
            }

            // Don't retry if it's a 400 (bad request) or 404 (not found)
            if (error.response && (error.response.status === 400 || error.response.status === 404)) {
                console.error('Request error, not retrying:', error.response.status);
                throw error;
            }

            // If we've reached max retries, throw the last error
            if (attempt === maxRetries) {
                console.error('Max retries reached, giving up');
                throw lastError;
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
            console.log(`Waiting ${delay}ms before retry`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
};

// Validation functions
const validateOrdersResponse = (response) => {
    if (!response) {
        return { isValid: false, error: 'Empty response from server' };
    }

    // Handle different response formats
    let orders;

    if (Array.isArray(response)) {
        // Direct array of orders
        orders = response;
    } else if (response.orders && Array.isArray(response.orders)) {
        // Object with orders property
        orders = response.orders;
    } else if (response.data && Array.isArray(response.data.orders)) {
        // Object with data.orders property
        orders = response.data.orders;
    } else if (typeof response === 'object' && response !== null) {
        // Single order object
        if (response._id) {
            orders = [response];
        } else {
            return { isValid: false, error: 'Invalid order object structure' };
        }
    } else {
        return { isValid: false, error: 'Invalid orders data format' };
    }

    // Validate each order object
    for (const order of orders) {
        if (!order._id) {
            console.warn('Order missing _id:', order);
            return { isValid: false, error: 'Invalid order object structure - missing _id' };
        }
    }

    return { isValid: true, data: orders };
};

// Validation for profile response
const validateProfileResponse = (response) => {
    if (!response) {
        return { isValid: false, error: 'Empty response from server' };
    }

    // Check if response has required fields
    if (!response._id && !response.id) {
        return { isValid: false, error: 'Invalid profile data: missing ID' };
    }

    return { isValid: true, data: response };
};

// Get all orders for the delivery partner
export const getDeliveryPartnerOrders = async () => {
    try {
        const token = await getAuthToken();

        if (!token) {
            console.error('No auth token available');
            throw new Error('Authentication required');
        }

        // Get user data to log user ID for debugging
        let userData = null;
        try {
            userData = await getUserData();
            console.log('Fetching orders for delivery partner ID:', userData?._id || userData?.id || 'Unknown');
        } catch (userDataError) {
            console.error('Error getting user data:', userDataError);
        }

        return await apiRequestWithRetry(
            async () => {
                console.log('Fetching delivery partner orders from API');

                try {
                    // First try the delivery partner specific endpoint
                    console.log(`Calling API endpoint: ${API_URL}/delivery/orders`);
                    const response = await axios.get(`${API_URL}/delivery/orders`, {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Accept': 'application/json'
                        },
                        timeout: 10000 // 10 second timeout
                    });

                    // Log the full response for debugging
                    console.log('Full response from delivery API:', JSON.stringify(response.data, null, 2));
                    console.log(`Received response from delivery API:`,
                        Array.isArray(response.data)
                            ? `${response.data.length} orders`
                            : typeof response.data);

                    // Validate the response format
                    if (!response.data) {
                        console.error('Empty response data from delivery API');
                        throw new Error('Empty response from server');
                    }

                    return response.data;
                } catch (deliveryError) {
                    console.error('Error fetching from delivery endpoint:', deliveryError.message);
                    console.error('Error details:', deliveryError.response?.status, deliveryError.response?.data);

                    // If that fails, try the general orders endpoint
                    console.log('Trying general orders endpoint as fallback');
                    console.log(`Calling API endpoint: ${API_URL}/orders`);
                    const fallbackResponse = await axios.get(`${API_URL}/orders`, {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Accept': 'application/json'
                        },
                        params: {
                            role: 'delivery_partner'
                        },
                        timeout: 10000 // 10 second timeout
                    });

                    // Log the full response for debugging
                    console.log('Full response from general API:', JSON.stringify(fallbackResponse.data, null, 2));
                    console.log('Received response from general API:',
                        Array.isArray(fallbackResponse.data)
                            ? `${fallbackResponse.data.length} orders`
                            : typeof fallbackResponse.data);

                    // Validate the response format
                    if (!fallbackResponse.data) {
                        console.error('Empty response data from general API');
                        throw new Error('Empty response from general API');
                    }

                    return fallbackResponse.data;
                }
            },
            2,
            validateOrdersResponse
        );
    } catch (error) {
        console.error('Error fetching delivery partner orders:', error);

        // Throw the error instead of returning an empty array
        // This will allow the component to handle the error properly
        throw error;
    }
};

// Update order status or assign order to delivery partner
export const updateOrderStatus = async (orderId, status, deliveryNote = '', assignToMe = false) => {
    try {
        const token = await getAuthToken();

        if (!token) {
            console.error('No auth token available');
            throw new Error('Authentication required');
        }

        if (!orderId) {
            console.error('No order ID provided');
            throw new Error('Order ID is required');
        }

        // For order assignment, we don't want to retry if successful
        // This prevents the 400 error when trying to assign an already assigned order
        if (assignToMe) {
            try {
                console.log(`Requesting assignment of order ${orderId} to current delivery partner`);

                // Log the request payload for debugging
                const requestPayload = { assignToMe: true };
                console.log('Request payload:', JSON.stringify(requestPayload, null, 2));

                const response = await axios.put(
                    `${API_URL}/delivery/orders/${orderId}/status`,
                    requestPayload,
                    {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        timeout: 10000 // 10 second timeout
                    }
                );
                console.log(`Order ${orderId} assigned successfully`);
                return response.data;
            } catch (error) {
                // Don't retry if it's a 400 (bad request) - likely already assigned
                if (error.response && error.response.status === 400) {
                    console.error('Order assignment failed, likely already assigned:', error.response.status);
                    throw error;
                }

                // For other errors, we can still retry
                console.error(`Error assigning order ${orderId}:`, error);
                throw error;
            }
        } else {
            // For regular status updates, use the retry mechanism
            return await apiRequestWithRetry(
                async () => {
                    console.log(`Updating order ${orderId} status to ${status}`);
                    const response = await axios.put(
                        `${API_URL}/delivery/orders/${orderId}/status`,
                        { status, deliveryNote },
                        {
                            headers: {
                                Authorization: `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            },
                            timeout: 10000 // 10 second timeout
                        }
                    );
                    console.log(`Order ${orderId} status updated successfully`);
                    return response.data;
                },
                2,
                validateOrdersResponse
            );
        }
    } catch (error) {
        console.error(`Error updating order ${orderId} status:`, error);
        throw error;
    }
};

// Validation for availability response
const validateAvailabilityResponse = (response) => {
    if (!response) {
        return { isValid: false, error: 'Empty response from server' };
    }

    // Check if response has isAvailable field
    if (typeof response.isAvailable !== 'boolean' &&
        (!response.deliveryPartner || typeof response.deliveryPartner.isAvailable !== 'boolean')) {
        console.warn('Invalid availability response:', response);
        return { isValid: false, error: 'Invalid availability data: missing isAvailable field' };
    }

    return { isValid: true, data: response };
};

// Update delivery partner availability
export const updateAvailability = async (isAvailable) => {
    try {
        const token = await getAuthToken();

        if (!token) {
            console.error('No auth token available');
            throw new Error('Authentication required');
        }

        if (typeof isAvailable !== 'boolean') {
            console.error('Invalid availability value:', isAvailable);
            throw new Error('Availability must be a boolean value');
        }

        // Get user data for logging
        let userData = null;
        try {
            userData = await getUserData();
            console.log('Updating availability for delivery partner ID:', userData?._id || userData?.id || 'Unknown');
            console.log('Current availability in user data:', userData?.isAvailable);
            console.log('Setting availability to:', isAvailable);
        } catch (userDataError) {
            console.error('Error getting user data:', userDataError);
        }

        return await apiRequestWithRetry(
            async () => {
                console.log(`Updating availability status to ${isAvailable}`);
                const response = await axios.put(
                    `${API_URL}/delivery/availability`,
                    { isAvailable },
                    {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        timeout: 10000 // 10 second timeout
                    }
                );

                // Log the full response for debugging
                console.log('Full availability update response:', JSON.stringify(response.data, null, 2));

                // If the response doesn't include isAvailable field, add it
                if (response.data && typeof response.data.isAvailable === 'undefined') {
                    console.log('Adding isAvailable field to response');
                    response.data.isAvailable = isAvailable;
                }

                console.log('Availability status updated successfully to:', isAvailable);
                return response.data;
            },
            2,
            validateAvailabilityResponse
        );
    } catch (error) {
        console.error('Error updating availability status:', error);

        // Log detailed error information
        if (error.response) {
            console.error('Error response:', {
                status: error.response.status,
                data: error.response.data
            });
        }

        throw error;
    }
};

// Get delivery partner profile
export const getDeliveryPartnerProfile = async () => {
    try {
        const token = await getAuthToken();

        if (!token) {
            console.error('No auth token available');
            throw new Error('Authentication required');
        }

        return await apiRequestWithRetry(
            async () => {
                console.log('Fetching delivery partner profile from API');
                const response = await axios.get(`${API_URL}/delivery/profile`, {
                    headers: {
                        Authorization: `Bearer ${token}`
                    },
                    timeout: 10000 // 10 second timeout
                });
                console.log('Delivery partner profile fetched successfully');
                return response.data;
            },
            2,
            validateProfileResponse
        );
    } catch (error) {
        console.error('Error fetching delivery partner profile:', error);
        throw error;
    }
};

// Get unassigned orders that could be assigned to a delivery partner
export const getUnassignedOrders = async () => {
    try {
        const token = await getAuthToken();

        if (!token) {
            console.error('No auth token available');
            throw new Error('Authentication required');
        }

        return await apiRequestWithRetry(
            async () => {
                console.log('Fetching unassigned orders from API');
                const response = await axios.get(`${API_URL}/delivery/unassigned-orders`, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Accept': 'application/json'
                    },
                    timeout: 10000 // 10 second timeout
                });
                console.log(`Received ${response.data.length || 0} unassigned orders from API`);
                return response.data;
            },
            2,
            validateOrdersResponse
        );
    } catch (error) {
        console.error('Error fetching unassigned orders:', error);
        // Return empty array instead of throwing to prevent app crashes
        return [];
    }
};

// Assign an order to the current delivery partner
export const assignOrderToSelf = async (orderId) => {
    try {
        const token = await getAuthToken();

        if (!token) {
            console.error('No auth token available');
            throw new Error('Authentication required');
        }

        return await apiRequestWithRetry(
            async () => {
                console.log(`Assigning order ${orderId} to self`);
                const response = await axios.put(
                    `${API_URL}/delivery/orders/${orderId}/assign`,
                    {},
                    {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        timeout: 10000 // 10 second timeout
                    }
                );
                console.log(`Order ${orderId} assigned successfully`);
                return response.data;
            },
            2,
            validateOrdersResponse
        );
    } catch (error) {
        console.error(`Error assigning order ${orderId}:`, error);
        throw error;
    }
};
