/**
 * Utility functions for coin calculations and logic
 */

/**
 * Calculate dynamic coin limits based on order value
 * @param {number} subtotal - Order subtotal amount
 * @param {number} totalCoins - User's total available coins
 * @param {number} couponDiscount - Applied coupon discount
 * @param {number} deliveryFee - Delivery fee (usually 0 or 49)
 * @returns {object} Coin calculation result
 */
export const calculateDynamicCoinLimits = (subtotal, totalCoins, couponDiscount = 0, deliveryFee = 0) => {
    const totalBeforeCoins = subtotal + deliveryFee - couponDiscount;
    
    // 1. Ensure minimum profit margin (at least 20% of subtotal should remain)
    const minimumProfitAmount = Math.max(20, subtotal * 0.2);
    const maxDiscountFromCoins = Math.max(0, totalBeforeCoins - minimumProfitAmount);
    
    // 2. Apply dynamic coin limits based on order value
    let maxCoinsAllowed = 100; // Default maximum
    let limitReason = 'default';
    
    if (subtotal >= 500) {
        maxCoinsAllowed = Math.min(100, Math.floor(subtotal * 0.5)); // Up to 50% of subtotal for large orders
        limitReason = 'large_order';
    } else if (subtotal >= 200) {
        maxCoinsAllowed = Math.min(50, Math.floor(subtotal * 0.4)); // Up to 40% for medium orders
        limitReason = 'medium_order';
    } else {
        maxCoinsAllowed = Math.min(30, Math.floor(subtotal * 0.3)); // Up to 30% for small orders
        limitReason = 'small_order';
    }

    // 3. Calculate final applicable coins
    const maxApplicableCoins = Math.min(
        totalCoins,
        maxDiscountFromCoins,
        maxCoinsAllowed
    );

    // 4. Determine limiting factor
    let limitingFactor = 'none';
    if (maxApplicableCoins === totalCoins && totalCoins < maxCoinsAllowed && totalCoins < maxDiscountFromCoins) {
        limitingFactor = 'available_coins';
    } else if (maxApplicableCoins === maxCoinsAllowed) {
        limitingFactor = 'order_value_limit';
    } else if (maxApplicableCoins === maxDiscountFromCoins) {
        limitingFactor = 'profit_margin';
    }

    return {
        maxApplicableCoins,
        maxCoinsAllowed,
        maxDiscountFromCoins,
        minimumProfitAmount,
        limitingFactor,
        limitReason,
        canApplyCoins: maxApplicableCoins > 0,
        profitAfterCoins: totalBeforeCoins - maxApplicableCoins
    };
};

/**
 * Get user-friendly message for coin application
 * @param {object} coinLimits - Result from calculateDynamicCoinLimits
 * @param {number} requestedCoins - Coins user wanted to apply
 * @returns {string} User-friendly message
 */
export const getCoinApplicationMessage = (coinLimits, requestedCoins) => {
    const { maxApplicableCoins } = coinLimits;

    if (!coinLimits.canApplyCoins) {
        return 'Cart total is too low to apply coins or would result in insufficient profit margin.';
    }

    // Simple message showing only the applied amount
    return `${maxApplicableCoins} coins applied as discount`;
};

/**
 * Calculate coins earned from an order
 * @param {number} finalAmount - Final amount paid after all discounts
 * @returns {number} Coins earned (10 coins per ₹100)
 */
export const calculateCoinsEarned = (finalAmount) => {
    return finalAmount > 0 ? Math.floor(finalAmount / 100) * 10 : 0;
};

/**
 * Validate if coins can be used for an order
 * @param {number} subtotal - Order subtotal
 * @param {number} availableCoins - User's available coins
 * @param {boolean} hasCoupon - Whether user has applied a coupon
 * @returns {object} Validation result
 */
export const validateCoinUsage = (subtotal, availableCoins, hasCoupon = false) => {
    if (hasCoupon) {
        return {
            canUse: false,
            reason: 'Cannot use both coins and coupons together'
        };
    }
    
    if (subtotal < 100) {
        return {
            canUse: false,
            reason: 'Minimum order amount of ₹100 required to use coins'
        };
    }
    
    if (availableCoins <= 0) {
        return {
            canUse: false,
            reason: 'No available coins to use'
        };
    }
    
    return {
        canUse: true,
        reason: 'Coins can be applied'
    };
};
