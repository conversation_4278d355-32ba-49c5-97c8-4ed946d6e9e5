import AsyncStorage from '@react-native-async-storage/async-storage';

const PERMISSION_STORAGE_KEY = 'app_permissions_status';

/**
 * Save permission status to local storage
 * @param {Object} permissions - Object containing permission statuses
 */
export const savePermissionStatus = async (permissions) => {
    try {
        const permissionData = {
            ...permissions,
            timestamp: Date.now(),
            version: '1.0'
        };

        await AsyncStorage.setItem(PERMISSION_STORAGE_KEY, JSON.stringify(permissionData));
        console.log('Permission status saved:', permissionData);
    } catch (error) {
        console.error('Error saving permission status:', error);
    }
};

/**
 * Get saved permission status from local storage
 * @returns {Promise<Object|null>} Saved permission data or null
 */
export const getPermissionStatus = async () => {
    try {
        const permissionData = await AsyncStorage.getItem(PERMISSION_STORAGE_KEY);

        if (permissionData) {
            const parsed = JSON.parse(permissionData);
            console.log('Retrieved permission status:', parsed);
            return parsed;
        }

        return null;
    } catch (error) {
        console.error('Error retrieving permission status:', error);
        return null;
    }
};

/**
 * Check if permissions were requested recently (within 24 hours)
 * @returns {Promise<boolean>} Whether permissions were requested recently
 */
export const werePermissionsRequestedRecently = async () => {
    try {
        const permissionData = await getPermissionStatus();

        if (!permissionData || !permissionData.timestamp) {
            return false;
        }

        const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        const timeDiff = Date.now() - permissionData.timestamp;

        return timeDiff < twentyFourHours;
    } catch (error) {
        console.error('Error checking permission timestamp:', error);
        return false;
    }
};

/**
 * Clear saved permission status (for testing/reset purposes)
 */
export const clearPermissionStatus = async () => {
    try {
        await AsyncStorage.removeItem(PERMISSION_STORAGE_KEY);
        console.log('Permission status cleared - will show permissions on next launch');
    } catch (error) {
        console.error('Error clearing permission status:', error);
    }
};

/**
 * Force reset permissions (for testing - removes all permission data)
 */
export const resetAllPermissions = async () => {
    try {
        await AsyncStorage.removeItem(PERMISSION_STORAGE_KEY);
        await AsyncStorage.removeItem('otp_autofill_permission');
        console.log('All permissions reset - app will behave as first launch');
    } catch (error) {
        console.error('Error resetting permissions:', error);
    }
};

/**
 * Check if permissions have already been handled and shouldn't be asked again
 * @returns {Promise<boolean>} Whether to skip permission requests
 */
export const shouldSkipPermissionRequest = async () => {
    try {
        const permissionData = await getPermissionStatus();

        if (!permissionData) {
            // No permission data exists, show permissions for first time
            return false;
        }

        // If permissions have been handled (granted OR denied), skip asking again
        const hasNotificationStatus = permissionData.notification === 'granted' ||
                                     permissionData.notification === 'denied';

        const hasLocationStatus = permissionData.location === 'granted' ||
                                 permissionData.location === 'denied';

        // Skip if both permissions have been handled (regardless of granted/denied)
        const bothHandled = hasNotificationStatus && hasLocationStatus;

        console.log('Permission check:', {
            hasNotificationStatus,
            hasLocationStatus,
            bothHandled,
            notificationStatus: permissionData.notification,
            locationStatus: permissionData.location
        });

        return bothHandled;
    } catch (error) {
        console.error('Error checking if should skip permission request:', error);
        return false;
    }
};

/**
 * Check if this is the first time the app is being launched
 * @returns {Promise<boolean>} Whether this is the first launch
 */
export const isFirstAppLaunch = async () => {
    try {
        const permissionData = await getPermissionStatus();
        return permissionData === null;
    } catch (error) {
        console.error('Error checking first app launch:', error);
        return true; // Default to first launch if error
    }
};

/**
 * Save autofill permission preference
 * @param {string} status - 'granted' or 'denied'
 */
export const saveAutofillPermission = async (status) => {
    try {
        const AUTOFILL_KEY = 'otp_autofill_permission';
        const data = {
            status,
            timestamp: Date.now()
        };

        await AsyncStorage.setItem(AUTOFILL_KEY, JSON.stringify(data));
        console.log('Autofill permission saved:', data);
    } catch (error) {
        console.error('Error saving autofill permission:', error);
    }
};

/**
 * Get saved autofill permission preference
 * @returns {Promise<string|null>} 'granted', 'denied', or null
 */
export const getAutofillPermission = async () => {
    try {
        const AUTOFILL_KEY = 'otp_autofill_permission';
        const data = await AsyncStorage.getItem(AUTOFILL_KEY);

        if (data) {
            const parsed = JSON.parse(data);
            return parsed.status;
        }

        return null;
    } catch (error) {
        console.error('Error getting autofill permission:', error);
        return null;
    }
};
