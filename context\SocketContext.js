import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import io from 'socket.io-client';
import { SOCKET_URL } from '../config/constants';
import { getAuthToken, getUserData } from '../utils/authStorage';
import { useAuth } from './AuthContext';

const SocketContext = createContext();

export const useSocket = () => {
    const context = useContext(SocketContext);
    if (!context) {
        throw new Error('useSocket must be used within a SocketProvider');
    }
    return context;
};

export const SocketProvider = ({ children }) => {
    const [socket, setSocket] = useState(null);
    const [connected, setConnected] = useState(false);
    const [connectionError, setConnectionError] = useState(null);
    const { user, isAuthenticated } = useAuth();
    const reconnectAttempts = useRef(0);
    const maxReconnectAttempts = 5;

    // Initialize socket connection
    const initializeSocket = async () => {
        try {
            if (socket) {
                socket.disconnect();
            }

            console.log('Initializing Socket.IO connection...');

            // Get authentication data
            const token = await getAuthToken();
            const userData = await getUserData();

            // Socket connection options
            const socketOptions = {
                transports: ['websocket', 'polling'],
                timeout: 20000,
                forceNew: true,
                reconnection: true,
                reconnectionAttempts: maxReconnectAttempts,
                reconnectionDelay: 1000,
                reconnectionDelayMax: 5000,
                query: {}
            };

            // Add authentication data to query if available
            if (token && userData) {
                socketOptions.query = {
                    token: token,
                    userId: userData._id || userData.id,
                    userType: userData.userType || 'USER'
                };
            } else if (userData) {
                socketOptions.query = {
                    userId: userData._id || userData.id,
                    userType: userData.userType || 'USER'
                };
            }

            console.log('Socket connection options:', socketOptions);

            // Create socket connection
            console.log('🔌 Connecting to Socket.io server:', SOCKET_URL);
            const newSocket = io(SOCKET_URL, socketOptions);

            // Connection event handlers
            newSocket.on('connect', () => {
                console.log('Socket connected:', newSocket.id);
                setConnected(true);
                setConnectionError(null);
                reconnectAttempts.current = 0;

                // Join room if user data is available
                if (userData) {
                    newSocket.emit('join', {
                        userId: userData._id || userData.id,
                        userType: userData.userType || 'USER'
                    });
                }
            });

            newSocket.on('disconnect', (reason) => {
                console.log('Socket disconnected:', reason);
                setConnected(false);

                if (reason === 'io server disconnect') {
                    // Server disconnected, need to reconnect manually
                    setTimeout(() => {
                        if (reconnectAttempts.current < maxReconnectAttempts) {
                            reconnectAttempts.current++;
                            newSocket.connect();
                        }
                    }, 1000);
                }
            });

            newSocket.on('connect_error', (error) => {
                console.error('Socket connection error:', error);
                setConnectionError(error.message);
                setConnected(false);
            });

            newSocket.on('join_success', (data) => {
                console.log('Successfully joined room:', data);
            });

            newSocket.on('join_error', (error) => {
                console.error('Failed to join room:', error);
            });

            setSocket(newSocket);

        } catch (error) {
            console.error('Error initializing socket:', error);
            setConnectionError(error.message);
        }
    };

    // Initialize socket when user is authenticated
    useEffect(() => {
        if (isAuthenticated && user) {
            initializeSocket();
        } else if (socket) {
            // Disconnect socket when user logs out
            socket.disconnect();
            setSocket(null);
            setConnected(false);
        }

        // Cleanup on unmount
        return () => {
            if (socket) {
                socket.disconnect();
            }
        };
    }, [isAuthenticated, user]);

    // Socket event emitters
    const emitNewOrder = (orderData, callback) => {
        if (socket && connected) {
            socket.emit('new_order', orderData, callback);
        } else {
            console.warn('Socket not connected, cannot emit new_order');
            if (callback) callback({ success: false, error: 'Socket not connected' });
        }
    };

    const emitOrderStatusUpdate = (data, callback) => {
        if (socket && connected) {
            socket.emit('update_order_status', data, callback);
        } else {
            console.warn('Socket not connected, cannot emit update_order_status');
            if (callback) callback({ success: false, error: 'Socket not connected' });
        }
    };

    const emitDeliveryUpdate = (data, callback) => {
        if (socket && connected) {
            socket.emit('delivery_update', data, callback);
        } else {
            console.warn('Socket not connected, cannot emit delivery_update');
            if (callback) callback({ success: false, error: 'Socket not connected' });
        }
    };

    // Socket event listeners
    const onOrderUpdate = (callback) => {
        if (socket) {
            // Listen for both new orders and general order updates
            socket.on('new_order', callback);
            socket.on('order_update', callback);
            return () => {
                socket.off('new_order', callback);
                socket.off('order_update', callback);
            };
        }
        return () => {};
    };

    const onOrderStatusUpdate = (callback) => {
        if (socket) {
            // Listen for both event names used by backend
            socket.on('order_status_update', callback);
            socket.on('order_status_changed', callback);
            return () => {
                socket.off('order_status_update', callback);
                socket.off('order_status_changed', callback);
            };
        }
        return () => {};
    };

    const onDeliveryUpdate = (callback) => {
        if (socket) {
            // Listen for delivery updates and assignment notifications
            socket.on('delivery_update', callback);
            socket.on('new_delivery_assignment', callback);
            return () => {
                socket.off('delivery_update', callback);
                socket.off('new_delivery_assignment', callback);
            };
        }
        return () => {};
    };

    const onNotification = (callback) => {
        if (socket) {
            socket.on('notification', callback);
            return () => socket.off('notification', callback);
        }
        return () => {};
    };

    // Manual reconnection
    const reconnect = () => {
        if (socket && !connected) {
            socket.connect();
        } else {
            initializeSocket();
        }
    };

    // Ping to check connection health
    const ping = (callback) => {
        if (socket && connected) {
            socket.emit('ping', { timestamp: new Date().toISOString() }, callback);
        } else {
            if (callback) callback({ success: false, error: 'Socket not connected' });
        }
    };

    const value = {
        socket,
        connected,
        connectionError,
        // Event emitters
        emitNewOrder,
        emitOrderStatusUpdate,
        emitDeliveryUpdate,
        // Event listeners
        onOrderUpdate,
        onOrderStatusUpdate,
        onDeliveryUpdate,
        onNotification,
        // Utility functions
        reconnect,
        ping
    };

    return (
        <SocketContext.Provider value={value}>
            {children}
        </SocketContext.Provider>
    );
};
