import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Dimensions, Animated, RefreshControl, ActivityIndicator } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { PieChart } from "react-native-chart-kit";
import { useNavigation } from '@react-navigation/native';
import { useAdmin } from '../../context/AdminContext';
import { getAllOrders } from '../../utils/api/orderApi';
import { getAllProducts } from '../../utils/api/productApi';

// This section has been removed

const DashboardScreen = () => {
    const navigation = useNavigation();
    const { analytics } = useAdmin();
    const screenWidth = Dimensions.get('window').width;
    const [refreshing, setRefreshing] = useState(false);
    const [initialLoading, setInitialLoading] = useState(true);
    const [realOutOfStock, setRealOutOfStock] = useState(0);
    const [stockLoading, setStockLoading] = useState(true);
    const [recentOrders, setRecentOrders] = useState([]);
    const [ordersLoading, setOrdersLoading] = useState(true);
    const [orderStatusData, setOrderStatusData] = useState([]);
    const [statusLoading, setStatusLoading] = useState(true);

    // Animation values
    const [fadeAnim] = useState(new Animated.Value(0));
    const [slideAnim] = useState(new Animated.Value(50));

    // Function to fetch real-time out-of-stock count
    const fetchOutOfStockCount = useCallback(async () => {
        try {
            setStockLoading(true);
            console.log('Fetching out-of-stock count from database...');

            const response = await getAllProducts();
            console.log('Products API response:', response);

            // Handle different response formats
            let productsData = [];
            if (response && response.products && Array.isArray(response.products)) {
                // If response has products property
                productsData = response.products;
                console.log('Found products in response.products array');
            } else if (response && Array.isArray(response)) {
                // If response is an array directly
                productsData = response;
                console.log('Found products in direct response array');
            } else {
                console.log('No valid products data found in response');
                setRealOutOfStock(0);
                setStockLoading(false);
                return;
            }

            // Log a sample product to see its structure
            if (productsData.length > 0) {
                console.log('Sample product structure:', JSON.stringify(productsData[0], null, 2));
            }

            // Count products that are not available (using isAvailable property)
            const outOfStockCount = productsData.filter(product => {
                // Check if product exists
                if (!product) return false;

                // Check if isAvailable is explicitly false
                if (product.isAvailable === false) return true;

                // Check if available is explicitly false (fallback)
                if (product.available === false) return true;

                // Otherwise, product is available
                return false;
            }).length;

            console.log(`Found ${outOfStockCount} products with isAvailable=false`);

            // Use the real-time data from the database
            console.log(`Setting out-of-stock count to ${outOfStockCount} from real-time data`);
            setRealOutOfStock(outOfStockCount);
        } catch (error) {
            console.error('Error fetching out-of-stock count:', error);
            // Default to 0 if there's an error
            setRealOutOfStock(0);
        } finally {
            setStockLoading(false);

            // Check if all data is loaded
            if (!ordersLoading && !statusLoading) {
                setInitialLoading(false);
            }
        }
    }, []);

    // Function to calculate order status data for the pie chart
    const calculateOrderStatusData = useCallback((orders) => {
        if (!orders || !Array.isArray(orders) || orders.length === 0) {
            return [
                {
                    name: "No Data",
                    population: 1,
                    color: "#CCCCCC",
                    legendFontColor: "#7F7F7F",
                    legendFontSize: 12
                }
            ];
        }

        // Count orders by status
        const statusCounts = {
            pending: 0,
            'in-transit': 0,
            delivered: 0,
            cancelled: 0
        };

        // Map backend status to frontend status and count
        orders.forEach(order => {
            let status = "pending";
            if (order.status === "OUT_FOR_DELIVERY") status = "in-transit";
            else if (order.status === "DELIVERED") status = "delivered";
            else if (order.status === "CANCELLED") status = "cancelled";
            else if (order.status === "PLACED" || order.status === "CONFIRMED" || order.status === "PREPARING") status = "pending";

            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });

        // Create pie chart data
        const chartData = [
            {
                name: "Pending",
                population: statusCounts.pending,
                color: "#FFBB28",
                legendFontColor: "#7F7F7F",
                legendFontSize: 12
            },
            {
                name: "Out for Delivery",
                population: statusCounts['in-transit'],
                color: "#0088FE",
                legendFontColor: "#7F7F7F",
                legendFontSize: 12
            },
            {
                name: "Delivered",
                population: statusCounts.delivered,
                color: "#00C49F",
                legendFontColor: "#7F7F7F",
                legendFontSize: 12
            },
            {
                name: "Cancelled",
                population: statusCounts.cancelled,
                color: "#FF8042",
                legendFontColor: "#7F7F7F",
                legendFontSize: 12
            }
        ];

        // Filter out zero values
        return chartData.filter(item => item.population > 0);
    }, []);

    // Function to fetch recent orders from database
    const fetchRecentOrders = useCallback(async () => {
        try {
            setOrdersLoading(true);
            setStatusLoading(true);
            console.log('Fetching orders from database...');

            const response = await getAllOrders();
            console.log('API response:', response);

            // Handle different response formats
            let ordersData = [];
            if (response && response.orders && Array.isArray(response.orders)) {
                // If response has orders property (from admin API)
                ordersData = response.orders;
                console.log('Found orders in response.orders array');
            } else if (response && Array.isArray(response)) {
                // If response is an array directly
                ordersData = response;
                console.log('Found orders in direct response array');
            } else {
                console.log('No valid orders data found in response');
                setRecentOrders([]);
                setOrdersLoading(false);
                setStatusLoading(false);
                return;
            }

            // Log the first order to see its structure
            if (ordersData.length > 0) {
                console.log('Sample order structure:', JSON.stringify(ordersData[0], null, 2));
            }

            // Calculate order status data for pie chart
            const statusData = calculateOrderStatusData(ordersData);
            setOrderStatusData(statusData);

            // Sort by createdAt date (newest first) and take the 4 most recent
            const sortedOrders = ordersData.sort((a, b) => {
                const dateA = new Date(b.createdAt || b.orderPlacedAt || 0);
                const dateB = new Date(a.createdAt || a.orderPlacedAt || 0);
                return dateA - dateB;
            }).slice(0, 4);

            // Map the orders to a consistent format
            const formattedOrders = sortedOrders.map(order => {
                // Map backend status to frontend status
                let status = "pending";
                if (order.status === "OUT_FOR_DELIVERY") status = "in-transit";
                else if (order.status === "DELIVERED") status = "delivered";
                else if (order.status === "CANCELLED") status = "cancelled";
                else if (order.status === "PLACED" || order.status === "CONFIRMED" || order.status === "PREPARING") status = "pending";

                // Extract items with proper error handling
                const items = Array.isArray(order.items) ? order.items : [];

                // Use orderNumber if available, otherwise create a formatted order number from ID
                let orderNum = order.orderNumber;
                if (!orderNum) {
                    // Extract a short identifier from the ID
                    const idStr = order.orderId || order._id || '';
                    // Take the last 4 characters if available, or use the whole string
                    const shortId = idStr.length > 4 ? idStr.slice(-4) : idStr;
                    orderNum = `Order #${shortId}`;
                } else {
                    // Format the order number
                    orderNum = `Order #${orderNum}`;
                }

                // Add delivery information for delivered orders
                const result = {
                    id: order.orderId || order._id || 'Unknown ID', // Keep ID for key purposes
                    orderNumber: orderNum,
                    items: items,
                    total: order.totalAmount || 0,
                    createdAt: new Date(order.createdAt || order.orderPlacedAt || Date.now()),
                    status: status
                };

                // Add delivery information for delivered orders
                if (status === 'delivered') {
                    result.deliveredAt = new Date(order.deliveredAt || order.deliveryDate || (order.createdAt ? new Date(new Date(order.createdAt).getTime() + 2 * 60 * 60 * 1000) : Date.now()));
                }

                return result;
            });

            setRecentOrders(formattedOrders);
            console.log(`Fetched ${formattedOrders.length} recent orders`);

            // Set loading states to false
            setOrdersLoading(false);
            setStatusLoading(false);

            // Check if all data is loaded
            if (!stockLoading) {
                setInitialLoading(false);
            }
        } catch (error) {
            console.error('Error fetching orders:', error);

            // Create some fallback orders for testing
            const fallbackOrders = [
                {
                    id: "ORD001",
                    orderNumber: "Order #1001",
                    items: [{ name: "Chicken Breast" }],
                    total: 450,
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
                    status: "pending"
                },
                {
                    id: "ORD002",
                    orderNumber: "Order #1002",
                    items: [{ name: "Mutton Curry Cut" }],
                    total: 650,
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 5),
                    status: "in-transit"
                },
                {
                    id: "ORD003",
                    orderNumber: "Order #1003",
                    items: [{ name: "Fish Fillet" }],
                    total: 350,
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24),
                    status: "delivered",
                    deliveredAt: new Date(Date.now() - 1000 * 60 * 60 * 22)
                },
                {
                    id: "ORD004",
                    orderNumber: "Order #1004",
                    items: [{ name: "Egg Tray" }],
                    total: 120,
                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 48),
                    status: "delivered",
                    deliveredAt: new Date(Date.now() - 1000 * 60 * 60 * 46)
                }
            ];

            setRecentOrders(fallbackOrders);

            // Create fallback status data
            const fallbackStatusData = [
                {
                    name: "Pending",
                    population: 1,
                    color: "#FFBB28",
                    legendFontColor: "#7F7F7F",
                    legendFontSize: 12
                },
                {
                    name: "Out for Delivery",
                    population: 1,
                    color: "#0088FE",
                    legendFontColor: "#7F7F7F",
                    legendFontSize: 12
                },
                {
                    name: "Delivered",
                    population: 2,
                    color: "#00C49F",
                    legendFontColor: "#7F7F7F",
                    legendFontSize: 12
                }
            ];

            setOrderStatusData(fallbackStatusData);

            // Set loading states to false
            setOrdersLoading(false);
            setStatusLoading(false);

            // Check if all data is loaded
            if (!stockLoading) {
                setInitialLoading(false);
            }
        }
    }, [calculateOrderStatusData]);

    // Refresh function - defined without useCallback to avoid dependency issues
    const onRefresh = async () => {
        setRefreshing(true);
        try {
            // Fetch both orders and out-of-stock count in parallel
            await Promise.all([
                fetchRecentOrders(),
                fetchOutOfStockCount()
            ]);
        } catch (error) {
            console.error('Error refreshing data:', error);
        } finally {
            setRefreshing(false);
        }
    };

    // Run animations and fetch data when component mounts
    useEffect(() => {
        // Start animations
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                useNativeDriver: true,
            })
        ]).start();

        // Fetch data only once when component mounts
        fetchRecentOrders();
        fetchOutOfStockCount();

        // Set a timeout to ensure initialLoading is set to false
        const timer = setTimeout(() => {
            setInitialLoading(false);
        }, 1000);

        // Cleanup function to prevent memory leaks
        return () => {
            clearTimeout(timer);
            setRecentOrders([]);
            setOrderStatusData([]);
            setInitialLoading(false);
            setRefreshing(false);
        };
    // Empty dependency array ensures this only runs once on mount
    }, []);

    // Ensure a value is a valid number or default to 0 - memoized for performance
    const safeNumber = useCallback((value) => {
        if (value === undefined || value === null || !isFinite(value) || isNaN(value)) {
            return 0;
        }
        return Number(value);
    }, []);

    // This section has been removed as we now use real-time data from the database

    // This section has been removed

    return (
        <View className="flex-1 bg-snow">
            {initialLoading ? (
                <View className="flex-1 justify-center items-center">
                    <ActivityIndicator size="large" color="#A31621" />
                    <Text className="text-gray-600 mt-4 font-medium">Loading dashboard...</Text>
                </View>
            ) : (
                <ScrollView
                    className="flex-1"
                    contentContainerStyle={{ paddingBottom: 100 }}
                    showsVerticalScrollIndicator={false}
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={onRefresh}
                            colors={["#A31621"]}
                            tintColor="#A31621"
                        />
                    }>

                <Animated.View
                    className="bg-madder px-6 pt-16 pb-8 rounded-b-3xl shadow-lg mb-4"
                    style={{
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }]
                    }}
                >
                    <View>
                        <Text className="text-3xl text-white font-bold">Dashboard</Text>
                        <Text className="text-white/80 mt-1 text-lg">Welcome back, Admin</Text>
                    </View>
                </Animated.View>

                <View className="px-4">
                <Animated.View
                    className="flex-row flex-wrap justify-between mb-6"
                    style={{
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }]
                    }}
                >
                    <TouchableOpacity
                        className="bg-white w-[48%] p-5 rounded-2xl shadow-md mb-4"
                        onPress={() => navigation.navigate('AdminOrdersScreen')}
                        style={{ elevation: 2 }}
                    >
                        <View className="bg-madder/10 w-14 h-14 rounded-full items-center justify-center mb-3">
                            <MaterialIcons name="receipt" size={28} color="#A31621" />
                        </View>
                        <Text className="text-3xl font-bold mt-2">{safeNumber(analytics.orders.total)}</Text>
                        <Text className="text-gray-600 text-base mt-1">Total Orders</Text>
                        <View className="flex-row items-center mt-1">
                            <MaterialIcons name="local-shipping" size={16} color="#3B82F6" />
                            <Text className="text-blue-600 text-base ml-1">{safeNumber(analytics.orders.inTransit)} out for delivery</Text>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="bg-white w-[48%] p-5 rounded-2xl shadow-md mb-4"
                        onPress={() => navigation.navigate('Products')}
                        style={{ elevation: 2 }}
                    >
                        <View className="bg-madder/10 w-14 h-14 rounded-full items-center justify-center mb-3">
                            <MaterialIcons name="inventory" size={28} color="#A31621" />
                        </View>
                        <Text className="text-3xl font-bold mt-2">{safeNumber(analytics.products.total)}</Text>
                        <Text className="text-gray-600 text-base mt-1">Total Products</Text>
                        <View className="flex-row items-center mt-1">
                            {stockLoading ? (
                                <View className="flex-row items-center">
                                    <View className="w-4 h-4 rounded-full bg-orange-100 animate-pulse mr-2" />
                                    <View className="h-4 w-24 bg-orange-100 rounded animate-pulse" />
                                </View>
                            ) : (
                                <>
                                    <MaterialIcons name="error-outline" size={16} color="#F97316" />
                                    <Text className="text-orange-600 text-base ml-1">{realOutOfStock} out of stock</Text>
                                </>
                            )}
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="bg-white w-[48%] p-5 rounded-2xl shadow-md"
                        onPress={() => navigation.navigate('Users')}
                        style={{ elevation: 2 }}
                    >
                        <View className="bg-madder/10 w-14 h-14 rounded-full items-center justify-center mb-3">
                            <MaterialIcons name="people" size={28} color="#A31621" />
                        </View>
                        <Text className="text-3xl font-bold mt-2">{safeNumber(analytics.users.total).toLocaleString()}</Text>
                        <Text className="text-gray-600 text-base mt-1">Total Users</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="bg-white w-[48%] p-5 rounded-2xl shadow-md"
                        onPress={() => navigation.navigate('DeliveryPartnerManagementScreen')}
                        style={{ elevation: 2 }}
                    >
                        <View className="bg-madder/10 w-14 h-14 rounded-full items-center justify-center mb-3">
                            <MaterialIcons name="delivery-dining" size={28} color="#A31621" />
                        </View>
                        <Text className="text-3xl font-bold mt-2">{safeNumber(analytics.deliveryPartners.total)}</Text>
                        <Text className="text-gray-600 text-base mt-1">Delivery Partners</Text>
                        <View className="flex-row items-center mt-1">
                            <MaterialIcons name="check-circle" size={16} color="#16A34A" />
                            <Text className="text-green-600 text-base ml-1">{safeNumber(analytics.deliveryPartners.available)} available</Text>
                        </View>
                    </TouchableOpacity>
                </Animated.View>

                {/* Order Status Chart */}
                <Animated.View
                    className="bg-white rounded-2xl p-6 mb-6 shadow-md"
                    style={{
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }],
                        elevation: 2
                    }}
                >
                    <Text className="text-xl font-bold text-gray-800 mb-5">Order Status</Text>

                    {statusLoading ? (
                        <View className="py-8 items-center">
                            <View className="w-16 h-16 rounded-full bg-madder/20 items-center justify-center">
                                <ActivityIndicator size="large" color="#A31621" />
                            </View>
                            <Text className="text-gray-500 mt-3 font-medium">Loading order status data...</Text>
                        </View>
                    ) : orderStatusData.length === 0 ? (
                        <View className="py-8 items-center">
                            <View className="w-16 h-16 rounded-full bg-madder/20 items-center justify-center">
                                <MaterialIcons name="pie-chart" size={32} color="#A31621" />
                            </View>
                            <Text className="text-gray-500 mt-3 font-medium">No order status data available</Text>
                        </View>
                    ) : (
                        <View>
                            <View className="items-center">
                                <PieChart
                                    data={orderStatusData}
                                    width={screenWidth - 60}
                                    height={200}
                                    chartConfig={{
                                        backgroundColor: "#fff",
                                        backgroundGradientFrom: "#fff",
                                        backgroundGradientTo: "#fff",
                                        color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`
                                    }}
                                    accessor={"population"}
                                    backgroundColor={"transparent"}
                                    paddingLeft={"15"}
                                    absolute
                                    hasLegend={true}
                                    legendPosition="right"
                                />
                            </View>

                            {/* Status Summary */}
                            <View className="flex-row flex-wrap justify-between mt-4 pt-4 border-t border-gray-100">
                                {orderStatusData.map((status, index) => (
                                    <View key={index} className="bg-white p-3 rounded-xl mb-2" style={{ width: '48%' }}>
                                        <View className="flex-row items-center">
                                            <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: status.color, marginRight: 8 }} />
                                            <Text className="text-gray-800 font-medium">{status.name}</Text>
                                        </View>
                                        <Text className="text-2xl font-bold mt-1 text-gray-800">{status.population}</Text>
                                        <Text className="text-gray-500 text-xs">
                                            {status.name === "Pending" ? "Awaiting processing" :
                                             status.name === "Out for Delivery" ? "Currently in transit" :
                                             status.name === "Delivered" ? "Successfully completed" :
                                             "Order cancelled"}
                                        </Text>
                                    </View>
                                ))}
                            </View>
                        </View>
                    )}
                </Animated.View>

                {/* Recent Orders */}
                <Animated.View
                    className="bg-white rounded-2xl p-6 shadow-md"
                    style={{
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }],
                        elevation: 2
                    }}
                >
                    <Text className="text-xl font-bold text-gray-800 mb-5">Recent Orders</Text>

                    {ordersLoading ? (
                        <View className="py-8 items-center">
                            <ActivityIndicator size="large" color="#A31621" />
                            <Text className="text-gray-500 mt-2">Loading orders...</Text>
                        </View>
                    ) : recentOrders.length === 0 ? (
                        <View className="py-8 items-center">
                            <MaterialIcons name="inbox" size={48} color="#A31621" />
                            <Text className="text-gray-500 mt-2">No orders found</Text>
                        </View>
                    ) : (
                        recentOrders.map(order => {
                            const statusColors = {
                                'pending': { bg: 'bg-yellow-100', text: 'text-yellow-700', icon: 'schedule' },
                                'in-transit': { bg: 'bg-blue-100', text: 'text-blue-700', icon: 'local-shipping' },
                                'delivered': { bg: 'bg-green-100', text: 'text-green-700', icon: 'check-circle' },
                                'cancelled': { bg: 'bg-red-100', text: 'text-red-700', icon: 'cancel' }
                            };

                            const statusColor = statusColors[order.status] || statusColors.pending;

                            // Get product name from items array with better error handling
                            let productName = 'Unknown Product';
                            if (order.items && Array.isArray(order.items) && order.items.length > 0) {
                                const firstItem = order.items[0];
                                if (typeof firstItem === 'object' && firstItem !== null) {
                                    productName = firstItem.name || firstItem.productName || 'Unknown Product';
                                    // If there are multiple items, indicate that
                                    if (order.items.length > 1) {
                                        productName += ` + ${order.items.length - 1} more`;
                                    }
                                }
                            }

                            const formattedDate = new Date(order.createdAt).toLocaleString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            });

                            return (
                                <TouchableOpacity
                                    key={order.id}
                                    className="flex-row justify-between items-center py-4 border-b border-gray-100"
                                    onPress={() => navigation.navigate('AdminOrdersScreen')}
                                >
                                    <View className="flex-1 pr-4">
                                        <View className="flex-row items-center">
                                            <Text className="font-bold text-base text-gray-800">{order.orderNumber}</Text>
                                            <View className={`${statusColor.bg} px-2 py-1 rounded-full ml-2`}>
                                                <Text className={`${statusColor.text} text-xs font-medium`}>
                                                    {order.status === 'pending' ? 'Pending' :
                                                        order.status === 'in-transit' ? 'Out for Delivery' :
                                                            order.status === 'delivered' ? 'Delivered' : 'Cancelled'}
                                                </Text>
                                            </View>
                                        </View>
                                        <Text className="text-madder font-medium text-base mt-1">₹{order.total.toFixed(0)}</Text>
                                        <Text className="text-gray-700 text-sm mt-1">{productName}</Text>
                                        <Text className="text-gray-500 text-xs mt-1">Ordered: {formattedDate}</Text>
                                        {order.status === 'delivered' && order.deliveredAt && (
                                            <Text className="text-green-600 text-xs mt-0.5">
                                                Delivered: {new Date(order.deliveredAt).toLocaleString('en-US', {
                                                    month: 'short',
                                                    day: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit'
                                                })}
                                            </Text>
                                        )}
                                    </View>
                                    <MaterialIcons name={statusColor.icon} size={24} color={
                                        order.status === 'pending' ? "#F59E0B" :
                                        order.status === 'in-transit' ? "#3B82F6" :
                                        order.status === 'delivered' ? "#10B981" : "#EF4444"
                                    } />
                                </TouchableOpacity>
                            );
                        })
                    )}

                    <TouchableOpacity
                        className="bg-madder py-3.5 rounded-xl items-center mt-5 flex-row justify-center"
                        onPress={() => navigation.navigate('AdminOrdersScreen')}
                        style={{ elevation: 2 }}
                    >
                        <Text className="text-white font-medium mr-2">View All Orders</Text>
                        <MaterialIcons name="arrow-forward" size={18} color="white" />
                    </TouchableOpacity>
                </Animated.View>
                </View>
                </ScrollView>
            )}
        </View>
    );
};

export default DashboardScreen;