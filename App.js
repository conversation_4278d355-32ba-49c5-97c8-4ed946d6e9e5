// Import polyfills first
import './polyfills';

import React, { useState, useEffect, memo } from 'react';
import { View, Image, ActivityIndicator } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import * as SplashScreen from 'expo-splash-screen';
import AppNavigator from './navigation/AppNavigator';
import { CartProvider } from './context/CartContext';
import { OrderProvider } from './context/OrderContext';
import { DeliveryPartnerProvider } from './context/DeliveryPartnerContext';
import { UserProvider } from './context/UserContext';
import { AuthProvider } from './context/AuthContext';
import { LocationProvider } from './context/LocationContext';
import { SocketProvider } from './context/SocketContext';
import DeliveryPartnerContextBridge from './Components/DeliveryPartnerContextBridge';
import AdminContextWrapper from './Components/AdminContextWrapper';
import SocketNotifications from './Components/SocketNotifications';
import PermissionManager from './Components/PermissionManager';
import Toast from 'react-native-toast-message';
import logo from './assets/logo.png';
import { setupAxiosInterceptors } from './utils/tokenRefresh';
import { savePermissionStatus, shouldSkipPermissionRequest } from './utils/permissionStorage';
import { setupNotificationListeners } from './utils/notificationService';
import { savePushTokenToBackend } from './utils/notificationUtils';
// Prevent auto-hide before app is ready
SplashScreen.preventAutoHideAsync();

// Memoized splash screen component for better performance
const SplashScreenComponent = memo(() => (
  <View style={{ flex: 1, backgroundColor: '#A31621', justifyContent: 'center', alignItems: 'center' }} pointerEvents="none">
    <Image source={logo} style={{ width: 200, height: 200, marginBottom: 20 }} resizeMode="contain" />
    <ActivityIndicator size="large" color="white" />
  </View>
));

const App = () => {
  const [appReady, setAppReady] = useState(false);
  const [permissionsReady, setPermissionsReady] = useState(false);
  const [showPermissions, setShowPermissions] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [forceReady, setForceReady] = useState(false);

  // Initialize axios interceptors and notification listeners (combined for performance)
  useEffect(() => {
    // Setup axios interceptors immediately
    setupAxiosInterceptors();
    console.log('Axios interceptors set up for token refresh');

    // Setup notification listeners
    console.log('Setting up notification listeners...');
    const notificationCleanup = setupNotificationListeners();

    return () => {
      if (notificationCleanup) {
        notificationCleanup();
        console.log('Notification listeners cleaned up');
      }
    };
  }, []);

  // Timeout fallback to prevent infinite splash screen
  useEffect(() => {
    const timeout = setTimeout(() => {
      console.log('⏰ Timeout reached - forcing app to be ready');
      setForceReady(true);
      setAppReady(true);
      setPermissionsReady(true);
    }, 5000); // 5 seconds timeout (reduced from 10)

    return () => clearTimeout(timeout);
  }, []);

  useEffect(() => {
    const prepareApp = async () => {
      try {
        console.log('🚀 Starting app preparation...');

        // Set app ready immediately for faster UI response
        setAppReady(true);
        console.log('✅ App ready state set to true');

        // Check permission status in background (non-blocking)
        setTimeout(async () => {
          try {
            const shouldSkip = await shouldSkipPermissionRequest();
            console.log('🔍 Should skip permissions:', shouldSkip);

            if (shouldSkip) {
              console.log('⏭️ Skipping permission request - already handled');
              setPermissionsReady(true);
            } else {
              // Show permission manager for first-time users
              console.log('📱 Showing permission manager for first-time user...');
              setShowPermissions(true);
            }
          } catch (error) {
            console.error('❌ Error checking permissions:', error);
            setPermissionsReady(true);
          }
        }, 50); // Reduced delay for faster startup

      } catch (e) {
        console.error('❌ Error in app preparation:', e);
        // If there's an error, skip permissions and continue
        setPermissionsReady(true);
        console.log('⚠️ Set permissions ready due to error');
      }
    };

    prepareApp();
  }, []);

  useEffect(() => {
    console.log('🔄 State check - appReady:', appReady, 'permissionsReady:', permissionsReady, 'forceReady:', forceReady);
    if ((appReady && permissionsReady) || forceReady) {
      console.log('🎉 App is ready - hiding splash screen');
      SplashScreen.hideAsync()
        .then(() => {
          console.log('✅ Splash screen hidden successfully');
        })
        .catch((error) => {
          console.error('❌ Error hiding splash screen:', error);
        });
    }
  }, [appReady, permissionsReady, forceReady]);

  const handlePermissionsComplete = async (permissions) => {
    console.log('🔐 Permissions completed:', permissions);

    try {
      // Save permission status for future app launches
      await savePermissionStatus(permissions);
      console.log('💾 Permission status saved successfully');
    } catch (error) {
      console.error('❌ Error saving permission status:', error);
    }

    setPermissionsReady(true);
    setShowPermissions(false);
    console.log('✅ Permissions ready state set to true');
  };

  if (!appReady) {
    return <SplashScreenComponent />;
  }

  if (showPermissions) {
    return <PermissionManager onPermissionsComplete={handlePermissionsComplete} />;
  }

  if (!permissionsReady) {
    return <SplashScreenComponent />;
  }

  return (
    <NavigationContainer>
      <AuthProvider>
        <SocketProvider>
          <UserProvider>
            <CartProvider>
              <OrderProvider>
                <LocationProvider>
                  <DeliveryPartnerProvider>
                    <AdminContextWrapper>
                      <DeliveryPartnerContextBridge />
                      <SocketNotifications />
                      <AppNavigator />
                      <Toast />
                    </AdminContextWrapper>
                  </DeliveryPartnerProvider>
                </LocationProvider>
              </OrderProvider>
            </CartProvider>
          </UserProvider>
        </SocketProvider>
      </AuthProvider>
    </NavigationContainer>
  );
};

export default App;
