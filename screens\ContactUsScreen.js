import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Linking, StatusBar, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const ContactUsScreen = () => {
    const navigation = useNavigation();



    const handleCall = () => {
        const phoneNumber = '+918825549901';
        Alert.alert(
            "Call Customer Support",
            `Would you like to call our customer support?\n\n📞 ${phoneNumber}\n\nAvailable: Mon-Sat, 9AM-6PM`,
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Call Now",
                    onPress: () => {
                        Linking.openURL(`tel:${phoneNumber}`).catch(err => {
                            Alert.alert("Error", "Unable to make phone call. Please try again.");
                        });
                    }
                }
            ]
        );
    };

    const handleEmail = () => {
        const email = '<EMAIL>';
        Alert.alert(
            "Email Customer Support",
            `Would you like to send an email to our support team?\n\n📧 ${email}\n\nWe respond within 24 hours`,
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Send Email",
                    onPress: () => {
                        Linking.openURL(`mailto:${email}?subject=Support Request - Meat Now App`).catch(err => {
                            Alert.alert("Error", "Unable to open email app. Please try again.");
                        });
                    }
                }
            ]
        );
    };

    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" />

            {/* Header with radius */}
            <View className="bg-madder pt-12 pb-6 px-4 rounded-b-3xl">
                <View className="flex-row items-center">
                    <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4 p-2 -ml-2">
                        <MaterialIcons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">Contact Us</Text>
                </View>
            </View>

            <ScrollView className="p-4">
                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                    <Text className="text-lg font-bold mb-3">Contact Information</Text>

                    <TouchableOpacity
                        className="flex-row items-center py-3 border-b border-gray-100"
                        onPress={handleCall}
                    >
                        <MaterialIcons name="call" size={20} color="#16A34A" />
                        <Text className="ml-3 text-gray-700">+91 8825549901</Text>
                        <Text className="ml-2 text-gray-500 text-xs">(Mon-Sat, 9AM-6PM)</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="flex-row items-center py-3"
                        onPress={handleEmail}
                    >
                        <MaterialIcons name="email" size={20} color="#A31621" />
                        <Text className="ml-3 text-gray-700"><EMAIL></Text>
                    </TouchableOpacity>
                </View>

                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                    <Text className="text-lg font-bold mb-3">Visit Us</Text>

                    <View className="flex-row items-start py-3">
                        <MaterialIcons name="location-on" size={20} color="#A31621" style={{ marginTop: 2 }} />
                        <View className="ml-3">
                            <Text className="text-gray-700 font-medium">Meat Now Headquarters</Text>
                            <Text className="text-gray-600 mt-1">
                                123 Main Street, Building A, Floor 4{'\n'}
                                Chennai, Tamil Nadu 600001{'\n'}
                                India
                            </Text>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default ContactUsScreen;
