import React from 'react';
import { View, Text, TouchableOpacity, Image, StatusBar } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const UserSelectionScreen = ({ navigation }) => {
    const handleUserTypeSelection = (userType) => {
        navigation.navigate('PreLoginScreen', { userType });
    };

    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" />
            
            <View className="bg-madder p-6 pt-16 pb-8 rounded-b-3xl">
                <Text className="text-2xl text-white font-bold">Welcome to Meat Now</Text>
                <Text className="text-white/80 mt-1">Select your user type to continue</Text>
            </View>
            
            <View className="p-6 flex-1 justify-center">
                {/* Customer Option */}
                <TouchableOpacity 
                    className="bg-white rounded-xl p-6 mb-4 shadow-sm flex-row items-center"
                    style={{
                        shadowColor: "#000",
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.1,
                        shadowRadius: 3,
                        elevation: 2
                    }}
                    onPress={() => handleUserTypeSelection('customer')}
                >
                    <View className="w-12 h-12 rounded-full bg-madder/10 items-center justify-center">
                        <MaterialIcons name="person" size={24} color="#A31621" />
                    </View>
                    <View className="ml-4 flex-1">
                        <Text className="text-lg font-bold text-gray-800">Customer</Text>
                        <Text className="text-gray-600">Order meat products for delivery</Text>
                    </View>
                    <MaterialIcons name="chevron-right" size={24} color="#A31621" />
                </TouchableOpacity>
                
                {/* Delivery Partner Option */}
                <TouchableOpacity 
                    className="bg-white rounded-xl p-6 mb-4 shadow-sm flex-row items-center"
                    style={{
                        shadowColor: "#000",
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.1,
                        shadowRadius: 3,
                        elevation: 2
                    }}
                    onPress={() => handleUserTypeSelection('delivery')}
                >
                    <View className="w-12 h-12 rounded-full bg-madder/10 items-center justify-center">
                        <MaterialIcons name="delivery-dining" size={24} color="#A31621" />
                    </View>
                    <View className="ml-4 flex-1">
                        <Text className="text-lg font-bold text-gray-800">Delivery Partner</Text>
                        <Text className="text-gray-600">Deliver orders to customers</Text>
                    </View>
                    <MaterialIcons name="chevron-right" size={24} color="#A31621" />
                </TouchableOpacity>
                
                {/* Admin Option */}
                <TouchableOpacity 
                    className="bg-white rounded-xl p-6 shadow-sm flex-row items-center"
                    style={{
                        shadowColor: "#000",
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.1,
                        shadowRadius: 3,
                        elevation: 2
                    }}
                    onPress={() => handleUserTypeSelection('admin')}
                >
                    <View className="w-12 h-12 rounded-full bg-madder/10 items-center justify-center">
                        <MaterialIcons name="admin-panel-settings" size={24} color="#A31621" />
                    </View>
                    <View className="ml-4 flex-1">
                        <Text className="text-lg font-bold text-gray-800">Admin</Text>
                        <Text className="text-gray-600">Manage products, orders and users</Text>
                    </View>
                    <MaterialIcons name="chevron-right" size={24} color="#A31621" />
                </TouchableOpacity>
            </View>
            
            <View className="p-6">
                <Image 
                    source={require('../assets/logo.png')} 
                    className="w-16 h-16 self-center mb-4"
                    resizeMode="contain"
                />
                <Text className="text-center text-gray-500">© 2025 Meat Now. All rights reserved.</Text>
            </View>
        </View>
    );
};

export default UserSelectionScreen;
