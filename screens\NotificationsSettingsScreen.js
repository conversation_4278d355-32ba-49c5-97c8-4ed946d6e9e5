import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Switch, Alert, StatusBar } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from '@react-navigation/native';
import * as Notifications from 'expo-notifications';

const NotificationsSettingsScreen = () => {
    const navigation = useNavigation();
    const [notificationsEnabled, setNotificationsEnabled] = useState(false);

    useEffect(() => {
        checkNotificationPermissions();
    }, []);

    const checkNotificationPermissions = async () => {
        try {
            const { status } = await Notifications.getPermissionsAsync();
            setNotificationsEnabled(status === 'granted');
        } catch (error) {
            console.log('Error checking notification permissions:', error);
        }
    };

    const handleToggleNotifications = async () => {
        try {
            if (notificationsEnabled) {
                // If notifications are enabled, show alert to disable in settings
                Alert.alert(
                    "Disable Notifications",
                    "To disable notifications, please go to your device settings and turn off notifications for Meat Now app.",
                    [{ text: "OK" }]
                );
            } else {
                // Request notification permissions
                const { status } = await Notifications.requestPermissionsAsync();
                if (status === 'granted') {
                    setNotificationsEnabled(true);
                    Alert.alert(
                        "Notifications Enabled",
                        "You will now receive notifications about your orders and important updates.",
                        [{ text: "OK" }]
                    );
                } else {
                    Alert.alert(
                        "Permission Denied",
                        "Please enable notifications in your device settings to receive order updates.",
                        [{ text: "OK" }]
                    );
                }
            }
        } catch (error) {
            Alert.alert("Error", "Unable to change notification settings. Please try again.");
        }
    };

    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" />

            {/* Header with radius */}
            <View className="bg-madder pt-12 pb-6 px-4 rounded-b-3xl">
                <View className="flex-row items-center">
                    <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4 p-2 -ml-2">
                        <MaterialIcons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">Notification Settings</Text>
                </View>
            </View>

            <View className="p-4">
                <View className="bg-white rounded-xl shadow-sm overflow-hidden">
                    <Text className="p-4 text-lg font-bold text-gray-800">App Notifications</Text>

                    <View className="flex-row items-center justify-between p-4 border-t border-gray-100">
                        <View className="flex-row items-center">
                            <View className="w-8 h-8 rounded-full bg-madder/10 items-center justify-center">
                                <MaterialIcons name="notifications" size={18} color="#A31621" />
                            </View>
                            <View className="ml-3">
                                <Text className="text-gray-700 font-medium">Push Notifications</Text>
                                <Text className="text-gray-500 text-xs">Receive order updates and important alerts</Text>
                            </View>
                        </View>
                        <Switch
                            value={notificationsEnabled}
                            onValueChange={handleToggleNotifications}
                            trackColor={{ false: "#D1D5DB", true: "#A31621" }}
                            thumbColor={notificationsEnabled ? "#FFFFFF" : "#FFFFFF"}
                            ios_backgroundColor="#D1D5DB"
                        />
                    </View>
                </View>

                {/* Information Card */}
                <View className="bg-blue-50 rounded-xl p-4 mt-4">
                    <View className="flex-row items-start">
                        <MaterialIcons name="info" size={20} color="#3B82F6" style={{ marginTop: 2 }} />
                        <View className="ml-3 flex-1">
                            <Text className="text-blue-800 font-medium text-sm">About Notifications</Text>
                            <Text className="text-blue-700 text-xs mt-1">
                                When enabled, you'll receive notifications about order status updates, delivery alerts, and important app information. You can manage detailed notification preferences in your device settings.
                            </Text>
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default NotificationsSettingsScreen;
