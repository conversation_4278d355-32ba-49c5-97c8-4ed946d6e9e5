const express = require("express");
const dotenv = require("dotenv");
const colors = require("colors");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const http = require("http");
const { Server } = require("socket.io");
const { verifyToken } = require('./utils/jwtUtils');
const connectDB = require("./config/db");
const { errorHandler } = require('./middleware/errorHandler');
const { initCronJobs } = require('./scripts/setupCronJobs');

dotenv.config();

// Connect to MongoDB
console.log('Connecting to MongoDB Atlas...');
console.log('MongoDB URI:', process.env.MONGO_URI ? 'URI is set' : 'URI is not set');
connectDB();

const app = express();
const server = http.createServer(app);
// Use a simple CORS configuration that allows all origins for React Native app
app.use(cors());

// Configure Socket.IO with enhanced error handling and reconnection settings
const io = new Server(server, {
    cors: {
        // Allow connections from any origin for React Native app
        origin: '*',
        methods: ['GET', 'POST', 'OPTIONS'],
        credentials: true,
        allowedHeaders: ['Content-Type', 'Authorization']
    },
    // Connection settings
    pingTimeout: 60000,
    pingInterval: 25000,
    // Support both websocket and polling for maximum compatibility
    transports: ['websocket', 'polling'],
    allowEIO3: true,
    // Allow larger payloads
    maxHttpBufferSize: 1e8,
    // Reconnection settings
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    // Additional settings for better performance
    connectTimeout: 45000,
    // Path can be customized if needed
    path: '/socket.io'
});

// Socket.io middleware for authentication
io.use(async (socket, next) => {
    try {
        const token = socket.handshake.query.token;
        const userId = socket.handshake.query.userId;
        const userType = socket.handshake.query.userType;

        // Log connection attempt with available info
        console.log(`Socket connection attempt: ${socket.id} from ${socket.handshake.address}`.cyan);

        // If token is provided, verify it
        if (token) {
            try {
                // Verify token (ignoring expiration for socket connections)
                const decoded = verifyToken(token);
                if (decoded) {
                    // Store user info in socket object
                    socket.userId = decoded.id;
                    socket.userType = decoded.userType;
                    socket.authenticated = true;
                    console.log(`Socket authenticated via token: ${socket.id} (${decoded.userType})`.green);
                    return next();
                } else {
                    console.log(`Invalid socket auth token: ${socket.id}`.yellow);
                    // Continue without authentication
                }
            } catch (tokenError) {
                console.error(`Token verification error: ${tokenError.message}`.red);
                // Continue without authentication
            }
        }

        // If userId and userType are provided directly (fallback)
        if (userId && userType) {
            socket.userId = userId;
            socket.userType = userType;
            socket.authenticated = false; // Not authenticated via token
            console.log(`Socket identified without token: ${socket.id} (${userType})`.yellow);
            return next();
        }

        // Allow connection without authentication
        console.log(`Socket connected without authentication: ${socket.id}`.yellow);
        socket.authenticated = false;
        next();
    } catch (error) {
        console.error(`Socket auth error: ${error.message}`.red);
        // Allow connection even if authentication fails
        socket.authenticated = false;
        next();
    }
});

// Socket.io connection
io.on("connection", (socket) => {
    console.log(`User connected: ${socket.id} ${socket.userType ? `(${socket.userType})` : ''}`.green);

    // Track connected sockets by user type for monitoring
    const userTypeCount = {
        ADMIN: 0,
        USER: 0,
        DELIVERY_PARTNER: 0,
        UNKNOWN: 0
    };

    // Auto-join room if user is authenticated via middleware
    if (socket.userId && socket.userType) {
        // Join user-specific room
        const userRoom = `${socket.userType}_${socket.userId}`;
        socket.join(userRoom);
        console.log(`User auto-joined room: ${userRoom}`.blue);

        // Join general rooms based on user type
        if (socket.userType === 'ADMIN') {
            socket.join('admin_room');
            userTypeCount.ADMIN++;
        } else if (socket.userType === 'DELIVERY_PARTNER') {
            socket.join('delivery_room');
            userTypeCount.DELIVERY_PARTNER++;
        } else if (socket.userType === 'USER') {
            socket.join('user_room');
            userTypeCount.USER++;
        } else {
            userTypeCount.UNKNOWN++;
        }

        // Notify client about successful auto-join
        socket.emit('join_success', {
            userId: socket.userId,
            userType: socket.userType,
            room: userRoom,
            authenticated: socket.authenticated
        });
    } else {
        userTypeCount.UNKNOWN++;
    }

    // Log current connection counts
    console.log(`Connected users: Admin: ${userTypeCount.ADMIN}, Users: ${userTypeCount.USER}, Delivery: ${userTypeCount.DELIVERY_PARTNER}, Unknown: ${userTypeCount.UNKNOWN}`.cyan);

    // Join a room based on user type and ID (for clients not using token auth)
    socket.on("join", ({ userId, userType }) => {
        try {
            if (!userId || !userType) {
                socket.emit('join_error', { error: 'Missing userId or userType' });
                return;
            }

            // Normalize user type to uppercase
            const normalizedUserType = userType.toUpperCase();

            // Validate user type
            if (!['ADMIN', 'USER', 'DELIVERY_PARTNER'].includes(normalizedUserType)) {
                socket.emit('join_error', { error: 'Invalid userType' });
                return;
            }

            // Create room name
            const room = `${normalizedUserType}_${userId}`;

            // Join user-specific room
            socket.join(room);
            console.log(`User joined room: ${room}`.blue);

            // Join general rooms based on user type
            if (normalizedUserType === 'ADMIN') {
                socket.join('admin_room');
            } else if (normalizedUserType === 'DELIVERY_PARTNER') {
                socket.join('delivery_room');
            } else if (normalizedUserType === 'USER') {
                socket.join('user_room');
            }

            // Store user info in socket for later use
            socket.userId = userId;
            socket.userType = normalizedUserType;

            // Notify client that join was successful
            socket.emit('join_success', {
                userId,
                userType: normalizedUserType,
                room,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error(`Error in join handler: ${error.message}`.red);
            socket.emit('join_error', { error: 'Server error during room join' });
        }
    });

    // Handle new order with acknowledgment
    socket.on("new_order", (orderData, callback) => {
        try {
            console.log(`New order received: ${orderData.id || orderData._id}`.green);

            // Validate order data
            if (!orderData || !orderData.id && !orderData._id) {
                console.warn('Invalid order data received'.yellow);
                if (typeof callback === 'function') {
                    callback({ success: false, error: 'Invalid order data' });
                }
                return;
            }

            // Add timestamp if not present
            if (!orderData.timestamp) {
                orderData.timestamp = new Date().toISOString();
            }

            // Broadcast to admin room
            io.to('admin_room').emit("order_update", {
                type: "new",
                order: orderData,
                timestamp: orderData.timestamp
            });

            // Also notify the specific user
            if (orderData.userId) {
                io.to(`USER_${orderData.userId}`).emit("order_update", {
                    type: "new",
                    order: orderData,
                    timestamp: orderData.timestamp
                });
            }

            // Send acknowledgment if callback provided
            if (typeof callback === 'function') {
                callback({ success: true, orderId: orderData.id || orderData._id });
            }
        } catch (error) {
            console.error(`Error handling new_order: ${error.message}`.red);
            if (typeof callback === 'function') {
                callback({ success: false, error: 'Server error processing order' });
            }
        }
    });

    // Handle order status update with acknowledgment
    socket.on("update_order_status", ({ orderId, status, userId, deliveryPartnerId, timestamp }, callback) => {
        try {
            console.log(`Order status update: ${orderId} -> ${status}`.green);

            // Validate required data
            if (!orderId || !status) {
                console.warn('Invalid order status update data'.yellow);
                if (typeof callback === 'function') {
                    callback({ success: false, error: 'Missing orderId or status' });
                }
                return;
            }

            // Use provided timestamp or create new one
            const eventTimestamp = timestamp || new Date().toISOString();

            // Create event payload
            const statusUpdatePayload = {
                orderId,
                status,
                timestamp: eventTimestamp,
                updatedBy: socket.userType || 'UNKNOWN',
                updatedById: socket.userId || 'UNKNOWN'
            };

            // Broadcast to relevant rooms
            io.to('admin_room').emit("order_status_changed", statusUpdatePayload);

            if (userId) {
                io.to(`USER_${userId}`).emit("order_status_changed", statusUpdatePayload);
            }

            if (deliveryPartnerId) {
                io.to(`DELIVERY_PARTNER_${deliveryPartnerId}`).emit("order_status_changed", statusUpdatePayload);
            }

            // Send acknowledgment if callback provided
            if (typeof callback === 'function') {
                callback({ success: true, orderId, status });
            }
        } catch (error) {
            console.error(`Error handling update_order_status: ${error.message}`.red);
            if (typeof callback === 'function') {
                callback({ success: false, error: 'Server error updating order status' });
            }
        }
    });

    // Handle delivery partner assignment with acknowledgment
    socket.on("assign_delivery_partner", ({ orderId, partnerId, orderDetails, timestamp }, callback) => {
        try {
            console.log(`Assigning order ${orderId} to delivery partner ${partnerId}`.green);

            // Validate required data
            if (!orderId || !partnerId) {
                console.warn('Invalid delivery assignment data'.yellow);
                if (typeof callback === 'function') {
                    callback({ success: false, error: 'Missing orderId or partnerId' });
                }
                return;
            }

            // Use provided timestamp or create new one
            const eventTimestamp = timestamp || new Date().toISOString();

            // Create assignment payload
            const assignmentPayload = {
                orderId,
                orderDetails,
                timestamp: eventTimestamp,
                assignedBy: socket.userType || 'UNKNOWN',
                assignedById: socket.userId || 'UNKNOWN'
            };

            // Emit to the specific delivery partner
            io.to(`DELIVERY_PARTNER_${partnerId}`).emit("new_delivery_assignment", assignmentPayload);

            // Also notify admin room
            io.to('admin_room').emit("delivery_partner_assigned", {
                ...assignmentPayload,
                partnerId
            });

            // Send acknowledgment if callback provided
            if (typeof callback === 'function') {
                callback({ success: true, orderId, partnerId });
            }
        } catch (error) {
            console.error(`Error handling assign_delivery_partner: ${error.message}`.red);
            if (typeof callback === 'function') {
                callback({ success: false, error: 'Server error assigning delivery partner' });
            }
        }
    });

    // Handle disconnect
    socket.on("disconnect", (reason) => {
        console.log(`User disconnected: ${socket.id}, reason: ${reason}`.yellow);

        // Update user type counts
        if (socket.userType === 'ADMIN') {
            userTypeCount.ADMIN = Math.max(0, userTypeCount.ADMIN - 1);
        } else if (socket.userType === 'DELIVERY_PARTNER') {
            userTypeCount.DELIVERY_PARTNER = Math.max(0, userTypeCount.DELIVERY_PARTNER - 1);
        } else if (socket.userType === 'USER') {
            userTypeCount.USER = Math.max(0, userTypeCount.USER - 1);
        } else {
            userTypeCount.UNKNOWN = Math.max(0, userTypeCount.UNKNOWN - 1);
        }
    });

    // Handle errors
    socket.on("error", (error) => {
        console.error(`Socket error for ${socket.id}: ${error}`.red);
    });

    // Ping to keep connection alive and check health
    socket.on("ping", (data, callback) => {
        const response = {
            status: 'ok',
            time: new Date().toISOString(),
            socketId: socket.id,
            authenticated: !!socket.authenticated,
            userType: socket.userType || 'UNKNOWN'
        };

        if (typeof callback === 'function') {
            callback(response);
        } else {
            socket.emit('pong', response);
        }
    });

    // Debug event to log all rooms a socket is in
    socket.on("get_my_rooms", (callback) => {
        try {
            const rooms = Array.from(socket.rooms);
            console.log(`Rooms for socket ${socket.id}:`, rooms);

            if (typeof callback === 'function') {
                callback({
                    socketId: socket.id,
                    rooms,
                    userId: socket.userId,
                    userType: socket.userType
                });
            }
        } catch (error) {
            console.error(`Error getting rooms: ${error.message}`.red);
            if (typeof callback === 'function') {
                callback({ error: 'Failed to get rooms' });
            }
        }
    });
});

// Make io accessible to route handlers
app.set('io', io);

// Middleware
app.use(express.json());
app.use(cookieParser());

// Import routes
const authRoutes = require('./routes/authRoutes');
const productRoutes = require('./routes/productRoutes');
const orderRoutes = require('./routes/orderRoutes');
const userRoutes = require('./routes/userRoutes');
const userAddressRoutes = require('./routes/userAddressRoutes');
const adminRoutes = require('./routes/adminRoutes');
const deliveryRoutes = require('./routes/deliveryRoutes');
const categoryRoutes = require('./routes/categoryRoutes');
const invoiceRoutes = require('./routes/invoiceRoutes');

// Base route
app.get("/", (req, res) => {
    res.send("API is running...");
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/user', userAddressRoutes); // User address routes
app.use('/api/products', productRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/delivery', deliveryRoutes);
app.use('/api/invoices', invoiceRoutes);

// Error handling middleware
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

server.listen(PORT,'0.0.0.0', () => {
    console.log(`Server running on port ${PORT}`.yellow.bold);

    // Initialize cron jobs
    initCronJobs();
});
