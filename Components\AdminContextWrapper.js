import React, { useState, useEffect } from 'react';
import { getUserData } from '../utils/authStorage';
import { USER_TYPES } from '../config/constants';
import { AdminDeliveryPartnerProvider } from '../context/AdminDeliveryPartnerContext';
import { AdminProvider } from '../context/AdminContext';

/**
 * A wrapper component that conditionally renders admin contexts only for admin users
 * This prevents unnecessary API calls and data fetching for non-admin users
 */
const AdminContextWrapper = ({ children }) => {
    const [isAdmin, setIsAdmin] = useState(false);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const checkUserType = async () => {
            try {
                const userData = await getUserData();
                const userType = userData?.userType?.toUpperCase();

                console.log('AdminContextWrapper: User type:', userType);
                setIsAdmin(userType === USER_TYPES.ADMIN);
            } catch (error) {
                console.error('AdminContextWrapper: Error checking user type:', error);
                setIsAdmin(false);
            } finally {
                setLoading(false);
            }
        };

        checkUserType();
    }, []);

    // Show nothing while loading
    if (loading) {
        return null;
    }

    // If user is admin, wrap children in admin contexts
    if (isAdmin) {
        console.log('AdminContextWrapper: User is admin, providing admin contexts');
        return (
            <AdminDeliveryPartnerProvider>
                <AdminProvider>
                    {children}
                </AdminProvider>
            </AdminDeliveryPartnerProvider>
        );
    }

    // If user is not admin, wrap children in empty providers to avoid context errors
    console.log('AdminContextWrapper: User is not admin, providing empty admin contexts');
    return (
        <AdminDeliveryPartnerProvider>
            <AdminProvider>
                {children}
            </AdminProvider>
        </AdminDeliveryPartnerProvider>
    );
};

export default AdminContextWrapper;
