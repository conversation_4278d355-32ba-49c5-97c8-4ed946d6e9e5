import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

/**
 * Configure notification behavior
 */
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

/**
 * Handle incoming push notifications when app is in foreground
 */
export const setupNotificationListeners = () => {
  // Listen for notifications received while app is foregrounded
  const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
    console.log('Notification received in foreground:', notification);
    
    const { data } = notification.request.content;
    
    // Handle different types of notifications
    if (data?.type === 'order_status_update') {
      console.log('Order status update notification:', data);
      // You can add custom handling here if needed
    } else if (data?.type === 'new_order') {
      console.log('New order notification:', data);
      // You can add custom handling here if needed
    }
  });

  // Listen for notification responses (when user taps notification)
  const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
    console.log('Notification response received:', response);
    
    const { data } = response.notification.request.content;
    
    // Handle navigation based on notification type
    if (data?.type === 'order_status_update') {
      // Navigate to orders screen
      console.log('Should navigate to orders screen for order:', data.orderNumber);
    } else if (data?.type === 'new_order') {
      // Navigate to admin orders screen
      console.log('Should navigate to admin orders screen for order:', data.orderNumber);
    }
  });

  return () => {
    foregroundSubscription.remove();
    responseSubscription.remove();
  };
};

/**
 * Schedule a local notification
 * @param {string} title - Notification title
 * @param {string} body - Notification body
 * @param {Object} data - Additional data
 * @param {number} seconds - Delay in seconds (optional)
 */
export const scheduleLocalNotification = async (title, body, data = {}, seconds = 0) => {
  try {
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: 'default',
      },
      trigger: seconds > 0 ? { seconds } : null,
    });
    
    console.log('Local notification scheduled:', notificationId);
    return notificationId;
  } catch (error) {
    console.error('Error scheduling local notification:', error);
    return null;
  }
};

/**
 * Cancel a scheduled notification
 * @param {string} notificationId - Notification ID to cancel
 */
export const cancelNotification = async (notificationId) => {
  try {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
    console.log('Notification cancelled:', notificationId);
  } catch (error) {
    console.error('Error cancelling notification:', error);
  }
};

/**
 * Cancel all scheduled notifications
 */
export const cancelAllNotifications = async () => {
  try {
    await Notifications.cancelAllScheduledNotificationsAsync();
    console.log('All notifications cancelled');
  } catch (error) {
    console.error('Error cancelling all notifications:', error);
  }
};

/**
 * Get notification permissions status
 */
export const getNotificationPermissions = async () => {
  try {
    const settings = await Notifications.getPermissionsAsync();
    return settings;
  } catch (error) {
    console.error('Error getting notification permissions:', error);
    return null;
  }
};

/**
 * Show a local notification immediately for testing
 * @param {string} title - Notification title
 * @param {string} body - Notification body
 * @param {Object} data - Additional data
 */
export const showTestNotification = async (title, body, data = {}) => {
  try {
    await Notifications.presentNotificationAsync({
      title,
      body,
      data,
      sound: 'default',
    });
    console.log('Test notification shown');
  } catch (error) {
    console.error('Error showing test notification:', error);
  }
};

export default {
  setupNotificationListeners,
  scheduleLocalNotification,
  cancelNotification,
  cancelAllNotifications,
  getNotificationPermissions,
  showTestNotification,
};
