import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, TextInput } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const FAQScreen = () => {
    const navigation = useNavigation();
    const [searchQuery, setSearchQuery] = useState('');
    const [expandedId, setExpandedId] = useState(null);

    // FAQ data
    const faqData = [
        {
            id: '1',
            category: 'Orders',
            questions: [
                {
                    id: '1-1',
                    question: 'How do I track my order?',
                    answer: 'You can track your order by going to the Orders section in your profile. There you will see all your orders and their current status. Click on any order to see detailed tracking information.'
                },
                {
                    id: '1-2',
                    question: 'Can I cancel my order?',
                    answer: 'Yes, you can cancel your order if it has not been processed yet. Go to the Orders section, select the order you want to cancel, and click on the Cancel button. If the order has already been processed, you may not be able to cancel it.'
                },
                {
                    id: '1-3',
                    question: 'What if I receive a damaged product?',
                    answer: 'If you receive a damaged product, please take a photo of it and contact our customer support within 24 hours of delivery. We will arrange for a replacement or refund as per our policy.'
                }
            ]
        },
        {
            id: '2',
            category: 'Payments',
            questions: [
                {
                    id: '2-1',
                    question: 'What payment methods do you accept?',
                    answer: 'We accept various payment methods including credit/debit cards, UPI, net banking, and cash on delivery. You can select your preferred payment method during checkout.'
                },
                {
                    id: '2-2',
                    question: 'Is it safe to save my card details?',
                    answer: 'Yes, it is safe to save your card details. We use industry-standard encryption to protect your payment information. Your card details are stored securely and are not accessible to anyone.'
                },
                {
                    id: '2-3',
                    question: 'How do I use my fresh coins for payment?',
                    answer: 'You can use your coins during checkout. On the payment page, you will see an option to apply your available coins. Each 10 coins is equivalent to ₹1 discount on your order.'
                }
            ]
        },
        {
            id: '3',
            category: 'Delivery',
            questions: [
                {
                    id: '3-1',
                    question: 'What are your delivery hours?',
                    answer: 'We deliver from 10 AM to 8 PM every day. You can select your preferred delivery slot during checkout.'
                },
                {
                    id: '3-2',
                    question: 'Do you deliver to my area?',
                    answer: 'We deliver to most areas in the city. You can check if we deliver to your area by entering your pincode on the product page or during checkout.'
                },
                {
                    id: '3-3',
                    question: 'Is there a minimum order value for free delivery?',
                    answer: 'Yes, orders above ₹299 qualify for free delivery. For orders below this amount, a delivery fee of ₹30 will be charged.'
                }
            ]
        },
        {
            id: '4',
            category: 'Account',
            questions: [
                {
                    id: '4-1',
                    question: 'How do I reset my password?',
                    answer: 'To reset your password, go to the login screen and click on "Forgot Password". Enter your registered phone number, and we will send you an OTP to reset your password.'
                },
                {
                    id: '4-2',
                    question: 'How can I update my profile information?',
                    answer: 'You can update your profile information by going to your profile page and clicking on the Edit button. From there, you can update your name, phone number, and other details.'
                },
                {
                    id: '4-3',
                    question: 'How do I delete my account?',
                    answer: 'To delete your account, please contact our customer support. They will guide you through the account deletion process.'
                }
            ]
        }
    ];

    // Filter FAQs based on search query
    const filteredFAQs = searchQuery.trim() === ''
        ? faqData
        : faqData.map(category => ({
            ...category,
            questions: category.questions.filter(q =>
                q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                q.answer.toLowerCase().includes(searchQuery.toLowerCase())
            )
        })).filter(category => category.questions.length > 0);

    const toggleExpand = (id) => {
        setExpandedId(expandedId === id ? null : id);
    };

    const renderFAQItem = (item) => (
        <TouchableOpacity
            key={item.id}
            className="bg-white rounded-xl p-4 mb-3 shadow-sm"
            onPress={() => toggleExpand(item.id)}
            activeOpacity={0.7}
        >
            <View className="flex-row justify-between items-center">
                <Text className="text-gray-800 font-bold flex-1 pr-4">{item.question}</Text>
                <MaterialIcons
                    name={expandedId === item.id ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                    size={24}
                    color="#A31621"
                />
            </View>

            {expandedId === item.id && (
                <Text className="text-gray-600 mt-3 leading-5">{item.answer}</Text>
            )}
        </TouchableOpacity>
    );

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-6 pt-16 pb-6">
                <View className="flex-row items-center mb-4">
                    <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                        <MaterialIcons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">Frequently Asked Questions</Text>
                </View>

                <View
                    className="bg-white rounded-xl flex-row items-center px-4 mr-1 mb-1"
                    style={{
                        shadowColor: '#A31621',
                        shadowOffset: { width: 3, height: 2 },
                        shadowOpacity: 0.15,
                        shadowRadius: 4,
                        elevation: 4,
                    }}
                >
                    <MaterialIcons name="search" size={24} color="#A31621" />
                    <TextInput
                        placeholder="Search FAQs..."
                        className="flex-1 p-3"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor="#888"
                    />
                    {searchQuery.length > 0 && (
                        <TouchableOpacity onPress={() => setSearchQuery('')}>
                            <MaterialIcons name="close" size={24} color="#A31621" />
                        </TouchableOpacity>
                    )}
                </View>
            </View>

            <ScrollView className="p-4">
                {filteredFAQs.map(category => (
                    <View key={category.id} className="mb-6">
                        <Text className="text-lg font-bold text-gray-800 mb-3">{category.category}</Text>
                        {category.questions.map(item => renderFAQItem(item))}
                    </View>
                ))}

                {filteredFAQs.length === 0 && (
                    <View className="items-center justify-center py-10">
                        <View className="w-16 h-16 rounded-full bg-madder/10 items-center justify-center mb-4">
                            <MaterialIcons name="search-off" size={32} color="#A31621" />
                        </View>
                        <Text className="text-lg font-bold text-gray-800 mb-2">No Results Found</Text>
                        <Text className="text-gray-600 text-center">
                            We couldn't find any FAQs matching your search. Please try a different search term.
                        </Text>
                    </View>
                )}

                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                    <Text className="text-lg font-bold mb-3">Still have questions?</Text>
                    <Text className="text-gray-600 mb-4">
                        If you couldn't find the answer to your question, please contact our customer support.
                    </Text>

                    <TouchableOpacity
                        className="bg-madder py-3 rounded-xl items-center"
                        onPress={() => navigation.navigate('ContactUsScreen')}
                    >
                        <Text className="text-white font-bold">Contact Support</Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </View>
    );
};

export default FAQScreen;
