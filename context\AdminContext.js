import React, { createContext, useState, useContext, useEffect } from 'react';
import { useUser } from './UserContext';
import { useAdminDeliveryPartner } from './AdminDeliveryPartnerContext';
import { useSocket } from './SocketContext';
import { getAllOrders, updateOrderStatus } from '../utils/api/orderApi';
import { getAllProducts, updateProduct, createProduct } from '../utils/api/productApi';
import { getDashboardAnalytics } from '../utils/api/analyticsApi';
import { getUserData, getAuthToken } from '../utils/authStorage';
import { USER_TYPES } from '../config/constants';

// Create the context
const AdminContext = createContext();

// Create a provider component
export const AdminProvider = ({ children }) => {
    const { users } = useUser();
    const { deliveryPartners } = useAdminDeliveryPartner();
    const {
        connected: socketConnected,
        emitOrderStatusUpdate,
        onOrderUpdate,
        onOrderStatusUpdate
    } = useSocket();

    // Initialize with empty arrays, will be populated from API
    const [orders, setOrders] = useState([]);
    const [products, setProducts] = useState([]);
    const [analytics, setAnalytics] = useState({
        sales: {
            daily: 0,
            weekly: 0,
            monthly: 0,
            yearly: 0,
            growth: {
                daily: 0,
                weekly: 0,
                monthly: 0,
                yearly: 0
            }
        },
        orders: {
            pending: 0,
            inTransit: 0,
            delivered: 0,
            cancelled: 0,
            total: 0,
            growth: 0
        },
        users: {
            total: 0,
            active: 0,
            new: 0,
            growth: 0
        },
        deliveryPartners: {
            total: 0,
            active: 0,
            available: 0,
            growth: 0
        },
        products: {
            total: 0,
            outOfStock: 0,
            lowStock: 0,
            topSelling: []
        },
        revenue: {
            daily: [],
            weekly: [],
            monthly: []
        }
    });

    // Loading states
    const [ordersLoading, setOrdersLoading] = useState(true);
    const [productsLoading, setProductsLoading] = useState(true);
    const [analyticsLoading, setAnalyticsLoading] = useState(true);

    // Fetch orders from API on component mount
    useEffect(() => {
        const fetchOrders = async () => {
            try {
                setOrdersLoading(true);

                // Get user data to check user type
                const userData = await getUserData();
                const userType = userData?.userType?.toUpperCase();

                console.log('AdminContext: User type:', userType);

                // Skip fetching completely if user is not an admin
                if (userType !== USER_TYPES.ADMIN) {
                    console.log('AdminContext: User is not admin, skipping admin orders fetch completely');
                    setOrders([]);
                    setOrdersLoading(false);
                    return;
                }

                // Only proceed if user is an admin
                console.log('AdminContext: Fetching orders from API for admin');
                try {
                    console.log('AdminContext: Attempting to fetch orders from API');
                    const response = await getAllOrders();
                    console.log('AdminContext: Orders fetched:', response);

                    // Check if response has orders property
                    if (response && response.orders && response.orders.length > 0) {
                        console.log('AdminContext: Found orders in response.orders');
                        setOrders(response.orders);
                    }
                    // Fallback: check if response is an array directly
                    else if (response && Array.isArray(response) && response.length > 0) {
                        console.log('AdminContext: Found orders in response array');
                        setOrders(response);
                    }
                    // Check if response has data.orders property
                    else if (response && response.data && response.data.orders && response.data.orders.length > 0) {
                        console.log('AdminContext: Found orders in response.data.orders');
                        setOrders(response.data.orders);
                    }
                    // No orders found
                    else {
                        console.log('AdminContext: No orders returned from API, trying fallback endpoint');

                        // Try fallback endpoint
                        try {
                            // Use the same API_URL from constants to ensure consistency
                            const { API_URL } = require('../config/constants');
                            const fallbackResponse = await fetch(`${API_URL}/orders`, {
                                headers: {
                                    'Authorization': `Bearer ${await getAuthToken()}`
                                }
                            });

                            if (fallbackResponse.ok) {
                                const fallbackData = await fallbackResponse.json();
                                console.log('AdminContext: Orders fetched from fallback endpoint:', fallbackData);

                                if (fallbackData.orders && Array.isArray(fallbackData.orders)) {
                                    console.log('AdminContext: Found orders in fallback response.orders');
                                    setOrders(fallbackData.orders);
                                } else if (Array.isArray(fallbackData)) {
                                    console.log('AdminContext: Found orders in fallback response array');
                                    setOrders(fallbackData);
                                } else {
                                    console.log('AdminContext: No orders found in fallback response');
                                    setOrders([]);
                                }
                            } else {
                                console.log('AdminContext: Fallback endpoint failed, using empty array');
                                setOrders([]);
                            }
                        } catch (fallbackError) {
                            console.error('AdminContext: Error fetching from fallback endpoint:', fallbackError);
                            setOrders([]);
                        }
                    }
                } catch (apiError) {
                    console.error('AdminContext: Error fetching orders from API:', apiError);
                    setOrders([]);
                }
            } catch (error) {
                console.error('AdminContext: Error in order fetch process:', error);
                // Fallback to empty array if process fails
                setOrders([]);
            } finally {
                setOrdersLoading(false);
            }
        };

        fetchOrders();
    }, []);

    // Fetch products from API on component mount
    useEffect(() => {
        const fetchProducts = async () => {
            try {
                setProductsLoading(true);

                // Get user data to check user type
                const userData = await getUserData();
                const userType = userData?.userType?.toUpperCase();

                console.log('AdminContext: User type for products fetch:', userType);

                // Skip fetching completely if user is not an admin
                if (userType !== USER_TYPES.ADMIN) {
                    console.log('AdminContext: User is not admin, skipping admin products fetch completely');
                    setProducts([]);
                    setProductsLoading(false);
                    return;
                }

                // Only proceed if user is an admin
                console.log('AdminContext: Fetching products from API for admin');
                try {
                    const response = await getAllProducts();
                    console.log('AdminContext: Products fetched:', response);

                    // Check if response has products property
                    if (response && response.products && response.products.length > 0) {
                        console.log('AdminContext: Found products in response.products');
                        setProducts(response.products);
                    }
                    // Fallback: check if response is an array directly
                    else if (response && Array.isArray(response) && response.length > 0) {
                        console.log('AdminContext: Found products in response array');
                        setProducts(response);
                    }
                    // No products found
                    else {
                        console.log('AdminContext: No products returned from API');
                        setProducts([]);
                    }
                } catch (apiError) {
                    console.error('AdminContext: Error fetching products from API:', apiError);
                    setProducts([]);
                }
            } catch (error) {
                console.error('AdminContext: Error in product fetch process:', error);
                // Fallback to empty array if process fails
                setProducts([]);
            } finally {
                setProductsLoading(false);
            }
        };

        fetchProducts();
    }, []);

    // Fetch analytics from API on component mount
    useEffect(() => {
        const fetchAnalytics = async () => {
            try {
                setAnalyticsLoading(true);
                console.log('AdminContext: Starting analytics fetch process');

                // Get user data to check user type
                const userData = await getUserData();
                const userType = userData?.userType?.toUpperCase();

                console.log('AdminContext: User type for analytics fetch:', userType);

                // Skip fetching completely if user is not an admin
                if (userType !== USER_TYPES.ADMIN) {
                    console.log('AdminContext: User is not admin, skipping analytics fetch completely');
                    // Set empty analytics object
                    const emptyAnalytics = {
                        sales: {
                            daily: 0,
                            weekly: 0,
                            monthly: 0,
                            yearly: 0,
                            growth: {
                                daily: 0,
                                weekly: 0,
                                monthly: 0,
                                yearly: 0
                            }
                        },
                        orders: {
                            pending: 0,
                            inTransit: 0,
                            delivered: 0,
                            cancelled: 0,
                            total: 0,
                            growth: 0
                        },
                        users: {
                            total: 0,
                            active: 0,
                            new: 0,
                            growth: 0
                        },
                        deliveryPartners: {
                            total: 0,
                            active: 0,
                            available: 0,
                            growth: 0
                        },
                        products: {
                            total: 0,
                            outOfStock: 0,
                            lowStock: 0,
                            topSelling: []
                        },
                        revenue: {
                            daily: [],
                            weekly: [],
                            monthly: []
                        }
                    };
                    setAnalytics(emptyAnalytics);
                    setAnalyticsLoading(false);
                    return;
                }

                // Log available data for analytics
                console.log('AdminContext: Users available:', Array.isArray(users) ? users.length : 'Not an array');
                console.log('AdminContext: Delivery partners available:', Array.isArray(deliveryPartners) ? deliveryPartners.length : 'Not an array');
                console.log('AdminContext: Orders available:', Array.isArray(orders) ? orders.length : 'Not an array');
                console.log('AdminContext: Products available:', Array.isArray(products) ? products.length : 'Not an array');

                // Create basic analytics from existing data
                const basicAnalytics = {
                    sales: {
                        daily: 0,
                        weekly: 0,
                        monthly: 0,
                        yearly: 0,
                        growth: {
                            daily: 0,
                            weekly: 0,
                            monthly: 0,
                            yearly: 0
                        }
                    },
                    orders: {
                        pending: Array.isArray(orders) ? orders.filter(o => o && o.status === 'PLACED').length : 0,
                        inTransit: Array.isArray(orders) ? orders.filter(o => o && o.status === 'OUT_FOR_DELIVERY').length : 0,
                        delivered: Array.isArray(orders) ? orders.filter(o => o && o.status === 'DELIVERED').length : 0,
                        cancelled: Array.isArray(orders) ? orders.filter(o => o && o.status === 'CANCELLED').length : 0,
                        total: Array.isArray(orders) ? orders.length : 0,
                        growth: 0
                    },
                    users: {
                        total: Array.isArray(users) ? users.length : 0,
                        active: Array.isArray(users) ? users.filter(u => u && u.lastActive).length : 0,
                        new: Array.isArray(users) ? users.filter(u => {
                            if (!u || !u.createdAt) return false;
                            try {
                                const createdAt = new Date(u.createdAt);
                                const sevenDaysAgo = new Date();
                                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                                return createdAt >= sevenDaysAgo;
                            } catch (e) {
                                console.error('Error parsing date:', e);
                                return false;
                            }
                        }).length : 0,
                        growth: 0
                    },
                    deliveryPartners: {
                        total: Array.isArray(deliveryPartners) ? deliveryPartners.length : 0,
                        active: Array.isArray(deliveryPartners) ? deliveryPartners.filter(dp => dp && dp.lastActive).length : 0,
                        available: Array.isArray(deliveryPartners) ? deliveryPartners.filter(dp => dp && dp.isAvailable).length : 0,
                        growth: 0
                    },
                    products: {
                        total: Array.isArray(products) ? products.length : 0,
                        outOfStock: Array.isArray(products) ? products.filter(p => p && p.stock === 0).length : 0,
                        lowStock: Array.isArray(products) ? products.filter(p => p && p.stock > 0 && p.stock < 10).length : 0,
                        topSelling: [] // Initialize with empty array
                    },
                    revenue: {
                        daily: [],
                        weekly: [],
                        monthly: []
                    }
                };

                // Only proceed if user is an admin
                console.log('AdminContext: Fetching analytics from API for admin');
                try {
                    const response = await getDashboardAnalytics();
                    console.log('AdminContext: Analytics fetched:', response);

                    if (response && response.analytics) {
                        // Ensure all required properties exist in the response
                        const safeAnalytics = {
                            ...basicAnalytics, // Start with basic analytics as fallback
                            ...response.analytics, // Override with API response
                            // Ensure nested objects exist and have required properties
                            products: {
                                ...basicAnalytics.products,
                                ...(response.analytics.products || {}),
                                // Ensure topSelling is always an array
                                topSelling: Array.isArray(response.analytics.products?.topSelling)
                                    ? response.analytics.products.topSelling
                                    : []
                            }
                        };

                        console.log('AdminContext: Using safe analytics data');
                        setAnalytics(safeAnalytics);
                    } else {
                        console.log('AdminContext: No analytics data returned, using basic analytics');
                        setAnalytics(basicAnalytics);
                    }
                } catch (apiError) {
                    console.error('AdminContext: Error fetching analytics from API:', apiError);
                    console.log('AdminContext: Using basic analytics data');

                    // Use basic analytics if API fails
                    setAnalytics(basicAnalytics);
                }
            } catch (error) {
                console.error('Error in analytics fetch process:', error);

                // Create a safe default analytics object
                const safeDefaultAnalytics = {
                    sales: {
                        daily: 0,
                        weekly: 0,
                        monthly: 0,
                        yearly: 0,
                        growth: {
                            daily: 0,
                            weekly: 0,
                            monthly: 0,
                            yearly: 0
                        }
                    },
                    orders: {
                        pending: 0,
                        inTransit: 0,
                        delivered: 0,
                        cancelled: 0,
                        total: 0,
                        growth: 0
                    },
                    users: {
                        total: 0,
                        active: 0,
                        new: 0,
                        growth: 0
                    },
                    deliveryPartners: {
                        total: 0,
                        active: 0,
                        available: 0,
                        growth: 0
                    },
                    products: {
                        total: 0,
                        outOfStock: 0,
                        lowStock: 0,
                        topSelling: []
                    },
                    revenue: {
                        daily: [],
                        weekly: [],
                        monthly: []
                    }
                };

                // Set the safe default analytics
                setAnalytics(safeDefaultAnalytics);
                console.log('Set safe default analytics due to error');
            } finally {
                setAnalyticsLoading(false);
            }
        };

        fetchAnalytics();
    }, [orders, products, users, deliveryPartners]);

    // Set up socket listeners for real-time updates
    useEffect(() => {
        if (!socketConnected) return;

        console.log('Setting up admin socket listeners');

        // Listen for new orders
        const unsubscribeOrderUpdate = onOrderUpdate((data) => {
            console.log('Admin received order update:', data);

            if (data.type === 'new' && data.order) {
                // Add new order to the list
                setOrders(prevOrders => {
                    const exists = prevOrders.find(order =>
                        (order.id === data.order._id || order._id === data.order._id)
                    );
                    if (!exists) {
                        return [data.order, ...prevOrders];
                    }
                    return prevOrders;
                });

                // Update analytics
                updateLocalAnalytics();
            }
        });

        // Listen for order status updates
        const unsubscribeStatusUpdate = onOrderStatusUpdate((data) => {
            console.log('Admin received order status update:', data);

            if (data.orderId && data.status) {
                // Update order status in local state
                setOrders(prevOrders =>
                    prevOrders.map(order => {
                        if (order.id === data.orderId || order._id === data.orderId) {
                            return {
                                ...order,
                                status: data.status,
                                updatedAt: data.timestamp,
                                ...(data.deliveryPartnerId && { deliveryPartnerId: data.deliveryPartnerId }),
                                ...(data.status === 'DELIVERED' && { deliveredAt: data.timestamp })
                            };
                        }
                        return order;
                    })
                );

                // Update analytics
                updateLocalAnalytics();
            }
        });

        // Cleanup listeners
        return () => {
            unsubscribeOrderUpdate();
            unsubscribeStatusUpdate();
        };
    }, [socketConnected, onOrderUpdate, onOrderStatusUpdate]);

    // Function to update order status
    const handleUpdateOrderStatus = async (orderId, status, deliveryPartnerId = null) => {
        try {
            // Call API to update order status
            const response = await updateOrderStatus(orderId, status, deliveryPartnerId);
            console.log('Order status updated:', response);

            if (response && response.order) {
                // Update in the list with the response from the API
                setOrders(prevOrders =>
                    prevOrders.map(order =>
                        order.id === orderId || order._id === orderId
                            ? response.order
                            : order
                    )
                );

                // Update analytics
                updateLocalAnalytics();

                // Emit order status update via socket for real-time updates
                if (socketConnected && emitOrderStatusUpdate) {
                    emitOrderStatusUpdate({
                        orderId: orderId,
                        status: status,
                        deliveryPartnerId: deliveryPartnerId,
                        timestamp: new Date().toISOString()
                    }, (response) => {
                        if (response && response.success) {
                            console.log('Order status update successfully broadcasted via socket');
                        } else {
                            console.warn('Failed to broadcast order status update via socket:', response?.error);
                        }
                    });
                }

                return response.order;
            } else {
                // Fallback to local update if API doesn't return the updated order
                setOrders(prevOrders =>
                    prevOrders.map(order => {
                        if (order.id === orderId || order._id === orderId) {
                            const updatedOrder = {
                                ...order,
                                status,
                                ...(deliveryPartnerId && { deliveryPartnerId }),
                                ...(status === 'DELIVERED' && { deliveredAt: new Date().toISOString() })
                            };
                            return updatedOrder;
                        }
                        return order;
                    })
                );

                // Update analytics
                updateLocalAnalytics();
            }
        } catch (error) {
            console.error(`Error updating order ${orderId} status:`, error);
            throw error;
        }
    };

    // Function to update product
    const handleUpdateProduct = async (productId, updatedData) => {
        try {
            // Call API to update product
            const response = await updateProduct(productId, updatedData);
            console.log('Product updated:', response);

            if (response && response.product) {
                // Update in the list with the response from the API
                setProducts(prevProducts =>
                    prevProducts.map(product =>
                        product.id === productId || product._id === productId
                            ? response.product
                            : product
                    )
                );

                // Update analytics
                updateLocalAnalytics();

                return response.product;
            } else {
                // Fallback to local update if API doesn't return the updated product
                setProducts(prevProducts =>
                    prevProducts.map(product =>
                        product.id === productId || product._id === productId
                            ? {...product, ...updatedData}
                            : product
                    )
                );

                // Update analytics
                updateLocalAnalytics();
            }
        } catch (error) {
            console.error(`Error updating product ${productId}:`, error);
            throw error;
        }
    };

    // Function to add new product
    const handleAddProduct = async (productData) => {
        try {
            // Call API to create product
            const response = await createProduct(productData);
            console.log('Product created:', response);

            if (response && response.product) {
                // Add to the list with the response from the API
                setProducts(prevProducts => [...prevProducts, response.product]);

                // Update analytics
                updateLocalAnalytics();

                return response.product.id || response.product._id;
            } else {
                // Fallback to local creation if API doesn't return the created product
                const newProduct = {
                    id: `PROD${String(products.length + 1).padStart(3, '0')}`,
                    ...productData,
                    sold: 0
                };

                setProducts(prevProducts => [...prevProducts, newProduct]);

                // Update analytics
                updateLocalAnalytics();

                return newProduct.id;
            }
        } catch (error) {
            console.error('Error adding product:', error);
            throw error;
        }
    };

    // Function to update analytics locally
    const updateLocalAnalytics = () => {
        // In a real app, this would call the API to get updated analytics
        // For now, we'll just update a few values based on local data

        setAnalytics(prev => ({
            ...prev,
            orders: {
                ...prev.orders,
                pending: Array.isArray(orders) ? orders.filter(o => o && o.status === 'pending').length : 0,
                inTransit: Array.isArray(orders) ? orders.filter(o => o && o.status === 'in-transit').length : 0,
                delivered: Array.isArray(orders) ? orders.filter(o => o && o.status === 'delivered').length : 0,
                cancelled: Array.isArray(orders) ? orders.filter(o => o && o.status === 'cancelled').length : 0,
                total: Array.isArray(orders) ? orders.length : 0
            },
            products: {
                ...prev.products,
                total: Array.isArray(products) ? products.length : 0,
                outOfStock: Array.isArray(products) ? products.filter(p => p && p.stock === 0).length : 0,
                lowStock: Array.isArray(products) ? products.filter(p => p && p.stock > 0 && p.stock < 10).length : 0,
                // Preserve the topSelling array or initialize it if it doesn't exist
                topSelling: Array.isArray(prev.products?.topSelling) ? prev.products.topSelling : []
            }
        }));
    };

    // Function to get order details by ID
    const getOrderById = (orderId) => {
        return orders.find(order => order.id === orderId || order._id === orderId);
    };

    // Function to get product details by ID
    const getProductById = (productId) => {
        return products.find(product => product.id === productId || product._id === productId);
    };

    // Function to get user details by ID
    const getUserById = (userId) => {
        return users.find(user => user.id === userId || user._id === userId);
    };

    // Function to get delivery partner details by ID
    const getDeliveryPartnerById = (partnerId) => {
        return deliveryPartners.find(partner => partner.id === partnerId || partner._id === partnerId);
    };

    // Function to get orders by user ID with improved handling of nested objects
    const getOrdersByUserId = (userId) => {
        if (!userId) return [];

        return orders.filter(order => {
            // Direct match on userId or user field
            if (order.userId === userId || order.user === userId) {
                return true;
            }

            // Check for nested userId object
            if (order.userId && typeof order.userId === 'object') {
                return order.userId.id === userId || order.userId._id === userId;
            }

            // Check for nested user object
            if (order.user && typeof order.user === 'object') {
                return order.user.id === userId || order.user._id === userId;
            }

            return false;
        });
    };

    // Function to get orders by delivery partner ID with improved handling of nested objects
    // Now supports optional status filtering
    const getOrdersByDeliveryPartnerId = (partnerId, status = null) => {
        if (!partnerId) return [];

        console.log(`Filtering orders for delivery partner ${partnerId}${status ? ` with status: ${status}` : ''}`);
        console.log(`Total orders available: ${orders.length}`);

        // Log the first few orders to help with debugging
        if (orders.length > 0) {
            console.log('Sample order structure:', JSON.stringify(orders[0]).substring(0, 200) + '...');
        }

        // Create a normalized partner ID for comparison
        const normalizedPartnerId = partnerId.toString().trim();

        // Special handling for custom ID format (e.g., DP002)
        // If we have a custom ID format like DP002, we need to handle it specially
        const isCustomIdFormat = typeof normalizedPartnerId === 'string' && normalizedPartnerId.startsWith('DP');

        // If we have a custom ID format, try to find the MongoDB ObjectId for this partner
        let mongoDbId = null;
        if (isCustomIdFormat && deliveryPartners && deliveryPartners.length > 0) {
            console.log('Looking for MongoDB ID for custom ID:', partnerId);
            const partner = deliveryPartners.find(p => p.id === partnerId);
            if (partner) {
                mongoDbId = partner._id.toString();
                console.log(`Found MongoDB ID ${mongoDbId} for custom ID ${partnerId}`);
            }
        }

        // If we have all orders, we can filter them directly
        // This is more efficient than making additional API calls
        let filteredOrders = orders.filter(order => {
            if (!order) return false;

            try {
                // Direct match on deliveryPartnerId or deliveryPartner field
                if (order.deliveryPartnerId === partnerId ||
                    order.deliveryPartner === partnerId ||
                    (order.deliveryPartnerId && order.deliveryPartnerId.toString() === normalizedPartnerId) ||
                    (order.deliveryPartner && order.deliveryPartner.toString() === normalizedPartnerId)) {
                    return true;
                }

                // Check for nested deliveryPartnerId object
                if (order.deliveryPartnerId && typeof order.deliveryPartnerId === 'object') {
                    if (order.deliveryPartnerId.id === partnerId ||
                        order.deliveryPartnerId._id === partnerId ||
                        (order.deliveryPartnerId.id && order.deliveryPartnerId.id.toString() === normalizedPartnerId) ||
                        (order.deliveryPartnerId._id && order.deliveryPartnerId._id.toString() === normalizedPartnerId)) {
                        return true;
                    }
                }

                // Check for nested deliveryPartner object
                if (order.deliveryPartner && typeof order.deliveryPartner === 'object') {
                    if (order.deliveryPartner.id === partnerId ||
                        order.deliveryPartner._id === partnerId ||
                        (order.deliveryPartner.id && order.deliveryPartner.id.toString() === normalizedPartnerId) ||
                        (order.deliveryPartner._id && order.deliveryPartner._id.toString() === normalizedPartnerId)) {
                        return true;
                    }

                    // Special handling for custom ID format (e.g., DP002)
                    if (isCustomIdFormat && order.deliveryPartner.id === normalizedPartnerId) {
                        console.log(`Found match for custom ID ${partnerId} in order ${order._id}`);
                        return true;
                    }

                    // Check with MongoDB ID if we have one
                    if (mongoDbId && order.deliveryPartner._id &&
                        order.deliveryPartner._id.toString() === mongoDbId) {
                        console.log(`Found match using MongoDB ID ${mongoDbId} in order ${order._id}`);
                        return true;
                    }
                }

                // Check for deliveryPartnerInfo field
                if (order.deliveryPartnerInfo) {
                    if (typeof order.deliveryPartnerInfo === 'string') {
                        return order.deliveryPartnerInfo === partnerId ||
                               order.deliveryPartnerInfo.toString() === normalizedPartnerId;
                    }
                    if (typeof order.deliveryPartnerInfo === 'object') {
                        return order.deliveryPartnerInfo.id === partnerId ||
                               order.deliveryPartnerInfo._id === partnerId ||
                               order.deliveryPartnerInfo.partnerId === partnerId ||
                               (order.deliveryPartnerInfo.id && order.deliveryPartnerInfo.id.toString() === normalizedPartnerId) ||
                               (order.deliveryPartnerInfo._id && order.deliveryPartnerInfo._id.toString() === normalizedPartnerId) ||
                               (order.deliveryPartnerInfo.partnerId && order.deliveryPartnerInfo.partnerId.toString() === normalizedPartnerId);
                    }
                }

                // Check for deliveryPartnerID field (different casing)
                if (order.deliveryPartnerID === partnerId ||
                    (order.deliveryPartnerID && order.deliveryPartnerID.toString() === normalizedPartnerId)) {
                    return true;
                }

                // Check for delivery_partner_id field (snake case)
                if (order.delivery_partner_id === partnerId ||
                    (order.delivery_partner_id && order.delivery_partner_id.toString() === normalizedPartnerId)) {
                    return true;
                }

                // Check for assignedTo field
                if (order.assignedTo === partnerId ||
                    (order.assignedTo && order.assignedTo.toString() === normalizedPartnerId)) {
                    return true;
                }

                // Check for assignedDeliveryPartner field
                if (order.assignedDeliveryPartner === partnerId ||
                    (order.assignedDeliveryPartner && order.assignedDeliveryPartner.toString() === normalizedPartnerId)) {
                    return true;
                }

                // Special handling for custom ID format (e.g., DP002)
                // This is needed because MongoDB ObjectIds and custom IDs like DP002 are different
                if (isCustomIdFormat) {
                    // Check all possible fields that might contain the delivery partner ID
                    const fieldsToCheck = [
                        'deliveryPartnerId', 'deliveryPartner', 'deliveryPartnerID',
                        'delivery_partner_id', 'assignedTo', 'assignedDeliveryPartner'
                    ];

                    for (const field of fieldsToCheck) {
                        if (order[field] && order[field].toString() === normalizedPartnerId) {
                            return true;
                        }

                        // Check if the field is an object with an id property
                        if (order[field] && typeof order[field] === 'object' &&
                            order[field].id && order[field].id.toString() === normalizedPartnerId) {
                            return true;
                        }
                    }
                }

                return false;
            } catch (error) {
                console.error('Error filtering order:', error);
                return false;
            }
        });

        // Apply status filter if provided
        if (status) {
            console.log(`Applying additional status filter: ${status}`);

            // Handle different status formats (uppercase, lowercase, etc.)
            const normalizedStatus = status.toUpperCase();

            // Map frontend status names to backend status names if needed
            const statusMap = {
                'PENDING': 'PLACED',
                'IN-TRANSIT': 'OUT_FOR_DELIVERY',
                'OUT-FOR-DELIVERY': 'OUT_FOR_DELIVERY',
                'DELIVERED': 'DELIVERED',
                'CANCELLED': 'CANCELLED'
            };

            // Get the actual status to filter by
            const filterStatus = statusMap[normalizedStatus] || normalizedStatus;

            filteredOrders = filteredOrders.filter(order => {
                // Handle different status field formats
                const orderStatus = order.status ?
                    (typeof order.status === 'string' ? order.status.toUpperCase() : order.status) :
                    null;

                return orderStatus === filterStatus;
            });

            console.log(`After status filtering: ${filteredOrders.length} orders match status ${filterStatus}`);
        }

        console.log(`Found ${filteredOrders.length} orders for delivery partner ${partnerId}${status ? ` with status: ${status}` : ''}`);
        return filteredOrders;
    };

    // Function to get orders by status
    const getOrdersByStatus = (status) => {
        return orders.filter(order => order.status === status);
    };

    // Value to be provided to consumers
    const value = {
        orders,
        products,
        analytics,
        updateOrderStatus: handleUpdateOrderStatus,
        updateProduct: handleUpdateProduct,
        addProduct: handleAddProduct,
        getOrderById,
        getProductById,
        getUserById,
        getDeliveryPartnerById,
        getOrdersByUserId,
        getOrdersByDeliveryPartnerId,
        getOrdersByStatus,
        loading: {
            orders: ordersLoading,
            products: productsLoading,
            analytics: analyticsLoading
        }
    };

    return (
        <AdminContext.Provider value={value}>
            {children}
        </AdminContext.Provider>
    );
};

// Custom hook to use the admin context
export const useAdmin = () => {
    const context = useContext(AdminContext);
    if (context === undefined) {
        throw new Error('useAdmin must be used within an AdminProvider');
    }
    return context;
};
