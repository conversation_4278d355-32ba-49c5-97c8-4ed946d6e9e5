/**
 * Test script for push notifications
 * Usage: node scripts/testPushNotification.js <pushToken> <otp>
 */

const { sendOtpPushNotification } = require('../utils/pushNotificationUtils');

async function testPushNotification() {
    const args = process.argv.slice(2);
    
    if (args.length < 2) {
        console.log('Usage: node scripts/testPushNotification.js <pushToken> <otp>');
        console.log('Example: node scripts/testPushNotification.js ExponentPushToken[xxxxxx] 123456');
        process.exit(1);
    }

    const [pushToken, otp] = args;
    const testPhoneNumber = '1234567890';

    console.log('Testing push notification...');
    console.log('Push Token:', pushToken);
    console.log('OTP:', otp);
    console.log('Phone Number:', testPhoneNumber);
    console.log('---');

    try {
        const result = await sendOtpPushNotification(pushToken, otp, testPhoneNumber);
        
        if (result) {
            console.log('✅ Push notification sent successfully!');
        } else {
            console.log('❌ Failed to send push notification');
        }
    } catch (error) {
        console.error('❌ Error testing push notification:', error.message);
    }
}

// Run the test
testPushNotification();
