import React, { createContext, useState, useContext, useEffect } from 'react';
import { getUserOrders, getUserOrderById, createUserOrder } from '../utils/api/userOrderApi';
import { getAuthToken } from '../utils/authStorage';
import { API_URL } from '../config/constants';
import { useUser } from './UserContext';
import { useSocket } from './SocketContext';

// Create the context
const OrderContext = createContext();

// Custom hook to use the order context
export const useOrders = () => {
    const context = useContext(OrderContext);
    if (context === undefined) {
        throw new Error('useOrders must be used within an OrderProvider');
    }
    return context;
};

// Create a provider component
export const OrderProvider = ({ children }) => {
    // User orders
    const [orders, setOrders] = useState([]);
    // Current order being viewed
    const [currentOrder, setCurrentOrder] = useState(null);
    // Loading state
    const [loading, setLoading] = useState(true);

    // Get refreshUserData from UserContext
    const { refreshUserData } = useUser();

    // Get socket functions
    const {
        connected: socketConnected,
        emitNewOrder,
        onOrderUpdate,
        onOrderStatusUpdate
    } = useSocket();

    // Fetch user orders from API on component mount
    useEffect(() => {
        fetchUserOrders();
    }, []);

    // Set up socket listeners for real-time order updates
    useEffect(() => {
        if (!socketConnected) return;

        console.log('Setting up socket listeners for order updates');

        // Listen for order updates
        const unsubscribeOrderUpdate = onOrderUpdate((data) => {
            console.log('Received order update:', data);

            if (data.type === 'new' && data.order) {
                // Add new order to the list if it belongs to current user
                setOrders(prevOrders => {
                    const exists = prevOrders.find(order => order._id === data.order._id);
                    if (!exists) {
                        return [data.order, ...prevOrders];
                    }
                    return prevOrders;
                });
            }
        });

        // Listen for order status updates
        const unsubscribeStatusUpdate = onOrderStatusUpdate((data) => {
            console.log('Received order status update:', data);

            if (data.orderId && data.status) {
                // Update order status in local state
                setOrders(prevOrders =>
                    prevOrders.map(order =>
                        order._id === data.orderId
                            ? { ...order, status: data.status, updatedAt: data.timestamp }
                            : order
                    )
                );

                // Update current order if it matches
                if (currentOrder && currentOrder._id === data.orderId) {
                    setCurrentOrder(prev => ({
                        ...prev,
                        status: data.status,
                        updatedAt: data.timestamp
                    }));
                }
            }
        });

        // Cleanup listeners on unmount or when socket disconnects
        return () => {
            unsubscribeOrderUpdate();
            unsubscribeStatusUpdate();
        };
    }, [socketConnected, currentOrder]);

    // Function to fetch user orders
    const fetchUserOrders = async () => {
        try {
            setLoading(true);
            console.log('Fetching user orders');

            const response = await getUserOrders();
            console.log('User orders fetched:', response);

            if (Array.isArray(response)) {
                setOrders(response);
            } else {
                console.log('No orders returned from API or invalid format');
                setOrders([]);
            }
        } catch (error) {
            console.error('Error fetching user orders:', error);
            setOrders([]);
        } finally {
            setLoading(false);
        }
    };

    // Function to get order by ID
    const getOrderById = async (orderId) => {
        try {
            console.log('Fetching order details:', orderId);

            const response = await getUserOrderById(orderId);
            console.log('Order details fetched:', response);

            if (response && response.order) {
                setCurrentOrder(response.order);
                return response.order;
            } else {
                // Try to find the order in the local state
                const localOrder = orders.find(order => order._id === orderId);
                if (localOrder) {
                    setCurrentOrder(localOrder);
                    return localOrder;
                }

                console.log('Order not found');
                return null;
            }
        } catch (error) {
            console.error(`Error fetching order ${orderId}:`, error);

            // Try to find the order in the local state as fallback
            const localOrder = orders.find(order => order._id === orderId);
            if (localOrder) {
                setCurrentOrder(localOrder);
                return localOrder;
            }

            return null;
        }
    };

    // Function to create a new order
    const addOrder = async (orderData) => {
        try {
            setLoading(true);
            console.log('Creating new order:', orderData);
            console.log('Order amount before API call:', orderData.totalAmount);

            const response = await createUserOrder(orderData);
            console.log('Order created:', response);
            console.log('Order amount after API call:',
                response.order?.totalAmount || response.order?.total ||
                response.totalAmount || response.total);

            // The response could be the order directly or nested in an order property
            const orderResponse = response.order || response;

            if (orderResponse) {
                // Log any amount discrepancy
                const sentAmount = orderData.totalAmount;
                const receivedAmount = orderResponse.totalAmount || orderResponse.total;
                if (sentAmount !== receivedAmount) {
                    console.warn('Amount discrepancy detected:', {
                        sent: sentAmount,
                        received: receivedAmount,
                        difference: receivedAmount - sentAmount
                    });
                }

                // Add the new order to the orders list
                setOrders(prevOrders => [orderResponse, ...prevOrders]);
                setCurrentOrder(orderResponse);

                // Emit new order event via socket for real-time updates
                if (socketConnected && emitNewOrder) {
                    emitNewOrder(orderResponse, (response) => {
                        if (response && response.success) {
                            console.log('Order successfully broadcasted via socket');
                        } else {
                            console.warn('Failed to broadcast order via socket:', response?.error);
                        }
                    });
                }

                return orderResponse;
            } else {
                console.log('Failed to create order');
                return null;
            }
        } catch (error) {
            console.error('Error creating order:', error);
            throw error;
        } finally {
            setLoading(false);
        }
    };

    // Function to cancel an order
    const cancelOrder = async (orderId) => {
        try {
            console.log('Cancelling order:', orderId);

            // Call the API to update the order status to CANCELLED
            const token = await getAuthToken();
            const response = await fetch(`${API_URL}/orders/${orderId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ status: 'CANCELLED' })
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Error response from server:', errorData);
                throw new Error(errorData.message || 'Failed to cancel order');
            }

            // Update local state
            setOrders(prevOrders =>
                prevOrders.map(order =>
                    order._id === orderId
                        ? { ...order, status: 'CANCELLED' }
                        : order
                )
            );

            if (currentOrder && currentOrder._id === orderId) {
                setCurrentOrder({ ...currentOrder, status: 'CANCELLED' });
            }

            // Refresh user data to update coin count
            try {
                if (refreshUserData) {
                    await refreshUserData();
                }
            } catch (refreshError) {
                console.error('Error refreshing user data after order cancellation:', refreshError);
                // Continue even if refresh fails
            }

            return true;
        } catch (error) {
            console.error(`Error cancelling order ${orderId}:`, error);
            return false;
        }
    };

    // Function to update order status
    const updateOrderStatus = async (orderId, newStatus) => {
        try {
            // In a real implementation, this would call an API endpoint
            // For now, we'll just update locally
            console.log('Updating order status:', orderId, newStatus);

            setOrders(prevOrders =>
                prevOrders.map(order =>
                    order._id === orderId
                        ? { ...order, status: newStatus }
                        : order
                )
            );

            if (currentOrder && currentOrder._id === orderId) {
                setCurrentOrder({ ...currentOrder, status: newStatus });
            }

            return true;
        } catch (error) {
            console.error(`Error updating order ${orderId} status:`, error);
            return false;
        }
    };

    // Function to check if an order can be cancelled
    const canCancelOrder = (status) => {
        return status !== 'CANCELLED' && status !== 'DELIVERED' && status !== 'OUT_FOR_DELIVERY';
    };

    // Function to calculate coins earned from an order
    const calculateCoinsEarned = (totalAmount) => {
        // Award 10 coins for every 100 Rs spent
        return Math.floor(totalAmount / 100) * 10;
    };

    // Value to be provided to consumers
    const value = {
        orders,
        setOrders,
        currentOrder,
        loading,
        fetchUserOrders,
        getOrderById,
        addOrder,
        cancelOrder,
        updateOrderStatus,
        canCancelOrder,
        calculateCoinsEarned
    };

    return (
        <OrderContext.Provider value={value}>
            {children}
        </OrderContext.Provider>
    );
};