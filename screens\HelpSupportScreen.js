import React, { useState } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    Alert,
    StatusBar,
    Linking
} from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from '@react-navigation/native';
import { useUser } from '../context/UserContext';

const HelpSupportScreen = () => {
    const navigation = useNavigation();
    const { currentUser } = useUser();

    const [activeSection, setActiveSection] = useState(null);

    const faqData = [
        {
            id: 1,
            question: 'How do I track my order?',
            answer: 'You can track your order by going to the "My Orders" section in your profile. There you will see all your current and past orders. Click on any current order to see its real-time status.'
        },
        {
            id: 2,
            question: 'What payment methods do you accept?',
            answer: 'We accept UPI and Cash on Delivery (COD). You can select your preferred payment method during checkout. For COD orders, you can also pay via UPI to the delivery partner at the time of delivery.'
        },
        {
            id: 3,
            question: 'How can I cancel my order?',
            answer: 'You can cancel your order from the "My Orders" section if it has not yet been prepared. Simply select the order you wish to cancel and click on the "Cancel Order" button. Please note that orders that are already being prepared or out for delivery cannot be cancelled.'
        },
        {
            id: 4,
            question: 'What is your refund policy?',
            answer: 'Refunds for cancelled orders are processed within 5-7 business days. The amount will be credited back to the original payment method. For any issues with your order, please contact our customer support.'
        },
        {
            id: 5,
            question: 'How do I add or change my delivery address?',
            answer: 'You can manage your delivery addresses in the "My Addresses" section of your profile. You can add new addresses, edit existing ones, or delete addresses you no longer use.'
        },
        {
            id: 6,
            question: 'What are Meat Now coins and how do I use them?',
            answer: 'Meat Now coins are reward points you earn on purchases. 1 coin = ₹1 that you can use for future purchases. You can view your coin balance in the "My FreshCoins" section and apply them during checkout.'
        }
    ];

    const handleCall = () => {
        const phoneNumber = '+918825549901';
        Alert.alert(
            "Call Customer Support",
            `Would you like to call our customer support?\n\n📞 ${phoneNumber}\n\nAvailable: 9 AM - 9 PM (Mon-Sat)`,
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Call Now",
                    onPress: () => {
                        Linking.openURL(`tel:${phoneNumber}`).catch(err => {
                            Alert.alert("Error", "Unable to make phone call. Please try again.");
                        });
                    }
                }
            ]
        );
    };

    const handleEmail = () => {
        const email = '<EMAIL>';
        Alert.alert(
            "Email Customer Support",
            `Would you like to send an email to our support team?\n\n📧 ${email}\n\nWe respond within 24 hours`,
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Send Email",
                    onPress: () => {
                        Linking.openURL(`mailto:${email}?subject=Support Request - Meat Now App`).catch(err => {
                            Alert.alert("Error", "Unable to open email app. Please try again.");
                        });
                    }
                }
            ]
        );
    };

    const contactOptions = [
        {
            id: 'phone',
            title: 'Call Us',
            subtitle: 'Available 9 AM - 9 PM (Mon-Sat)',
            icon: 'phone',
            action: handleCall
        },
        {
            id: 'email',
            title: 'Email Us',
            subtitle: 'We respond within 24 hours',
            icon: 'email',
            action: handleEmail
        }
    ];

    const toggleSection = (id) => {
        setActiveSection(activeSection === id ? null : id);
    };

    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" />

            {/* Header with radius */}
            <View className="bg-madder pt-12 pb-6 px-4 rounded-b-3xl">
                <View className="flex-row items-center">
                    <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4 p-2 -ml-2">
                        <MaterialIcons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">Help & Support</Text>
                </View>
            </View>

            <ScrollView className="p-4">
                {/* Contact Options */}
                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                    <Text className="text-lg font-bold mb-3">Contact Us</Text>

                    {contactOptions.map((option) => (
                        <TouchableOpacity
                            key={option.id}
                            className="flex-row items-center py-3 border-b border-gray-100"
                            onPress={option.action}
                        >
                            <View className="w-10 h-10 rounded-full bg-madder/10 items-center justify-center mr-3">
                                <MaterialIcons name={option.icon} size={20} color="#A31621" />
                            </View>
                            <View className="flex-1">
                                <Text className="text-gray-800 font-medium">{option.title}</Text>
                                <Text className="text-gray-500 text-xs">{option.subtitle}</Text>
                            </View>
                            <MaterialIcons name="chevron-right" size={20} color="#A31621" />
                        </TouchableOpacity>
                    ))}
                </View>

                {/* FAQs */}
                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                    <Text className="text-lg font-bold mb-3">Frequently Asked Questions</Text>

                    {faqData.map((faq) => (
                        <View key={faq.id} className="mb-2 border-b border-gray-100 pb-2">
                            <TouchableOpacity
                                className="flex-row justify-between items-center py-2"
                                onPress={() => toggleSection(faq.id)}
                            >
                                <Text className="text-gray-800 font-medium flex-1 pr-2">{faq.question}</Text>
                                <MaterialIcons
                                    name={activeSection === faq.id ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                                    size={24}
                                    color="#A31621"
                                />
                            </TouchableOpacity>

                            {activeSection === faq.id && (
                                <View className="py-2 px-1">
                                    <Text className="text-gray-600">{faq.answer}</Text>
                                </View>
                            )}
                        </View>
                    ))}
                </View>
            </ScrollView>
        </View>
    );
};

export default HelpSupportScreen;
