// API configuration - Production Ready
import { Platform } from 'react-native';

// Production backend URL
const API_BASE_URL = 'https://meatshop-v2-backend-v2.onrender.com/api';

// Production API configuration
console.log('🚀 PRODUCTION MODE: Backend API configured');
console.log('📡 API URL:', API_BASE_URL);

export const API_URL = API_BASE_URL;

// Socket.io URL (remove /api suffix from API_URL)
export const SOCKET_URL = API_BASE_URL.replace('/api', '');
console.log('🚀 PRODUCTION: Socket URL configured:', SOCKET_URL);

// Google Maps API Key
// This is a placeholder - replace with your actual API key
export const GOOGLE_MAPS_API_KEY = 'AIzaSyC4IwgWpiexsb328mgLejrbnPCyhrwVGbs';

// Authentication constants
export const AUTH_TOKEN_KEY = 'auth_token';
export const REFRESH_TOKEN_KEY = 'refresh_token';
export const USER_DATA_KEY = 'user_data';

// User types
export const USER_TYPES = {
    USER: 'USER',
    ADMIN: 'ADMIN',
    DELIVERY_PARTNER: 'DELIVERY_PARTNER'
};
