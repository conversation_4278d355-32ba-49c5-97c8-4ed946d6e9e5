/**
 * Utility functions for sending push notifications via Expo
 */

const axios = require('axios');

/**
 * Send OTP via Expo Push Notification
 * @param {string} expoPushToken - Expo push token
 * @param {string} otp - OTP code
 * @param {string} phoneNumber - User's phone number (for logging)
 * @returns {Promise<boolean>} - Success status
 */
const sendOtpPushNotification = async (expoPushToken, otp, phoneNumber) => {
    try {
        if (!expoPushToken) {
            console.log(`No push token available for ${phoneNumber}, skipping push notification`);
            return false;
        }

        // Validate Expo push token format
        if (!expoPushToken.startsWith('ExponentPushToken[') && !expoPushToken.startsWith('ExpoPushToken[')) {
            console.error(`Invalid Expo push token format for ${phoneNumber}:`, expoPushToken);
            return false;
        }

        const message = {
            to: expoPushToken,
            sound: 'default',
            title: 'Meat Noww OTP Verification',
            body: `Your OTP is ${otp}. Valid for 10 minutes.`,
            data: {
                otp: otp,
                type: 'otp_verification',
                phoneNumber: phoneNumber
            },
            priority: 'high',
            channelId: 'default',
        };

        console.log(`Sending OTP push notification to ${phoneNumber}`);

        const response = await axios.post('https://exp.host/--/api/v2/push/send', message, {
            headers: {
                Accept: 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
            },
            timeout: 10000, // 10 second timeout
        });

        if (response.data && response.data.data) {
            const result = response.data.data;
            if (result.status === 'ok') {
                console.log(`OTP push notification sent successfully to ${phoneNumber}`);
                return true;
            } else {
                console.error(`Failed to send OTP push notification to ${phoneNumber}:`, result);
                return false;
            }
        } else {
            console.error(`Unexpected response format for ${phoneNumber}:`, response.data);
            return false;
        }
    } catch (error) {
        console.error(`Error sending OTP push notification to ${phoneNumber}:`, error.message);

        // Log specific error details
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }

        return false;
    }
};

/**
 * Send general push notification
 * @param {string} expoPushToken - Expo push token
 * @param {string} title - Notification title
 * @param {string} body - Notification body
 * @param {Object} data - Additional data
 * @returns {Promise<boolean>} - Success status
 */
const sendPushNotification = async (expoPushToken, title, body, data = {}) => {
    try {
        if (!expoPushToken) {
            console.log('No push token available, skipping push notification');
            return false;
        }

        // Validate Expo push token format
        if (!expoPushToken.startsWith('ExponentPushToken[') && !expoPushToken.startsWith('ExpoPushToken[')) {
            console.error('Invalid Expo push token format:', expoPushToken);
            return false;
        }

        const message = {
            to: expoPushToken,
            sound: 'default',
            title: title,
            body: body,
            data: data,
            priority: 'default',
            channelId: 'default',
        };

        console.log('Sending push notification:', title);

        const response = await axios.post('https://exp.host/--/api/v2/push/send', message, {
            headers: {
                Accept: 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
            },
            timeout: 10000,
        });

        if (response.data && response.data.data) {
            const result = response.data.data;
            if (result.status === 'ok') {
                console.log('Push notification sent successfully');
                return true;
            } else {
                console.error('Failed to send push notification:', result);
                return false;
            }
        } else {
            console.error('Unexpected response format:', response.data);
            return false;
        }
    } catch (error) {
        console.error('Error sending push notification:', error.message);

        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }

        return false;
    }
};

/**
 * Validate Expo push token format
 * @param {string} token - Push token to validate
 * @returns {boolean} - Whether token is valid
 */
const isValidExpoPushToken = (token) => {
    if (!token || typeof token !== 'string') {
        return false;
    }

    return token.startsWith('ExponentPushToken[') || token.startsWith('ExpoPushToken[');
};

/**
 * Send batch push notifications
 * @param {Array} notifications - Array of notification objects
 * @returns {Promise<Array>} - Array of results
 */
const sendBatchPushNotifications = async (notifications) => {
    try {
        if (!notifications || notifications.length === 0) {
            return [];
        }

        // Filter out invalid tokens
        const validNotifications = notifications.filter(notif =>
            isValidExpoPushToken(notif.to)
        );

        if (validNotifications.length === 0) {
            console.log('No valid push tokens found in batch');
            return [];
        }

        console.log(`Sending batch of ${validNotifications.length} push notifications`);

        const response = await axios.post('https://exp.host/--/api/v2/push/send', validNotifications, {
            headers: {
                Accept: 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
            },
            timeout: 15000,
        });

        return response.data.data || [];
    } catch (error) {
        console.error('Error sending batch push notifications:', error.message);
        return [];
    }
};

/**
 * Send order status update notification
 * @param {string} expoPushToken - Expo push token
 * @param {string} orderNumber - Order number
 * @param {string} status - Order status
 * @param {string} userType - USER, ADMIN, or DELIVERY_PARTNER
 * @returns {Promise<boolean>} - Success status
 */
const sendOrderStatusNotification = async (expoPushToken, orderNumber, status, userType = 'USER') => {
    try {
        if (!expoPushToken) {
            console.log('No push token available for order status notification');
            return false;
        }

        let title, body, priority = 'default';

        // Customize notification based on status and user type
        switch (status) {
            case 'PLACED':
                if (userType === 'ADMIN') {
                    title = '🆕 New Order Received';
                    body = `New order placed! Order #${orderNumber}`;
                    priority = 'high';
                } else {
                    title = '✅ Order Confirmed';
                    body = `Your order #${orderNumber} has been placed successfully`;
                }
                break;
            case 'OUT_FOR_DELIVERY':
                if (userType === 'USER') {
                    title = '🚚 Out for Delivery';
                    body = `Your order #${orderNumber} is out for delivery`;
                    priority = 'high';
                } else if (userType === 'DELIVERY_PARTNER') {
                    title = '📦 Delivery Started';
                    body = `You started delivery for order #${orderNumber}`;
                }
                break;
            case 'DELIVERED':
                if (userType === 'USER') {
                    title = '✅ Order Delivered';
                    body = `Your order #${orderNumber} has been delivered successfully`;
                    priority = 'high';
                } else if (userType === 'DELIVERY_PARTNER') {
                    title = '✅ Delivery Completed';
                    body = `You completed delivery for order #${orderNumber}`;
                } else if (userType === 'ADMIN') {
                    title = '✅ Order Delivered';
                    body = `Order #${orderNumber} has been delivered`;
                }
                break;
            default:
                title = '📋 Order Update';
                body = `Order #${orderNumber} status: ${status}`;
        }

        const message = {
            to: expoPushToken,
            sound: 'default',
            title: title,
            body: body,
            data: {
                type: 'order_status_update',
                orderNumber: orderNumber,
                status: status,
                userType: userType,
                timestamp: new Date().toISOString()
            },
            priority: priority,
            channelId: 'orders',
        };

        console.log(`Sending order status notification to ${userType}:`, title);

        const response = await axios.post('https://exp.host/--/api/v2/push/send', message, {
            headers: {
                Accept: 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
            },
            timeout: 10000,
        });

        if (response.data && response.data.data) {
            const result = response.data.data;
            if (result.status === 'ok') {
                console.log('Order status notification sent successfully');
                return true;
            } else {
                console.error('Failed to send order status notification:', result);
                return false;
            }
        } else {
            console.error('Unexpected response format:', response.data);
            return false;
        }
    } catch (error) {
        console.error('Error sending order status notification:', error.message);
        return false;
    }
};

/**
 * Send new order notification to admin or delivery partner
 * @param {string} expoPushToken - Expo push token
 * @param {string} orderNumber - Order number
 * @param {string} customerName - Customer name
 * @param {number} totalAmount - Order total amount
 * @param {string} userType - USER_TYPE (ADMIN or DELIVERY_PARTNER)
 * @returns {Promise<boolean>} - Success status
 */
const sendNewOrderNotification = async (expoPushToken, orderNumber, customerName, totalAmount, userType = 'ADMIN') => {
    try {
        if (!expoPushToken) {
            console.log(`No push token available for new order notification (${userType})`);
            return false;
        }

        let title, body;

        if (userType === 'DELIVERY_PARTNER') {
            title = '🆕 New Delivery Available';
            body = `New order #${orderNumber} for ₹${totalAmount} is available for delivery`;
        } else {
            title = '🆕 New Order Placed';
            body = `${customerName} placed order #${orderNumber} for ₹${totalAmount}`;
        }

        const message = {
            to: expoPushToken,
            sound: 'default',
            title: title,
            body: body,
            data: {
                type: 'new_order',
                orderNumber: orderNumber,
                customerName: customerName,
                totalAmount: totalAmount,
                userType: userType,
                timestamp: new Date().toISOString()
            },
            priority: 'high',
            channelId: 'orders',
        };

        console.log(`Sending new order notification to ${userType}: Order #${orderNumber}`);

        const response = await axios.post('https://exp.host/--/api/v2/push/send', message, {
            headers: {
                Accept: 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
            },
            timeout: 10000,
        });

        if (response.data && response.data.data) {
            const result = response.data.data;
            if (result.status === 'ok') {
                console.log(`New order notification sent successfully to ${userType}`);
                return true;
            } else {
                console.error('Failed to send new order notification:', result);
                return false;
            }
        } else {
            console.error('Unexpected response format:', response.data);
            return false;
        }
    } catch (error) {
        console.error('Error sending new order notification:', error.message);
        return false;
    }
};

module.exports = {
    sendOtpPushNotification,
    sendPushNotification,
    isValidExpoPushToken,
    sendBatchPushNotifications,
    sendOrderStatusNotification,
    sendNewOrderNotification
};
