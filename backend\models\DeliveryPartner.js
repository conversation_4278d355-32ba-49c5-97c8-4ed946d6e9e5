const mongoose = require('mongoose');

const deliveryPartnerSchema = new mongoose.Schema({
    id: { type: String, unique: true, sparse: true }, // Custom ID like "DP001"
    name: { type: String, required: true },
    phoneNumber: { type: String, required: true, unique: true },
    email: { type: String, required: false, unique: true, sparse: true }, // Optional email
    // OTP-based authentication fields
    otpCode: { type: String, default: null },
    otpExpiry: { type: Date, default: null },
    refreshToken: { type: String, default: null },
    expoPushToken: { type: String, default: null },
    // Alias for expoPushToken for consistency
    pushToken: { type: String, default: null },
    otpRetryCount: { type: Number, default: 0 },
    lastOtpRequest: { type: Date, default: null },
    isActive: { type: Boolean, default: true }, // To enable/disable delivery partner account
    addedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Admin' }, // Reference to the admin who added this partner
    vehicleType: { type: String, default: 'Two Wheeler' },
    vehicleNumber: { type: String },
    joinedDate: { type: Date, default: Date.now },
    totalDeliveries: { type: Number, default: 0 },
    rating: { type: Number, default: 5.0 },
    isAvailable: { type: Boolean, default: true },
    earnings: { type: Number, default: 0 },
    completedOrders: { type: Number, default: 0 },
    cancelledOrders: { type: Number, default: 0 },
    onTimeRate: { type: Number, default: 100 },
    location: { type: String },
    lastActive: { type: Date, default: Date.now },
    orders: [
        {
            orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'Order' }, // Reference to the Order
            customerName: { type: String, required: true },
            deliveryAddress: { type: String, required: true },
            deliveryStatus: {
                type: String,
                enum: ['PLACED', 'CONFIRMED', 'PREPARING', 'OUT_FOR_DELIVERY', 'DELIVERED', 'CANCELLED'],
                default: 'PLACED'
            },
            estimatedDeliveryTime: { type: Date } // Expected delivery time
        }
    ]
}, { timestamps: true });

const DeliveryPartner = mongoose.model('DeliveryPartner', deliveryPartnerSchema);
module.exports = DeliveryPartner;
