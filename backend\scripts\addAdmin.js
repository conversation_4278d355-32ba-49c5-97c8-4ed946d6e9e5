const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Admin = require('../models/Admin');

// Load environment variables
dotenv.config();
console.log('MongoDB URI:', process.env.MONGO_URI ? 'URI is set' : 'URI is not set');

// Admin data
const adminData = {
    name: 'Admin User',
    phoneNumber: '8825549901',
    email: '<EMAIL>',
    role: 'ADMIN'
};

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
    .then(async () => {
        console.log('MongoDB Connected');
        
        try {
            // Check if admin already exists
            const existingAdmin = await Admin.findOne({ phoneNumber: adminData.phoneNumber });
            
            if (existingAdmin) {
                console.log('Admin already exists:', existingAdmin);
            } else {
                // Create new admin
                const admin = new Admin(adminData);
                const savedAdmin = await admin.save();
                console.log('Admin created:', savedAdmin);
            }
            
            // List all admins
            const admins = await Admin.find({});
            console.log('All admins:', admins);
            
            process.exit(0);
        } catch (error) {
            console.error('Error:', error);
            process.exit(1);
        }
    })
    .catch(err => {
        console.error('MongoDB connection error:', err);
        process.exit(1);
    });
