# MeatShop-V2_backend

Backend API for the MeatShop V2 application. This Node.js application provides RESTful API endpoints for the MeatShop mobile application.

## Features

- User authentication and authorization
- Product management
- Order processing
- Invoice generation
- Email notifications

## Tech Stack

- Node.js
- Express.js
- MongoDB
- JWT Authentication
- Socket.io for real-time updates

## Getting Started

### Prerequisites

- Node.js
- MongoDB Atlas account or local MongoDB installation

### Installation

1. Clone the repository
   ```
   git clone https://github.com/Venket661/MeatShop-V2_backend.git
   ```

2. Install dependencies
   ```
   npm install
   ```

3. Create a `.env` file in the root directory with the following variables:
   ```
   NODE_ENV=development
   MONGO_URI=your_mongodb_connection_string
   PORT=5000
   JWT_SECRET=your_jwt_secret
   JWT_REFRESH_SECRET=your_refresh_token_secret
   JWT_EXPIRES_IN=7d
   REFRESH_TOKEN_EXPIRES_IN=30d
   EMAIL_SERVICE=your_email_service
   EMAIL_USER=your_email
   EMAIL_PASSWORD=your_email_password
   EMAIL_FROM_NAME=your_sender_name
   EMAIL_FROM_ADDRESS=your_sender_email
   INVOICE_DIR=invoices
   INVOICE_PREFIX=INV-
   ```

4. Start the server
   ```
   npm run dev
   ```

## API Documentation

API documentation will be added soon.

## License

This project is licensed under the ISC License.
