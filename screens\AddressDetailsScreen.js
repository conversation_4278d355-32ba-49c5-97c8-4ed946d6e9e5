import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image, KeyboardAvoidingView, Platform, ScrollView, ActivityIndicator, Alert, Modal } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useUser } from '../context/UserContext';
import axios from 'axios';
import { API_URL } from '../config/constants';
import { getAuthToken } from '../utils/authStorage';
import DeliveryLocationPicker from '../Components/DeliveryLocationPicker';

const AddressDetailsScreen = ({ route, navigation }) => {
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [showLocationPicker, setShowLocationPicker] = useState(false);
    const [coordinates, setCoordinates] = useState(null);

    // These fields will be populated from the location picker but not shown as input fields
    const [doorNo, setDoorNo] = useState('');
    const [streetName, setStreetName] = useState('');
    const [area, setArea] = useState('');
    const [district, setDistrict] = useState('');
    const [pincode, setPincode] = useState('');

    const { name, email } = route.params || {};
    const { setCurrentUser } = useUser();

    const validateInputs = () => {
        // Check if coordinates are set
        if (!coordinates) {
            setError('Please select your location on the map');
            return false;
        }

        // Check if all required address fields are filled
        if (!doorNo.trim()) {
            setError('Flat/House number is required');
            return false;
        }

        if (!streetName.trim()) {
            setError('Street name is required');
            return false;
        }

        if (!area.trim()) {
            setError('Area/Locality is required');
            return false;
        }

        if (!district.trim()) {
            setError('District is required');
            return false;
        }

        if (!pincode.trim()) {
            setError('Pincode is required');
            return false;
        } else if (!/^\d{6}$/.test(pincode.trim())) {
            setError('Pincode must be 6 digits');
            return false;
        }

        return true;
    };

    const handleContinue = async () => {
        if (!validateInputs()) {
            return;
        }

        setLoading(true);

        // Combine all address parts into a full address string
        const fullAddress = `${doorNo}, ${streetName}, ${area}, ${district}, ${pincode}`;

        try {
            // Get the auth token from AsyncStorage
            const token = await getAuthToken();

            // Use the token to update the profile
            console.log('AddressDetailsScreen: Updating profile with email:', email);
            const response = await axios.post(
                `${API_URL}/auth/update-profile`,
                {
                    name: name.trim(),
                    email: email ? email.trim() : undefined,
                    address: {
                        doorNo: doorNo.trim(),
                        streetName: streetName.trim(),
                        area: area.trim(),
                        district: district.trim(),
                        pincode: pincode.trim(),
                        fullAddress: fullAddress,
                        // Include coordinates in both formats for compatibility
                        coordinates: {
                            latitude: coordinates?.latitude || null,
                            longitude: coordinates?.longitude || null
                        },
                        latitude: coordinates?.latitude || null,
                        longitude: coordinates?.longitude || null,
                        addressType: 'Home', // Default to Home
                        isPrimary: true, // Set as primary address
                        isWithinDeliveryZone: true // Assume it's within delivery zone
                    }
                },
                {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                }
            );
            console.log('AddressDetailsScreen: Update profile response:', response.data);

            // Set the current user with the updated data
            setCurrentUser(response.data.user);

            // Navigate to the main app
            navigation.reset({
                index: 0,
                routes: [{ name: 'MainTabs' }],
            });
        } catch (error) {
            console.error('Error updating profile:', error);
            let errorMessage = 'Failed to update profile. Please try again.';

            if (error.response && error.response.data && error.response.data.message) {
                errorMessage = error.response.data.message;
            }

            Alert.alert(
                "Error",
                errorMessage,
                [{ text: "OK" }]
            );
        } finally {
            setLoading(false);
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            className="flex-1 bg-white"
        >
            {/* Unique asymmetric design elements */}
            <View className="absolute top-0 right-0 w-[70%] h-[220px] overflow-hidden">
                <View className="absolute w-[300px] h-[300px] rounded-full bg-madder opacity-[0.08] top-[-100px] right-[-50px]" />
            </View>

            <View className="absolute bottom-0 left-0 w-[50%] h-[200px] overflow-hidden">
                <View className="absolute w-[250px] h-[250px] rounded-full bg-madder opacity-[0.05] bottom-[-100px] left-[-50px]" />
            </View>

            <ScrollView
                className="flex-1"
                contentContainerClassName="px-6 pt-12 pb-6"
                keyboardShouldPersistTaps="handled"
            >
                {/* Logo with unique presentation */}
                <View className="items-center mt-16 mb-10">
                    <View className="w-[80px] h-[80px] rounded-xl bg-white items-center justify-center shadow-md"
                        style={{
                            transform: [{ rotate: '-5deg' }],
                            elevation: 4,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.1,
                            shadowRadius: 4,
                        }}>
                        <Image
                            source={require('../assets/logo.png')}
                            className="w-[60px] h-[60px]"
                            resizeMode="contain"
                        />
                    </View>
                </View>

                {/* Header text */}
                <View className="mb-8">
                    <Text className="text-2xl font-bold text-gray-800 text-center mb-2">
                        Where to Deliver?
                    </Text>
                    <Text className="text-sm text-gray-600 text-center">
                        Select your location on the map
                    </Text>
                </View>

                {/* Location Selection Section */}
                <View className="space-y-4 mb-10 mx-6">
                    {error ? (
                        <Text className="text-red-500 text-sm text-center mb-2">{error}</Text>
                    ) : null}

                    {/* Map Location Picker Button */}
                    <View>
                        <Text className="text-base font-medium text-gray-700 mb-2 ml-1">Select Your Delivery Location</Text>
                        <TouchableOpacity
                            className="border border-madder rounded-xl px-4 py-4 bg-white shadow-sm flex-row items-center justify-center"
                            style={{
                                elevation: 2,
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 2 },
                                shadowOpacity: 0.1,
                                shadowRadius: 3,
                            }}
                            onPress={() => setShowLocationPicker(true)}
                        >
                            <MaterialIcons name="location-on" size={24} color="#A31621" />
                            <Text className="text-madder text-base font-medium ml-2">
                                {coordinates ? "Change Location on Map" : "Set Location on Map"}
                            </Text>
                        </TouchableOpacity>
                    </View>

                    {/* Selected Address Display */}
                    {coordinates && (
                        <View className="mt-4 bg-gray-50 p-4 rounded-xl border border-gray-200">
                            <Text className="text-base font-medium text-gray-800 mb-2">Your order will be delivered here:</Text>

                            {doorNo && (
                                <Text className="text-gray-700 mb-1">
                                    {doorNo}
                                </Text>
                            )}

                            {streetName && (
                                <Text className="text-gray-700 mb-1">
                                    {streetName}
                                </Text>
                            )}

                            {area && (
                                <Text className="text-gray-700 mb-1">
                                    {area}
                                </Text>
                            )}

                            {district && (
                                <Text className="text-gray-700 mb-1">
                                    {district}
                                </Text>
                            )}

                            {pincode && (
                                <Text className="text-gray-700 mb-1">
                                    Pincode: {pincode}
                                </Text>
                            )}
                        </View>
                    )}
                </View>

                {/* Continue Button */}
                <View className="items-center justify-center mb-6">
                    <TouchableOpacity
                        className="bg-madder py-4 px-8 rounded-lg w-[65%] items-center shadow-md"
                        style={{
                            elevation: 3,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.1,
                            shadowRadius: 3,
                        }}
                        onPress={handleContinue}
                        disabled={loading}
                    >
                        {loading ? (
                            <ActivityIndicator size="small" color="#FFFFFF" />
                        ) : (
                            <View className="flex-row items-center">
                                <Text className="text-white text-base font-semibold tracking-wide">Start Shopping</Text>
                                <Text className="text-white text-lg ml-2">🛒</Text>
                            </View>
                        )}
                    </TouchableOpacity>
                </View>
            </ScrollView>

            {/* Location Picker Modal */}
            <Modal
                visible={showLocationPicker}
                animationType="slide"
                onRequestClose={() => setShowLocationPicker(false)}
            >
                <DeliveryLocationPicker
                    onLocationSelected={(addressData) => {
                        // Extract location coordinates
                        if (addressData && addressData.coordinates) {
                            setCoordinates(addressData.coordinates);

                            // Update address fields with standardized field names
                            // Handle both naming conventions for compatibility
                            if (addressData.doorNo || addressData.flatNumber) setDoorNo(addressData.doorNo || addressData.flatNumber);
                            if (addressData.streetName || addressData.street) setStreetName(addressData.streetName || addressData.street);
                            if (addressData.area || addressData.locality) setArea(addressData.area || addressData.locality);
                            if (addressData.district || addressData.city) setDistrict(addressData.district || addressData.city);
                            if (addressData.pincode) setPincode(addressData.pincode);

                            // Clear any previous errors
                            setError('');
                        }
                        setShowLocationPicker(false);
                    }}
                    onClose={() => setShowLocationPicker(false)}
                />
            </Modal>
        </KeyboardAvoidingView>
    );
};

export default AddressDetailsScreen;
