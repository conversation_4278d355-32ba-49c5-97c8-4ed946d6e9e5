import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    Alert,
    Modal,
    ActivityIndicator
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import DeliveryLocationPicker from '../Components/DeliveryLocationPicker';
import { useUser } from '../context/UserContext';
import { addUserAddress, updateUserAddress } from '../utils/api/userProfileApi';

const DeliveryAddressScreen = ({ navigation, route }) => {
    const [showLocationPicker, setShowLocationPicker] = useState(false);
    const [selectedAddress, setSelectedAddress] = useState(null);
    const [loading, setLoading] = useState(false);
    const { addresses, refreshUserData, updateUserProfile } = useUser();

    // Check if we're editing an existing address
    const existingAddress = route.params?.address;
    const isEditing = !!existingAddress;

    // Automatically show the location picker when the screen loads
    useEffect(() => {
        setShowLocationPicker(true);

        // Log the existing address ID for debugging
        if (isEditing && existingAddress) {
            console.log('Editing address with ID:', existingAddress._id);
        }
    }, [existingAddress, isEditing]);

    // Function to find the correct address ID from the user's addresses
    const findCorrectAddressId = (existingAddress) => {
        if (!existingAddress || !addresses || addresses.length === 0) {
            return null;
        }

        // First try to find by exact ID match
        const exactMatch = addresses.find(addr => addr._id === existingAddress._id);
        if (exactMatch) {
            return exactMatch._id;
        }

        // If no exact match, try to find by similar content (same doorNo, streetName, area)
        const similarMatch = addresses.find(addr =>
            addr.doorNo === existingAddress.doorNo &&
            addr.streetName === existingAddress.streetName &&
            addr.area === existingAddress.area
        );

        if (similarMatch) {
            return similarMatch._id;
        }

        // If still no match, return the most recently added address ID
        if (addresses.length > 0) {
            // Sort addresses by creation date (newest first)
            const sortedAddresses = [...addresses].sort((a, b) =>
                new Date(b.createdAt) - new Date(a.createdAt)
            );
            return sortedAddresses[0]._id;
        }

        return null;
    };

    // Handle location selection
    const handleLocationSelected = async (address) => {
        try {
            setLoading(true);
            setSelectedAddress(address);

            // Format the address for the database
            const formattedAddress = {
                type: address.addressType || address.type || 'Home',
                addressType: address.addressType || address.type || 'Home', // Ensure addressType is included
                // Handle both naming conventions for compatibility
                doorNo: address.doorNo || address.flatNumber || '',
                flatNumber: address.doorNo || address.flatNumber || '', // Add for compatibility
                streetName: address.streetName || address.street || '',
                street: address.streetName || address.street || '', // Add for compatibility
                area: address.area || address.locality || '',
                locality: address.area || address.locality || '', // Add for compatibility
                district: address.district || address.city || address.area || '',
                city: address.district || address.city || '', // Add for compatibility
                pincode: address.pincode || '',
                fullAddress: address.fullAddress || address.formattedString ||
                    `${address.doorNo || address.flatNumber || ''}, ${address.streetName || address.street || ''}, ${address.area || address.locality || ''}, ${address.district || address.city || ''}, ${address.pincode || ''}`,
                // Include coordinates in both formats for compatibility
                coordinates: {
                    latitude: address.coordinates?.latitude || address.latitude,
                    longitude: address.coordinates?.longitude || address.longitude
                },
                latitude: address.coordinates?.latitude || address.latitude,
                longitude: address.coordinates?.longitude || address.longitude
            };

            console.log('Saving edited address to database:', formattedAddress);

            // Validate required fields - check both field naming conventions
            const hasDoorNo = !!(formattedAddress.doorNo || formattedAddress.flatNumber);
            const hasStreetName = !!(formattedAddress.streetName || formattedAddress.street);

            if (!hasDoorNo || !hasStreetName) {
                Alert.alert(
                    "Missing Information",
                    "Please provide your door/flat number and street name.",
                    [{ text: "OK" }]
                );
                setLoading(false);
                return;
            }

            try {
                // If editing an existing address, update it; otherwise add a new one
                if (isEditing) {
                    // Get the address ID directly from the existing address
                    const addressId = existingAddress._id;

                    if (addressId) {
                        console.log('Updating existing address with ID:', addressId);

                        // Update the user's main address if this is the primary address
                        if (existingAddress.isPrimary || existingAddress.isDefault) {
                            console.log('Updating primary address in user profile');

                            // Update the main address in the user profile
                            await updateUserProfile({
                                address: formattedAddress
                            });
                        }

                        // Always update the address in the addresses array
                        try {
                            // Try to update with the address ID
                            await updateUserAddress(addressId, formattedAddress);
                            console.log('Successfully updated address with ID:', addressId);
                        } catch (updateError) {
                            console.error('Error updating address:', updateError);

                            // Show error message to user
                            Alert.alert(
                                "Error Updating Address",
                                "There was a problem updating your address. Please try again.",
                                [{ text: "OK" }]
                            );
                            throw updateError;
                        }
                    } else {
                        console.log('No address ID found, cannot update');
                        Alert.alert(
                            "Error Updating Address",
                            "Could not identify the address to update. Please try again.",
                            [{ text: "OK" }]
                        );
                        throw new Error('No address ID found for update');
                    }
                } else {
                    console.log('Adding new address');
                    await addUserAddress(formattedAddress);
                }
            } catch (apiError) {
                console.error('API error during address operation:', apiError);
                Alert.alert(
                    "Error Saving Address",
                    "There was a problem saving your address. Please try again.",
                    [{ text: "OK" }]
                );
                throw apiError;
            }

            // Refresh user data to get updated addresses
            await refreshUserData();

            // Also update the main address in the user profile
            await updateUserProfile({
                address: {
                    doorNo: formattedAddress.doorNo,
                    streetName: formattedAddress.streetName,
                    area: formattedAddress.area,
                    district: formattedAddress.district,
                    pincode: formattedAddress.pincode,
                    fullAddress: formattedAddress.fullAddress,
                    // Include coordinates in both formats for compatibility
                    coordinates: {
                        latitude: formattedAddress.coordinates?.latitude,
                        longitude: formattedAddress.coordinates?.longitude
                    },
                    latitude: formattedAddress.coordinates?.latitude,
                    longitude: formattedAddress.coordinates?.longitude,
                    addressType: formattedAddress.addressType,
                    isPrimary: true, // Set as primary address
                    isWithinDeliveryZone: true // Assume it's within delivery zone
                }
            });

            // Show success message
            Alert.alert(
                "Address Saved",
                isEditing
                    ? "Your address has been updated successfully."
                    : "Your new address has been saved successfully.",
                [{ text: "OK" }]
            );

            // Close the location picker
            setShowLocationPicker(false);
        } catch (error) {
            console.error('Error saving address:', error);
            Alert.alert(
                "Error",
                "Failed to save address. Please try again.",
                [{ text: "OK" }]
            );
        } finally {
            setLoading(false);
        }
    };

    // Proceed with the selected address
    const proceedWithAddress = () => {
        // Simply go back to the previous screen after saving the address
        navigation.goBack();
    };

    // We don't need to render saved addresses in this screen anymore
    // This screen is just for adding new addresses with precise location

    return (
        <View style={styles.container}>
            {loading ? (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#A31621" />
                    <Text style={styles.loadingText}>
                        {isEditing ? "Updating your address..." : "Saving your address..."}
                    </Text>
                </View>
            ) : (
                showLocationPicker && (
                    <DeliveryLocationPicker
                        onLocationSelected={handleLocationSelected}
                        onClose={() => navigation.goBack()}
                        existingAddress={existingAddress}
                    />
                )
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f8f8'
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.8)'
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#333',
        fontWeight: '500'
    }
});

export default DeliveryAddressScreen;
