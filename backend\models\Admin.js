const mongoose = require('mongoose');

const adminSchema = new mongoose.Schema({
    name: { type: String, required: true },
    phoneNumber: { type: String, required: true, unique: true },
    email: { type: String, required: false, unique: true, sparse: true }, // Optional email
    // Removed password field as we're using OTP-based authentication
    otpCode: { type: String, default: null },
    otpExpiry: { type: Date, default: null },
    refreshToken: { type: String, default: null },
    expoPushToken: { type: String, default: null },
    // Alias for expoPushToken for consistency
    pushToken: { type: String, default: null },
    otpRetryCount: { type: Number, default: 0 },
    lastOtpRequest: { type: Date, default: null },
    role: { type: String, enum: ['ADMIN'], default: 'ADMIN' } // Only Admin can have this role
});

const Admin = mongoose.model('Admin', adminSchema);
module.exports = Admin;
