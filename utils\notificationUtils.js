/**
 * Utility functions for handling push notifications in the app
 * Using Expo Push Notifications for managed workflow compatibility
 */

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform, Alert, Linking } from 'react-native';
import Constants from 'expo-constants';
import axios from 'axios';
import { API_URL } from '../config/constants';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

/**
 * Request notification permissions and get FCM push token
 * @returns {Promise<string|null>} FCM token or null if permission denied
 */
export const registerForPushNotificationsAsync = async () => {
  try {
    let token;

    console.log('Starting FCM notification registration...');
    console.log('Platform:', Platform.OS);
    console.log('Is Device:', Device.isDevice);

    // Check if running in Expo Go or standalone app
    const isExpoGo = Constants.appOwnership === 'expo';
    const isStandalone = Constants.appOwnership === 'standalone';
    console.log('Running in Expo Go:', isExpoGo);
    console.log('Running as Standalone:', isStandalone);
    console.log('App Ownership:', Constants.appOwnership);
    console.log('Expo SDK Version:', Constants.expoConfig?.sdkVersion || 'Unknown');

    if (Platform.OS === 'android') {
      console.log('Setting up Android notification channel...');
      await Notifications.setNotificationChannelAsync('default', {
        name: 'MeatS Now Notifications',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#A31621',
        sound: 'default',
        enableVibrate: true,
        enableLights: true,
        showBadge: true,
      });
      console.log('Android notification channel created');
    }

    if (Device.isDevice) {
      console.log('Checking existing permissions...');
      const { status: existingStatus, granted, canAskAgain } = await Notifications.getPermissionsAsync();
      console.log('Existing permission status:', { existingStatus, granted, canAskAgain });

      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        if (canAskAgain) {
          console.log('Requesting notification permissions...');
          const { status, granted: newGranted } = await Notifications.requestPermissionsAsync({
            ios: {
              allowAlert: true,
              allowBadge: true,
              allowSound: true,
              allowAnnouncements: true,
            },
            android: {
              allowAlert: true,
              allowBadge: true,
              allowSound: true,
            },
          });
          finalStatus = status;
          console.log('Permission request result:', { status, granted: newGranted });
        } else {
          console.log('Cannot ask for permissions again');
        }
      }

      if (finalStatus !== 'granted') {
        console.log('Notification permission denied');
        Alert.alert(
          'Notification Permission Required',
          'To receive OTP notifications, please enable notifications for Meat Now in your device settings.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Open Settings',
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              },
            },
          ]
        );
        return null;
      }

      console.log('Getting Expo push token...');

      try {
        // Get project ID for Expo push token
        const projectId = Constants.expoConfig?.extra?.eas?.projectId;

        if (!projectId) {
          console.log('No project ID found, using legacy token generation');
          token = (await Notifications.getExpoPushTokenAsync()).data;
        } else {
          console.log('Using project ID for token generation:', projectId);
          token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
        }

        if (!token) {
          throw new Error('Failed to get Expo push token');
        }

        console.log('Expo push token obtained successfully:', token?.substring(0, 50) + '...');
      } catch (tokenError) {
        console.error('Error getting push token:', tokenError);
        console.error('Token error details:', {
          message: tokenError.message,
          code: tokenError.code,
          stack: tokenError.stack
        });
        throw tokenError;
      }
    } else {
      console.log('Must use physical device for Push Notifications');
      Alert.alert(
        'Physical Device Required',
        'Push notifications only work on physical devices, not simulators.'
      );
      return null;
    }

    return token;
  } catch (error) {
    console.error('Error getting push token:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    // More specific error handling
    let errorMessage = 'There was an error setting up notifications. You may need to enter OTP manually.';

    if (error.message?.includes('not supported')) {
      errorMessage = 'Push notifications are not supported on this device. Please enter OTP manually.';
    } else if (error.message?.includes('permission')) {
      errorMessage = 'Notification permission was denied. Please enable notifications in settings.';
    } else if (error.message?.includes('network')) {
      errorMessage = 'Network error while setting up notifications. Please check your connection.';
    }

    Alert.alert(
      'Notification Setup Failed',
      errorMessage
    );
    return null;
  }
};

/**
 * Save push token to backend
 * @param {string} phoneNumber - User's phone number
 * @param {string} pushToken - Expo push token
 * @returns {Promise<boolean>} Success status
 */
export const savePushTokenToBackend = async (phoneNumber, pushToken) => {
  try {
    if (!phoneNumber || !pushToken) {
      console.log('Missing phone number or push token');
      return false;
    }

    console.log(`🚀 PRODUCTION: Saving Expo push token for ${phoneNumber}`);

    const response = await axios.post(`${API_URL}/auth/save-push-token`, {
      phoneNumber,
      expoPushToken: pushToken,
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200) {
      console.log('🚀 PRODUCTION: Expo push token saved successfully');
      return true;
    } else {
      console.error('🚀 PRODUCTION: Failed to save Expo push token:', response.data);
      return false;
    }
  } catch (error) {
    console.error('Error saving push token:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
};

/**
 * Setup notification listeners for OTP auto-fill
 * @param {Function} onOtpReceived - Callback when OTP is received
 * @returns {Function} Cleanup function
 */
export const setupOtpNotificationListener = (onOtpReceived) => {
  console.log('Setting up OTP notification listener');

  // Listen for notifications received while app is in foreground
  const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
    console.log('Notification received in foreground:', notification);

    const otp = notification.request.content.data?.otp;
    const type = notification.request.content.data?.type;

    if (type === 'otp_verification' && otp) {
      console.log('OTP received via notification:', otp);
      onOtpReceived(otp);
    }
  });

  // Listen for notification responses (when user taps notification)
  const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
    console.log('Notification response received:', response);

    const otp = response.notification.request.content.data?.otp;
    const type = response.notification.request.content.data?.type;

    if (type === 'otp_verification' && otp) {
      console.log('OTP received via notification tap:', otp);
      onOtpReceived(otp);
    }
  });

  // Return cleanup function
  return () => {
    foregroundSubscription.remove();
    responseSubscription.remove();
  };
};

/**
 * Check if notifications are enabled
 * @returns {Promise<boolean>} Whether notifications are enabled
 */
export const areNotificationsEnabled = async () => {
  try {
    const { status } = await Notifications.getPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error checking notification permissions:', error);
    return false;
  }
};

/**
 * Show notification permission prompt with custom message
 * @param {string} message - Custom message to show
 * @returns {Promise<boolean>} Whether permission was granted
 */
export const requestNotificationPermissionWithMessage = async (message) => {
  try {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();

    if (existingStatus === 'granted') {
      return true;
    }

    return new Promise((resolve) => {
      Alert.alert(
        'Enable Notifications',
        message || 'Allow Meat Now to send you OTP notifications for faster login.',
        [
          {
            text: 'Not Now',
            style: 'cancel',
            onPress: () => resolve(false),
          },
          {
            text: 'Enable',
            onPress: async () => {
              const { status } = await Notifications.requestPermissionsAsync();
              resolve(status === 'granted');
            },
          },
        ]
      );
    });
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

/**
 * Clear all notifications
 */
export const clearAllNotifications = async () => {
  try {
    await Notifications.dismissAllNotificationsAsync();
    console.log('All notifications cleared');
  } catch (error) {
    console.error('Error clearing notifications:', error);
  }
};

/**
 * Check if app is running in Expo Go
 * @returns {boolean} Whether app is running in Expo Go
 */
export const isRunningInExpoGo = () => {
  return Constants.appOwnership === 'expo';
};

/**
 * Get detailed notification status for debugging
 * @returns {Promise<object>} Detailed status information
 */
export const getNotificationDebugInfo = async () => {
  try {
    const permissions = await Notifications.getPermissionsAsync();
    const isExpoGo = isRunningInExpoGo();
    const isDevice = Device.isDevice;
    const platform = Platform.OS;
    const projectId = Constants.expoConfig?.extra?.eas?.projectId;

    return {
      permissions,
      isExpoGo,
      isDevice,
      platform,
      projectId,
      deviceInfo: {
        brand: Device.brand,
        modelName: Device.modelName,
        osName: Device.osName,
        osVersion: Device.osVersion,
      }
    };
  } catch (error) {
    console.error('Error getting debug info:', error);
    return { error: error.message };
  }
};

/**
 * Show notification setup guidance based on current environment
 */
export const showNotificationSetupGuidance = async () => {
  const debugInfo = await getNotificationDebugInfo();

  if (debugInfo.isExpoGo && debugInfo.permissions?.status !== 'granted') {
    Alert.alert(
      'Notification Permission Issue',
      'Notifications should work in Expo Go (SDK 52). The issue might be:\n\n1. Permission denied\n2. Network connectivity\n3. Device compatibility\n\nTry enabling notifications in device settings.',
      [{ text: 'OK' }]
    );
  } else if (!debugInfo.isDevice) {
    Alert.alert(
      'Simulator Limitation',
      'Push notifications only work on physical devices, not simulators. Please test on a real device.',
      [{ text: 'OK' }]
    );
  } else if (debugInfo.permissions?.status !== 'granted') {
    Alert.alert(
      'Permission Required',
      'Notification permission is required for OTP delivery. Please enable notifications in your device settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open Settings',
          onPress: () => {
            if (Platform.OS === 'ios') {
              Linking.openURL('app-settings:');
            } else {
              Linking.openSettings();
            }
          }
        }
      ]
    );
  }
};

/**
 * Simple test function to isolate notification issues
 */
export const testNotificationSetup = async () => {
  try {
    console.log('=== TESTING EXPO NOTIFICATION SETUP ===');

    // Step 1: Check device
    console.log('1. Device check:', Device.isDevice ? 'Physical device' : 'Simulator');
    if (!Device.isDevice) {
      throw new Error('Must use physical device for notifications');
    }

    // Step 2: Check Expo notification permissions
    console.log('2. Checking Expo notification permissions...');
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    console.log('   Existing permission status:', existingStatus);

    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      console.log('   Requesting permissions...');
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
      console.log('   New permission status:', status);
    }

    if (finalStatus !== 'granted') {
      throw new Error('Expo notification permission denied');
    }

    // Step 3: Setup notification channel (Android)
    if (Platform.OS === 'android') {
      console.log('3. Setting up Android notification channel...');
      await Notifications.setNotificationChannelAsync('test', {
        name: 'Test Channel',
        importance: Notifications.AndroidImportance.MAX,
      });
      console.log('   Channel created successfully');
    }

    // Step 4: Get Expo push token
    console.log('4. Getting Expo push token...');
    const projectId = Constants.expoConfig?.extra?.eas?.projectId;
    let expoPushToken;

    if (!projectId) {
      console.log('   No project ID found, using legacy token generation');
      expoPushToken = (await Notifications.getExpoPushTokenAsync()).data;
    } else {
      console.log('   Using project ID for token generation:', projectId);
      expoPushToken = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
    }

    if (!expoPushToken) {
      throw new Error('Failed to get Expo push token');
    }

    console.log('   Expo push token obtained:', expoPushToken.substring(0, 50) + '...');

    console.log('=== EXPO NOTIFICATION SETUP TEST PASSED ===');
    return { success: true, token: expoPushToken, type: 'EXPO' };

  } catch (error) {
    console.error('=== EXPO NOTIFICATION SETUP TEST FAILED ===');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    return { success: false, error: error.message };
  }
};

/**
 * Schedule a local notification (for testing)
 * @param {string} title - Notification title
 * @param {string} body - Notification body
 * @param {Object} data - Additional data
 */
export const scheduleLocalNotification = async (title, body, data = {}) => {
  try {
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: 'default',
      },
      trigger: { seconds: 1 },
    });
    console.log('Local notification scheduled');
  } catch (error) {
    console.error('Error scheduling local notification:', error);
  }
};
