import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ActivityIndicator,
    Alert,
    Dimensions,
    Platform,
    Animated
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { openMapsWithDirections } from '../utils/locationUtils';
import { useLocation } from '../context/LocationContext';

// Dynamically import MapView to handle potential errors
let MapView, Marker;
try {
    const Maps = require('react-native-maps');
    MapView = Maps.default;
    Marker = Maps.Marker;
} catch (error) {
    console.error('Error loading react-native-maps:', error);
    // Create placeholder components if maps can't be loaded
    MapView = ({ children, ...props }) => (
        <View style={[props.style, { justifyContent: 'center', alignItems: 'center' }]}>
            <Text>Map not available</Text>
        </View>
    );
    Marker = ({ children }) => <View>{children}</View>;
}

const { width, height } = Dimensions.get('window');
const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.01;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

const LocationMapView = ({
    location,
    address,
    title = "Selected Location",
    showCurrentLocation = true,
    showDirectionsButton = true,
    onClose = () => {},
    editable = false,
    onLocationChange = () => {}
}) => {
    const [currentLocation, setCurrentLocation] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [hasShownDragHint, setHasShownDragHint] = useState(false);
    const mapRef = useRef(null);

    // Animation values for the pulsating effect
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const pulseOpacity = useRef(new Animated.Value(0.7)).current;

    useEffect(() => {
        if (showCurrentLocation) {
            getCurrentLocation();
        }
    }, [showCurrentLocation]);

    // Start the pulsating animation when component mounts
    useEffect(() => {
        const startPulseAnimation = () => {
            // Reset values
            pulseAnim.setValue(1);
            pulseOpacity.setValue(0.7);

            // Create a sequence of animations
            Animated.parallel([
                Animated.sequence([
                    Animated.timing(pulseAnim, {
                        toValue: 1.3,
                        duration: 800,
                        useNativeDriver: true
                    }),
                    Animated.timing(pulseAnim, {
                        toValue: 1,
                        duration: 800,
                        useNativeDriver: true
                    })
                ]),
                Animated.sequence([
                    Animated.timing(pulseOpacity, {
                        toValue: 0.2,
                        duration: 800,
                        useNativeDriver: true
                    }),
                    Animated.timing(pulseOpacity, {
                        toValue: 0.7,
                        duration: 800,
                        useNativeDriver: true
                    })
                ])
            ]).start(() => {
                // Repeat the animation
                startPulseAnimation();
            });
        };

        startPulseAnimation();

        // Clean up animation when component unmounts
        return () => {
            pulseAnim.stopAnimation();
            pulseOpacity.stopAnimation();
        };
    }, []);

    // Use the location context
    const { getLocation: getContextLocation } = useLocation();

    const getCurrentLocation = async () => {
        try {
            setLoading(true);
            setError(null);

            // Use the location context to get the current location
            const newLocation = await getContextLocation();

            if (!newLocation) {
                setError('Could not get your location');
                setLoading(false);
                return;
            }

            setCurrentLocation(newLocation);

            // Fit map to show both current location and target location if both exist
            if (location && mapRef.current) {
                setTimeout(() => {
                    mapRef.current.fitToCoordinates(
                        [
                            newLocation,
                            location
                        ],
                        {
                            edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
                            animated: true
                        }
                    );
                }, 1000);
            }
        } catch (err) {
            console.error('Error getting current location:', err);
            setError('Could not get your location');
        } finally {
            setLoading(false);
        }
    };

    const handleGetDirections = () => {
        // Show confirmation alert before opening maps
        Alert.alert(
            "Open Maps",
            "Would you like to open this location in Google Maps?",
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Open Maps",
                    onPress: () => {
                        if (location) {
                            openMapsWithDirections(address || title, location);
                        } else {
                            openMapsWithDirections(address || title);
                        }
                    }
                }
            ],
            { cancelable: true }
        );
    };

    const handleMapPress = (event) => {
        if (editable) {
            const newLocation = event.nativeEvent.coordinate;
            onLocationChange(newLocation);
        }
    };

    const handleMarkerDragEnd = (event) => {
        if (editable) {
            const newLocation = event.nativeEvent.coordinate;
            onLocationChange(newLocation);
        }
    };

    return (
        <View style={styles.container}>
            {/* Map View */}
            <MapView
                ref={mapRef}
                style={styles.map}
                initialRegion={location ? {
                    ...location,
                    latitudeDelta: LATITUDE_DELTA,
                    longitudeDelta: LONGITUDE_DELTA
                } : null}
                onPress={handleMapPress}
                showsUserLocation={true}
                showsMyLocationButton={false}
                showsCompass={false}
                showsScale={false}
                rotateEnabled={true}
                loadingEnabled={false}
                minZoomLevel={5}
                maxZoomLevel={19}
                moveOnMarkerPress={false}
                tracksViewChanges={false}
            >
                {/* Current Location Marker */}
                {currentLocation && showCurrentLocation && (
                    <Marker
                        coordinate={currentLocation}
                        title="Your Location"
                        description="You are here"
                    >
                        <View style={styles.currentLocationMarker}>
                            <MaterialIcons name="my-location" size={20} color="white" />
                        </View>
                    </Marker>
                )}

                {/* Target Location Marker */}
                {location && (
                    <Marker
                        coordinate={location}
                        title={title}
                        description={address || "Selected Location"}
                        draggable={false}
                        tracksViewChanges={false}
                    >
                        <View style={styles.targetLocationMarker}>
                            {/* Shadow for better visibility */}
                            <View style={styles.markerShadow}>
                                <MaterialIcons
                                    name="person-pin"
                                    size={48}
                                    color="#000"
                                />
                            </View>

                            {/* Main marker icon - person shape */}
                            <View style={styles.markerIconContainer}>
                                <MaterialIcons
                                    name="person-pin"
                                    size={48}
                                    color="#A31621"
                                />
                            </View>

                            {/* Pulsating circle for emphasis */}
                            <Animated.View
                                style={[
                                    styles.markerPulse,
                                    {
                                        transform: [{ scale: pulseAnim }],
                                        opacity: pulseOpacity
                                    }
                                ]}
                            />
                        </View>
                    </Marker>
                )}
            </MapView>

            {/* Empty container to cover "Powered by Google" */}
            <View style={styles.mapAttributionContainer} />

            {loading && (
                <View style={styles.loadingOverlay}>
                    <ActivityIndicator size="large" color="#A31621" />
                </View>
            )}

            {error && (
                <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>{error}</Text>
                </View>
            )}

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
                {showDirectionsButton && location && (
                    <TouchableOpacity
                        style={styles.directionsButton}
                        onPress={handleGetDirections}
                    >
                        <MaterialIcons name="directions" size={20} color="white" />
                        <Text style={styles.directionsButtonText}>Get Directions</Text>
                    </TouchableOpacity>
                )}

                <TouchableOpacity
                    style={styles.closeButton}
                    onPress={onClose}
                >
                    <MaterialIcons name="close" size={20} color="#A31621" />
                    <Text style={styles.closeButtonText}>Close Map</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    },
    map: {
        ...StyleSheet.absoluteFillObject
    },
    currentLocationMarker: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#4285F4',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 2,
        borderColor: 'white'
    },
    targetLocationMarker: {
        alignItems: 'center',
        justifyContent: 'center',
        width: 60,
        height: 60
    },
    markerShadow: {
        position: 'absolute',
        top: 0,
        left: 6,
        opacity: 0.3,
        transform: [{scale: 1.05}]
    },
    markerIconContainer: {
        position: 'absolute',
        top: 0,
        left: 6
    },
    markerPulse: {
        position: 'absolute',
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: 'rgba(163, 22, 33, 0.5)',
        borderWidth: 2,
        borderColor: 'rgba(163, 22, 33, 0.2)',
        top: 8,
        left: 15,
        zIndex: -1
    },
    markerDragHint: {
        backgroundColor: 'rgba(0,0,0,0.7)',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        marginTop: 40,
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.3)'
    },
    markerDragHintText: {
        color: 'white',
        fontSize: 10,
        fontWeight: 'bold'
    },
    loadingOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(255,255,255,0.7)',
        alignItems: 'center',
        justifyContent: 'center'
    },
    errorContainer: {
        position: 'absolute',
        top: '50%',
        left: 20,
        right: 20,
        backgroundColor: 'rgba(255,0,0,0.7)',
        padding: 10,
        borderRadius: 8,
        alignItems: 'center'
    },
    errorText: {
        color: 'white',
        textAlign: 'center'
    },
    actionButtons: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    directionsButton: {
        backgroundColor: '#A31621',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 10,
        paddingHorizontal: 15,
        borderRadius: 8,
        flex: 1,
        marginRight: 10
    },
    directionsButtonText: {
        color: 'white',
        marginLeft: 5,
        fontWeight: 'bold'
    },
    closeButton: {
        backgroundColor: 'white',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 10,
        paddingHorizontal: 15,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#A31621'
    },
    closeButtonText: {
        color: '#A31621',
        marginLeft: 5,
        fontWeight: 'bold'
    },
    mapAttributionContainer: {
        position: 'absolute',
        bottom: 0,
        right: 0,
        backgroundColor: 'transparent',
        width: 100,
        height: 20
    }
});

export default LocationMapView;
