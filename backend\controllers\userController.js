const User = require('../models/User');
const Order = require('../models/Order');

/**
 * Get user profile
 * @route GET /api/users/profile
 * @access Private
 */
const getUserProfile = async (req, res) => {
    try {
        const user = await User.findById(req.user._id)
            .select('-otpCode -otpExpiry -refreshToken');

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.status(200).json(user);
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update user profile
 * @route PUT /api/users/profile
 * @access Private
 */
const updateUserProfile = async (req, res) => {
    try {
        const { name, email, address } = req.body;
        const mongoose = require('mongoose');

        // Find user
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Update basic fields
        if (name) user.name = name;
        if (email) user.email = email;

        // Handle address update
        if (address) {
            console.log('Updating address in user profile:', address);

            // Update the main address field
            user.address = {
                doorNo: address.doorNo || '',
                streetName: address.streetName || '',
                area: address.area || '',
                district: address.district || '',
                pincode: address.pincode || '',
                fullAddress: address.fullAddress ||
                    `${address.doorNo || ''}, ${address.streetName || ''}, ${address.area || ''}, ${address.district || ''}, ${address.pincode || ''}`.trim(),
                coordinates: {
                    latitude: address.coordinates?.latitude || address.latitude || null,
                    longitude: address.coordinates?.longitude || address.longitude || null
                },
                latitude: address.coordinates?.latitude || address.latitude || null,
                longitude: address.coordinates?.longitude || address.longitude || null,
                addressType: address.addressType || 'Home'
            };

            // Initialize addresses array if it doesn't exist
            if (!user.addresses) {
                user.addresses = [];
            }

            // Check if this address should be primary
            const isPrimary = address.isPrimary !== undefined ? address.isPrimary : true;

            // If this is a primary address, update all other addresses to not be primary
            if (isPrimary) {
                user.addresses.forEach(addr => {
                    addr.isPrimary = false;
                });
            }

            // Extract coordinates from the address
            const lat = address.coordinates?.latitude || address.latitude || null;
            const lng = address.coordinates?.longitude || address.longitude || null;

            // Check if we need to add this to the addresses array
            // First, look for an existing address with the same details
            const existingAddressIndex = user.addresses.findIndex(addr =>
                addr.doorNo === address.doorNo &&
                addr.streetName === address.streetName &&
                addr.area === address.area &&
                addr.district === address.district &&
                addr.pincode === address.pincode
            );

            if (existingAddressIndex >= 0) {
                // Update the existing address
                console.log('Updating existing address in addresses array at index:', existingAddressIndex);

                user.addresses[existingAddressIndex] = {
                    ...user.addresses[existingAddressIndex],
                    doorNo: address.doorNo || '',
                    streetName: address.streetName || '',
                    area: address.area || '',
                    district: address.district || '',
                    pincode: address.pincode || '',
                    fullAddress: address.fullAddress ||
                        `${address.doorNo || ''}, ${address.streetName || ''}, ${address.area || ''}, ${address.district || ''}, ${address.pincode || ''}`.trim(),
                    coordinates: {
                        latitude: lat,
                        longitude: lng
                    },
                    latitude: lat,
                    longitude: lng,
                    isPrimary: isPrimary,
                    addressType: address.addressType || 'Home'
                };
            } else {
                // Add as a new address
                console.log('Adding new address to addresses array');

                const newAddress = {
                    _id: new mongoose.Types.ObjectId(),
                    type: address.addressType || 'Home',
                    doorNo: address.doorNo || '',
                    streetName: address.streetName || '',
                    area: address.area || '',
                    district: address.district || '',
                    pincode: address.pincode || '',
                    fullAddress: address.fullAddress ||
                        `${address.doorNo || ''}, ${address.streetName || ''}, ${address.area || ''}, ${address.district || ''}, ${address.pincode || ''}`.trim(),
                    coordinates: {
                        latitude: lat,
                        longitude: lng
                    },
                    latitude: lat,
                    longitude: lng,
                    isDefault: false,
                    isPrimary: isPrimary,
                    createdAt: new Date(),
                    isWithinDeliveryZone: address.isWithinDeliveryZone !== undefined ? address.isWithinDeliveryZone : true,
                    addressType: address.addressType || 'Home'
                };

                user.addresses.push(newAddress);
            }
        }

        const updatedUser = await user.save();
        console.log('User profile updated successfully');

        res.status(200).json({
            _id: updatedUser._id,
            name: updatedUser.name,
            email: updatedUser.email,
            number: updatedUser.number,
            address: updatedUser.address,
            addresses: updatedUser.addresses
        });
    } catch (error) {
        console.error('Error updating user profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get user's coin history
 * @route GET /api/users/coins
 * @access Private
 */
const getUserCoins = async (req, res) => {
    try {
        const user = await User.findById(req.user._id)
            .select('coins usedCoins coinsHistory usedCoupons')
            .populate({
                path: 'coinsHistory.orderId',
                select: 'orderNumber totalAmount status'
            });

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Calculate different types of coins from history
        const now = new Date();

        // 1. Active earned coins (not expired)
        const activeEarnedCoins = user.coinsHistory
            .filter(coin => coin.type === 'EARNED' && (!coin.expiry || new Date(coin.expiry) > now))
            .reduce((sum, coin) => sum + coin.amount, 0);

        // 2. Total used coins (all time) - excluding refunded used coins
        const usedCoinsEntries = user.coinsHistory.filter(coin => coin.type === 'USED');
        const refundedUsedCoinsEntries = user.coinsHistory.filter(coin =>
            coin.type === 'REFUNDED' &&
            coin.amount > 0 &&
            (coin.description && coin.description.includes('Refunded used coins'))
        );

        // Calculate net used coins (used coins minus refunded used coins)
        const totalUsedCoinsAmount = usedCoinsEntries.reduce((sum, coin) => sum + Math.abs(coin.amount), 0);
        const totalRefundedUsedCoinsAmount = refundedUsedCoinsEntries.reduce((sum, coin) => sum + Math.abs(coin.amount), 0);
        const totalUsedCoins = Math.max(0, totalUsedCoinsAmount - totalRefundedUsedCoinsAmount);

        // 3. Expired coins
        const expiredCoins = user.coinsHistory
            .filter(coin => coin.type === 'EARNED' && coin.expiry && new Date(coin.expiry) <= now)
            .reduce((sum, coin) => sum + coin.amount, 0);

        // 4. Refunded coins (both earned and used coins that were refunded)
        const refundedCoins = user.coinsHistory
            .filter(coin => coin.type === 'REFUNDED')
            .reduce((sum, coin) => sum + Math.abs(coin.amount), 0);

        // 5. Current balance calculations
        const totalCoinBalance = user.coins; // This is the actual balance (can be negative in rare cases)
        const availableForUse = Math.max(0, totalCoinBalance); // Only positive coins can be used
        const hasNegativeBalance = totalCoinBalance < 0;

        // 6. Validation: Total balance should equal active earned coins minus used coins plus refunded coins
        const calculatedBalance = activeEarnedCoins - totalUsedCoins + refundedCoins - expiredCoins;

        console.log('Coin calculation breakdown:', {
            userCoinsField: user.coins,
            activeEarnedCoins,
            totalUsedCoins,
            refundedCoins,
            expiredCoins,
            calculatedBalance,
            difference: Math.abs(user.coins - calculatedBalance)
        });

        res.status(200).json({
            // Main balance
            totalCoins: totalCoinBalance, // The actual coin balance
            availableCoins: availableForUse, // Coins available to use (max 0, totalCoins)

            // Breakdown
            activeCoins: Math.max(0, activeEarnedCoins), // Active earned coins (not expired)
            usedCoins: totalUsedCoins, // Total coins used for discounts
            expiredCoins: Math.max(0, expiredCoins), // Coins that have expired
            refundedCoins: Math.max(0, refundedCoins), // Coins that were refunded

            // Status
            hasNegativeBalance,
            negativeAmount: hasNegativeBalance ? Math.abs(totalCoinBalance) : 0,

            // History and other data
            coinsHistory: user.coinsHistory,
            usedCoupons: user.usedCoupons || []
        });
    } catch (error) {
        console.error('Error fetching user coins:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get user's order history
 * @route GET /api/users/orders
 * @access Private
 */
const getUserOrders = async (req, res) => {
    try {
        const orders = await Order.find({ userId: req.user._id })
            .sort({ createdAt: -1 })
            .populate('deliveryPartner', 'name phoneNumber');

        res.status(200).json(orders);
    } catch (error) {
        console.error('Error fetching user orders:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

module.exports = {
    getUserProfile,
    updateUserProfile,
    getUserCoins,
    getUserOrders
};