import axios from 'axios';
import { API_URL, USER_TYPES } from '../../config/constants';
import { getAuthToken, getUserData } from '../authStorage';

// Get all delivery partners
export const getAllDeliveryPartners = async () => {
    try {
        const token = await getAuthToken();
        console.log('Auth token available:', !!token);

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('User type from userData:', userType);

        // Only admins should be able to fetch all delivery partners
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch all delivery partners');
            throw new Error('Unauthorized: Admin access required');
        }

        console.log('Fetching delivery partners from:', `${API_URL}/admin/delivery-partners`);

        // Debug: Log token details
        if (token) {
            try {
                const tokenParts = token.split('.');
                if (tokenParts.length === 3) {
                    const payload = JSON.parse(atob(tokenParts[1]));
                    console.log('Token payload:', payload);
                    console.log('User type in token:', payload.userType);
                }
            } catch (e) {
                console.error('Error parsing token:', e);
            }
        }

        const response = await axios.get(`${API_URL}/admin/delivery-partners`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        console.log('Delivery partners API response status:', response.status);
        console.log('Delivery partners API response data type:', typeof response.data);
        console.log('Delivery partners API response data keys:', Object.keys(response.data || {}));

        return response.data;
    } catch (error) {
        console.error('Error fetching delivery partners:', error);
        console.error('Error details:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url
        });
        throw error;
    }
};

// Get delivery partner by ID
export const getDeliveryPartnerById = async (partnerId) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        // If user is a delivery partner, they should only be able to get their own profile
        if (userType === USER_TYPES.DELIVERY_PARTNER) {
            // Check if the requested partner ID matches the current user's ID
            if (userData._id !== partnerId && userData.id !== partnerId) {
                console.error('Unauthorized: Delivery partners can only access their own profile');
                throw new Error('Unauthorized: Access denied');
            }

            // Use the delivery-specific endpoint
            console.log('Using delivery-specific endpoint to get profile');
            const { getDeliveryPartnerProfile } = require('./deliveryApi');
            const profile = await getDeliveryPartnerProfile();
            return { deliveryPartner: profile };
        }

        // For admins, use the admin endpoint
        console.log(`Fetching delivery partner ${partnerId} from admin endpoint`);
        const response = await axios.get(`${API_URL}/admin/delivery-partners/${partnerId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error fetching delivery partner ${partnerId}:`, error);
        throw error;
    }
};

// Create delivery partner
export const createDeliveryPartner = async (partnerData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        // Only admins should be able to create delivery partners
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can create delivery partners');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.post(`${API_URL}/admin/delivery-partners`, partnerData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error creating delivery partner:', error);
        throw error;
    }
};

// Update delivery partner
export const updateDeliveryPartner = async (partnerId, partnerData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        // Only admins should be able to update delivery partners
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can update delivery partners');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.put(`${API_URL}/admin/delivery-partners/${partnerId}`, partnerData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error updating delivery partner ${partnerId}:`, error);
        throw error;
    }
};

// Delete delivery partner
export const deleteDeliveryPartner = async (partnerId) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        // Only admins should be able to delete delivery partners
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can delete delivery partners');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.delete(`${API_URL}/admin/delivery-partners/${partnerId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error deleting delivery partner ${partnerId}:`, error);
        throw error;
    }
};

// Toggle delivery partner availability
export const toggleDeliveryPartnerAvailability = async (partnerId) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        // If user is a delivery partner, they should only be able to toggle their own availability
        if (userType === USER_TYPES.DELIVERY_PARTNER) {
            // Check if the requested partner ID matches the current user's ID
            if (userData._id !== partnerId && userData.id !== partnerId) {
                console.error('Unauthorized: Delivery partners can only update their own availability');
                throw new Error('Unauthorized: Access denied');
            }

            // Use the delivery-specific endpoint
            console.log('Using delivery-specific endpoint to update availability');
            const { updateAvailability } = require('./deliveryApi');

            // Get current availability status to toggle it
            const isCurrentlyAvailable = userData.isAvailable !== false;
            const response = await updateAvailability(!isCurrentlyAvailable);
            return response;
        }

        // For admins, use the admin endpoint
        console.log(`Toggling availability for delivery partner ${partnerId} using admin endpoint`);
        const response = await axios.patch(`${API_URL}/admin/delivery-partners/${partnerId}/toggle-availability`, {}, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error toggling delivery partner ${partnerId} availability:`, error);
        throw error;
    }
};
