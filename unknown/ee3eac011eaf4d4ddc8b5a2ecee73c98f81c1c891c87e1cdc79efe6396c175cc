import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, StatusBar, Animated, ActivityIndicator, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useUser } from '../context/UserContext';

const EditProfileScreen = ({ navigation }) => {
    // We no longer need the onGoBack callback from route params
    const { currentUser, updateUserProfile, refreshUserData } = useUser();

    // Loading state for both initial data fetch and save operation
    const [isLoading, setIsLoading] = useState(true);

    // User data states - only name and email
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    // We'll use the first letter of the name for the profile image
    // No need to store actual profile image

    // Fetch user data when component mounts
    useEffect(() => {
        const fetchUserData = async () => {
            setIsLoading(true);
            try {
                // Refresh user data from the database
                await refreshUserData();

                // Once data is refreshed, update the local state
                if (currentUser) {
                    console.log('Fetched user data from database:', currentUser);
                    setName(currentUser.name || '');
                    setEmail(currentUser.email || '');
                    // We don't need to set profile image as we'll use the first letter of the name
                }
            } catch (error) {
                console.error('Error fetching user data:', error);
                showToast('Failed to load user data', 'error');
            } finally {
                setIsLoading(false);
            }
        };

        fetchUserData();
    }, []);

    // Animation for toast notification
    const toastAnimation = useRef(new Animated.Value(0)).current;
    const [toastVisible, setToastVisible] = useState(false);
    const [toastMessage, setToastMessage] = useState('');
    const [toastType, setToastType] = useState('error');

    // Show toast notification
    const showToast = (message, type = 'error') => {
        setToastMessage(message);
        setToastType(type); // Now we can use this
        setToastVisible(true);

        Animated.sequence([
            // Animate in
            Animated.timing(toastAnimation, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            // Hold
            Animated.delay(2000),
            // Animate out
            Animated.timing(toastAnimation, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            })
        ]).start(() => {
            setToastVisible(false);
        });
    };

    // Get toast background color based on type
    const getToastBgColor = () => {
        return toastType === 'success' ? 'bg-green-500' : 'bg-red-500';
    };

    // Get toast icon based on type
    const getToastIcon = () => {
        return toastType === 'success' ? 'check-circle' : 'error';
    };

    // We're not using image picker functionality anymore
    // Profile image will be based on the first letter of the user's name

    const handleSave = async () => {
        // Validate inputs
        if (!name.trim()) {
            showToast('Name cannot be empty', 'error');
            return;
        }

        if (email && !/\S+@\S+\.\S+/.test(email)) {
            showToast('Please enter a valid email address', 'error');
            return;
        }

        try {
            // Show loading state
            setIsLoading(true);

            console.log('Updating profile with name:', name, 'and email:', email);

            // Update user profile in context - only name and email
            await updateUserProfile({
                name,
                email
            });

            // Log the updated user
            console.log('Updated user profile successfully');

            // Refresh user data to ensure we have the latest
            try {
                await refreshUserData();
                console.log('User data refreshed after profile update');
            } catch (refreshError) {
                console.error('Error refreshing user data after profile update:', refreshError);
                // Continue with success flow even if refresh fails
            }

            // Show success message
            showToast('Profile updated successfully', 'success');

            // Navigate back after a delay
            setTimeout(() => {
                navigation.goBack();
            }, 2000);
        } catch (error) {
            console.error('Error updating profile:', error);

            // Show more specific error message if available
            if (error.response && error.response.data && error.response.data.message) {
                showToast(error.response.data.message, 'error');
            } else if (error.message && error.message.includes('404')) {
                showToast('Profile not found. Please try logging in again.', 'error');
            } else {
                showToast('Failed to update profile. Please try again.', 'error');
            }

            // Try to refresh user data even if update failed
            try {
                console.log('Attempting to refresh user data after update error');
                await refreshUserData();
            } catch (refreshError) {
                console.error('Error refreshing user data after update error:', refreshError);
            }
        } finally {
            setIsLoading(false);
        }
    };

    // Toast animation styles
    const toastTranslateY = toastAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [100, 0],
    });

    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" />

            {/* Header */}
            <View className="bg-madder h-24 rounded-b-3xl p-4 pt-8 flex-row items-center">
                <TouchableOpacity
                    className=" p-2 rounded-full mr-4"
                    onPress={() => navigation.goBack()}
                >
                    <MaterialIcons name="arrow-back" size={22} color="white" />
                </TouchableOpacity>
                <Text className="text-xl text-white font-bold">Edit Profile</Text>
            </View>

            {/* Loading Indicator */}
            {isLoading && (
                <View className="absolute inset-0 bg-white/80 items-center justify-center z-50" style={{top: 0, left: 0, right: 0, bottom: 0}}>
                    <View className="bg-white p-6 rounded-xl shadow-lg items-center">
                        <ActivityIndicator size="large" color="#A31621" />
                        <Text className="mt-4 text-gray-700 font-medium text-center">Loading profile data...</Text>
                    </View>
                </View>
            )}

            <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                <View className="p-6">
                    {/* Profile Picture Section - Using first letter of name */}
                    <View className="items-center mb-6">
                        <View className="w-24 h-24 rounded-full bg-madder/10 items-center justify-center">
                            <Text className="text-madder text-3xl font-bold">
                                {name && name.length > 0 ? name.charAt(0).toUpperCase() : '?'}
                            </Text>
                        </View>
                        <Text className="text-gray-600 mt-2">Profile picture shows first letter of your name</Text>
                    </View>

                    {/* User's phone number display - not editable */}
                    <View className="mb-6">
                        <Text className="text-gray-700 mb-2 font-medium">Phone Number</Text>
                        <View className="bg-gray-100 p-4 rounded-lg">
                            <Text className="text-gray-500">
                                {currentUser?.phoneNumber || currentUser?.number || 'Not available'}
                            </Text>
                        </View>
                        <Text className="text-xs text-gray-500 mt-1">Phone number cannot be changed</Text>
                    </View>

                    {/* Name field */}
                    <View className="mb-6">
                        <Text className="text-gray-700 mb-2 font-medium">Name</Text>
                        <TextInput
                            className="bg-white p-4 rounded-lg text-base border border-gray-200"
                            value={name}
                            onChangeText={setName}
                            placeholder="Enter your name"
                        />
                    </View>

                    {/* Email field */}
                    <View className="mb-6">
                        <Text className="text-gray-700 mb-2 font-medium">Email (Optional)</Text>
                        <TextInput
                            className="bg-white p-4 rounded-lg text-base border border-gray-200"
                            value={email}
                            onChangeText={setEmail}
                            placeholder="Enter your email address"
                            keyboardType="email-address"
                            autoCapitalize="none"
                        />
                    </View>

                    {/* Address management note */}
                    <View className="bg-blue-50 p-4 rounded-lg mb-6">
                        <View className="flex-row items-center mb-2">
                            <MaterialIcons name="info" size={20} color="#3B82F6" />
                            <Text className="text-blue-700 font-medium ml-2">Address Management</Text>
                        </View>
                        <Text className="text-blue-600 text-sm">
                            To manage your delivery addresses, please use the Address section in your profile.
                        </Text>
                        <TouchableOpacity
                            className="bg-blue-100 p-2 rounded-lg mt-3"
                            onPress={() => navigation.navigate('AddressScreen')}
                        >
                            <Text className="text-blue-700 text-center font-medium">Go to Address Management</Text>
                        </TouchableOpacity>
                    </View>

                <TouchableOpacity
                    className="bg-madder py-4 rounded-xl mt-6 mb-10"
                    onPress={handleSave}
                >
                    <Text className="text-white text-center font-bold text-lg">Save Changes</Text>
                </TouchableOpacity>
                </View>
            </ScrollView>

            {/* Toast Notification */}
            {toastVisible && (
                <Animated.View
                    className={`absolute bottom-20 left-5 right-5 ${getToastBgColor()} rounded-lg p-4 flex-row items-center`}
                    style={{
                        transform: [{ translateY: toastTranslateY }],
                        opacity: toastAnimation,
                        shadowColor: "#000",
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.25,
                        shadowRadius: 3.84,
                        elevation: 5,
                    }}
                >
                    <MaterialIcons name={getToastIcon()} size={24} color="white" />
                    <Text className="text-white font-medium ml-2 flex-1">{toastMessage}</Text>
                </Animated.View>
            )}
        </View>
    );
};

export default EditProfileScreen;