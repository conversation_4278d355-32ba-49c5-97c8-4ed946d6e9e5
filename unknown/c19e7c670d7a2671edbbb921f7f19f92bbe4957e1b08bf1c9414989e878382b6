import React, { createContext, useState, useContext, useEffect } from 'react';
import {
    getAllDeliveryPartners,
    getDeliveryPartnerById,
    updateDeliveryPartner,
    toggleDeliveryPartnerAvailability,
    createDeliveryPartner,
    deleteDeliveryPartner
} from '../utils/api/deliveryPartnerApi';
import { getUserData, getAuthToken } from '../utils/authStorage';
import { USER_TYPES } from '../config/constants';

// Create the context
const AdminDeliveryPartnerContext = createContext();

// Create a provider component
export const AdminDeliveryPartnerProvider = ({ children }) => {
    // Initialize with empty array, will be populated from API
    const [deliveryPartners, setDeliveryPartners] = useState([]);
    // Loading state
    const [loading, setLoading] = useState(true);
    // Error state
    const [error, setError] = useState(null);
    // Track when data changes
    const [dataVersion, setDataVersion] = useState(0);

    // Function to refresh data - can be called after updates
    const refreshData = () => {
        console.log('AdminDeliveryPartnerContext: Triggering refresh of data');
        setDataVersion(prev => prev + 1);
    };

    // Fetch delivery partners from API on component mount or when data changes
    useEffect(() => {
        const fetchDeliveryPartners = async () => {
            try {
                setLoading(true);
                setError(null); // Clear previous errors

                // Get user data to check user type
                const userData = await getUserData();
                const userType = userData?.userType?.toUpperCase();

                console.log('AdminDeliveryPartnerContext: User type:', userType);

                // Skip fetching completely if user is not an admin
                if (userType !== USER_TYPES.ADMIN) {
                    console.log('AdminDeliveryPartnerContext: User is not admin, skipping fetch completely');
                    setDeliveryPartners([]);
                    setLoading(false);
                    return;
                }

                // Verify token is valid
                const token = await getAuthToken();
                if (!token) {
                    console.log('AdminDeliveryPartnerContext: No auth token available');
                    setLoading(false);
                    return;
                }

                // Only proceed if user is an admin
                console.log('AdminDeliveryPartnerContext: User is an admin, fetching all delivery partners');
                try {
                    const response = await getAllDeliveryPartners();
                    console.log('AdminDeliveryPartnerContext: Delivery partners fetched:', response);

                    // Check if response has deliveryPartners property
                    if (response && response.deliveryPartners && response.deliveryPartners.length > 0) {
                        console.log('AdminDeliveryPartnerContext: Found delivery partners in response.deliveryPartners');
                        setDeliveryPartners(response.deliveryPartners);
                    }
                    // Fallback: check if response is an array directly
                    else if (response && Array.isArray(response) && response.length > 0) {
                        console.log('AdminDeliveryPartnerContext: Found delivery partners in response array');
                        setDeliveryPartners(response);
                    }
                    // No delivery partners found
                    else {
                        console.log('AdminDeliveryPartnerContext: No delivery partners returned from API');
                        setDeliveryPartners([]);
                    }
                } catch (apiError) {
                    console.error('AdminDeliveryPartnerContext: Error fetching delivery partners from API:', apiError);
                    setError(apiError.message || 'Failed to fetch delivery partners');
                    setDeliveryPartners([]);
                }
            } catch (error) {
                console.error('AdminDeliveryPartnerContext: Error in delivery partner fetch process:', error);
                setError(error.message || 'An unexpected error occurred');
                // Fallback to empty array if process fails
                setDeliveryPartners([]);
            } finally {
                setLoading(false);
            }
        };

        fetchDeliveryPartners();
    }, [dataVersion]); // Re-run when dataVersion changes

    // Function to toggle availability
    const toggleAvailability = async (partnerId) => {
        try {
            if (!partnerId) {
                console.error('AdminDeliveryPartnerContext: No partner ID provided for toggle availability');
                throw new Error('No partner ID provided');
            }

            console.log(`AdminDeliveryPartnerContext: Toggling availability for partner ${partnerId}`);
            const response = await toggleDeliveryPartnerAvailability(partnerId);
            console.log('AdminDeliveryPartnerContext: Availability toggled:', response);

            // Update the local state with the response
            if (response && response.deliveryPartner) {
                setDeliveryPartners(prevPartners =>
                    prevPartners.map(partner =>
                        partner.id === partnerId || partner._id === partnerId
                            ? response.deliveryPartner
                            : partner
                    )
                );
                return response.deliveryPartner;
            } else {
                // Fallback to local update if API doesn't return the updated partner
                setDeliveryPartners(prevPartners =>
                    prevPartners.map(partner =>
                        partner.id === partnerId || partner._id === partnerId
                            ? { ...partner, isAvailable: !partner.isAvailable }
                            : partner
                    )
                );

                // Return the updated partner from local state
                const updatedPartner = deliveryPartners.find(
                    partner => partner.id === partnerId || partner._id === partnerId
                );

                if (updatedPartner) {
                    return {
                        ...updatedPartner,
                        isAvailable: !updatedPartner.isAvailable
                    };
                }
            }
        } catch (error) {
            console.error(`AdminDeliveryPartnerContext: Error toggling availability for partner ${partnerId}:`, error);
            throw error;
        }
    };

    // Function to update delivery partner profile
    const updateDeliveryPartnerProfile = async (partnerId, updatedData) => {
        try {
            console.log(`AdminDeliveryPartnerContext: Updating delivery partner ${partnerId}`);

            // Normalize the data to handle field name mismatches
            const normalizedData = { ...updatedData };

            // Convert phone to phoneNumber if needed
            if (normalizedData.phone && !normalizedData.phoneNumber) {
                console.log('Converting phone field to phoneNumber for API call');
                normalizedData.phoneNumber = normalizedData.phone;
            }

            // Make sure the ID is included in the update data
            const dataToUpdate = {
                ...normalizedData,
                id: partnerId
            };

            // Call API to update delivery partner
            const response = await updateDeliveryPartner(partnerId, dataToUpdate);
            console.log('AdminDeliveryPartnerContext: Delivery partner updated:', response);

            if (response && response.deliveryPartner) {
                // Update in the list with the response from the API
                setDeliveryPartners(prevPartners =>
                    prevPartners.map(partner =>
                        partner.id === partnerId || partner._id === partnerId
                            ? response.deliveryPartner
                            : partner
                    )
                );
                return response.deliveryPartner;
            } else {
                // Fallback to local update if API doesn't return the updated partner
                // Check if the partner exists
                const partnerExists = deliveryPartners.some(partner =>
                    partner.id === partnerId || partner._id === partnerId
                );

                if (partnerExists) {
                    // Update existing partner
                    setDeliveryPartners(prevPartners =>
                        prevPartners.map(partner =>
                            partner.id === partnerId || partner._id === partnerId
                                ? { ...partner, ...updatedData }
                                : partner
                        )
                    );
                } else {
                    // Add new partner
                    const newPartner = {
                        id: partnerId,
                        ...updatedData
                    };
                    setDeliveryPartners(prevPartners => [
                        ...prevPartners,
                        newPartner
                    ]);
                    return newPartner;
                }
            }
        } catch (error) {
            console.error(`AdminDeliveryPartnerContext: Error updating delivery partner ${partnerId}:`, error);
            throw error;
        }
    };

    // Function to get a delivery partner by ID
    const getPartnerById = async (partnerId) => {
        try {
            console.log(`AdminDeliveryPartnerContext: Getting delivery partner ${partnerId}`);

            // Call API to get delivery partner by ID
            const response = await getDeliveryPartnerById(partnerId);
            return response.deliveryPartner;
        } catch (error) {
            console.error(`AdminDeliveryPartnerContext: Error getting delivery partner ${partnerId}:`, error);
            // Fallback to local state
            return deliveryPartners.find(partner =>
                partner.id === partnerId || partner._id === partnerId
            );
        }
    };

    // Function to get a delivery partner by phone number
    const getPartnerByPhone = (phone) => {
        return deliveryPartners.find(partner =>
            partner.phone === phone || partner.phoneNumber === phone
        );
    };

    // Function to add a new delivery partner
    const addDeliveryPartner = async (partnerData) => {
        try {
            console.log('AdminDeliveryPartnerContext: Adding new delivery partner');
            const response = await createDeliveryPartner(partnerData);
            console.log('AdminDeliveryPartnerContext: Delivery partner added:', response);

            if (response && response.deliveryPartner) {
                // Add to the list
                setDeliveryPartners(prevPartners => [
                    ...prevPartners,
                    response.deliveryPartner
                ]);
                return response.deliveryPartner;
            }
        } catch (error) {
            console.error('AdminDeliveryPartnerContext: Error adding delivery partner:', error);
            throw error;
        }
    };

    // Function to delete a delivery partner
    const removeDeliveryPartner = async (partnerId) => {
        try {
            console.log(`AdminDeliveryPartnerContext: Deleting delivery partner ${partnerId}`);
            await deleteDeliveryPartner(partnerId);

            // Remove from the list
            setDeliveryPartners(prevPartners =>
                prevPartners.filter(partner =>
                    partner.id !== partnerId && partner._id !== partnerId
                )
            );

            return true;
        } catch (error) {
            console.error(`AdminDeliveryPartnerContext: Error deleting delivery partner ${partnerId}:`, error);
            throw error;
        }
    };

    // Value to be provided to consumers
    const value = {
        deliveryPartners,
        loading,
        error,
        toggleAvailability,
        updateDeliveryPartnerProfile,
        getDeliveryPartnerById: getPartnerById,
        getDeliveryPartnerByPhone: getPartnerByPhone,
        addDeliveryPartner,
        removeDeliveryPartner,
        refreshData
    };

    return (
        <AdminDeliveryPartnerContext.Provider value={value}>
            {children}
        </AdminDeliveryPartnerContext.Provider>
    );
};

// Custom hook to use the admin delivery partner context
export const useAdminDeliveryPartner = () => {
    const context = useContext(AdminDeliveryPartnerContext);
    if (context === undefined) {
        console.warn('useAdminDeliveryPartner called outside of AdminDeliveryPartnerProvider');
        // Return a default empty context instead of throwing an error
        return {
            deliveryPartners: [],
            loading: false,
            error: null,
            toggleAvailability: async () => {
                console.warn('toggleAvailability called outside of AdminDeliveryPartnerProvider');
                return null;
            },
            updateDeliveryPartnerProfile: async () => {
                console.warn('updateDeliveryPartnerProfile called outside of AdminDeliveryPartnerProvider');
                return null;
            },
            getDeliveryPartnerById: async () => {
                console.warn('getDeliveryPartnerById called outside of AdminDeliveryPartnerProvider');
                return null;
            },
            getDeliveryPartnerByPhone: () => {
                console.warn('getDeliveryPartnerByPhone called outside of AdminDeliveryPartnerProvider');
                return null;
            },
            addDeliveryPartner: async () => {
                console.warn('addDeliveryPartner called outside of AdminDeliveryPartnerProvider');
                return null;
            },
            removeDeliveryPartner: async () => {
                console.warn('removeDeliveryPartner called outside of AdminDeliveryPartnerProvider');
                return false;
            },
            refreshData: () => {
                console.warn('refreshData called outside of AdminDeliveryPartnerProvider');
            }
        };
    }
    return context;
};
