const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

// Function to run a script and log the output
async function runScript(scriptName) {
    console.log(`\n=== Running ${scriptName} ===\n`);
    try {
        const { stdout, stderr } = await execPromise(`node scripts/${scriptName}.js`);
        console.log(stdout);
        if (stderr) {
            console.error(stderr);
        }
        console.log(`\n=== Completed ${scriptName} ===\n`);
        return true;
    } catch (error) {
        console.error(`Error running ${scriptName}:`, error);
        return false;
    }
}

// Main function to run all scripts in sequence
async function main() {
    console.log('Starting database fix process...');
    
    // Step 1: Drop the email index
    await runScript('dropEmailIndex');
    
    // Step 2: Fix all users by adding generated emails
    await runScript('fixAllUsers');
    
    // Step 3: Add the admin user
    await runScript('addSpecificAdmin');
    
    // Step 4: Add the delivery partner
    await runScript('addDeliveryPartner');
    
    console.log('Database fix process completed!');
    process.exit(0);
}

// Run the main function
main().catch(error => {
    console.error('Error in main process:', error);
    process.exit(1);
});
