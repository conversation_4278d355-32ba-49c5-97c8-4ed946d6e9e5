const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Admin = require('../models/Admin');
const DeliveryPartner = require('../models/DeliveryPartner');
const User = require('../models/User');

// Load environment variables
dotenv.config();
console.log('MongoDB URI:', process.env.MONGO_URI ? 'URI is set' : 'URI is not set');

// Connect to MongoDB
const connectDB = async () => {
    try {
        const conn = await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log(`MongoDB Connected: ${conn.connection.host}`);
        return conn;
    } catch (error) {
        console.error('MongoDB Connection Error:', error);
        process.exit(1);
    }
};

// Admin data
const adminData = {
    name: 'Admin User',
    phoneNumber: '8825549901',
    email: '<EMAIL>',
    role: 'ADMIN'
};

// Delivery partner data
const deliveryPartners = [
    {
        name: 'Delivery Partner 1',
        phoneNumber: '9894258293',
        email: '<EMAIL>',
        vehicleType: 'Two Wheeler',
        vehicleNumber: 'TN01AB1234',
        location: 'Chennai'
    },
    {
        name: 'Delivery Partner 2',
        phoneNumber: '9876543210',
        email: '<EMAIL>',
        vehicleType: 'Two Wheeler',
        vehicleNumber: 'TN02CD5678',
        location: 'Chennai'
    },
    {
        name: 'Delivery Partner 3',
        phoneNumber: '9876543211',
        email: '<EMAIL>',
        vehicleType: 'Four Wheeler',
        vehicleNumber: 'TN03EF9012',
        location: 'Chennai'
    }
];

// Seed the database
const seedDatabase = async () => {
    try {
        // Connect to the database
        await connectDB();

        // Clear existing data
        console.log('Clearing existing data...');
        await Admin.deleteMany({});
        await DeliveryPartner.deleteMany({});

        // Add admin
        console.log('Adding admin...');
        const admin = new Admin(adminData);
        const savedAdmin = await admin.save();
        console.log(`Admin created with ID: ${savedAdmin._id}`);

        // Add delivery partners
        console.log('Adding delivery partners...');
        for (const partnerData of deliveryPartners) {
            partnerData.addedBy = savedAdmin._id;
            const partner = new DeliveryPartner(partnerData);
            const savedPartner = await partner.save();
            console.log(`Delivery partner created with ID: ${savedPartner._id}`);
        }

        console.log('Database seeded successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error seeding database:', error);
        process.exit(1);
    }
};

// Run the seed function
seedDatabase();
