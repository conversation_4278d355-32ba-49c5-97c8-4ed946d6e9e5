import axios from 'axios';
import { API_URL } from '../config/constants';
import { getAuthToken, getRefreshToken, storeAuthData, getUserData } from './authStorage';

// Flag to prevent multiple refresh attempts at the same time
let isRefreshing = false;
// Queue of callbacks to call after token refresh
let refreshSubscribers = [];

/**
 * Subscribe to token refresh
 * @param {Function} callback - Function to call after token refresh
 */
const subscribeTokenRefresh = (callback) => {
  refreshSubscribers.push(callback);
};

/**
 * Notify all subscribers that token has been refreshed
 * @param {string} token - New access token
 */
const onTokenRefreshed = (token) => {
  refreshSubscribers.forEach((callback) => callback(token));
  refreshSubscribers = [];
};

/**
 * Refresh the access token using the refresh token
 * @returns {Promise<string>} - New access token
 */
export const refreshAccessToken = async () => {
  try {
    // Prevent multiple refresh attempts
    if (isRefreshing) {
      return new Promise((resolve) => {
        subscribeTokenRefresh((token) => {
          resolve(token);
        });
      });
    }

    isRefreshing = true;
    console.log('Refreshing access token...');

    // Get refresh token
    const refreshToken = await getRefreshToken();
    if (!refreshToken) {
      console.log('No refresh token available, trying to get current auth token as fallback');

      // Try to get the current auth token as a fallback
      const currentToken = await getAuthToken();
      if (currentToken) {
        console.log('Using current auth token as fallback');
        isRefreshing = false;
        return currentToken;
      }

      console.error('No refresh token or auth token available');
      isRefreshing = false;
      throw new Error('No refresh token available');
    }

    // Call refresh token API
    try {
      const response = await axios.post(
        `${API_URL}/auth/refresh-token`,
        { refreshToken },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      // Get new access token
      const { token: newAccessToken } = response.data;
      if (!newAccessToken) {
        console.error('No access token in refresh response');
        isRefreshing = false;
        throw new Error('No access token in refresh response');
      }

      console.log('Access token refreshed successfully');

      // Get current user data
      const userData = await getUserData();

      // Store new access token
      await storeAuthData(newAccessToken, refreshToken, userData);

      // Notify subscribers
      onTokenRefreshed(newAccessToken);
      isRefreshing = false;

      return newAccessToken;
    } catch (apiError) {
      console.error('API error during token refresh:', apiError.message);

      // Try to get the current auth token as a fallback
      const currentToken = await getAuthToken();
      if (currentToken) {
        console.log('Using current auth token as fallback after API error');
        isRefreshing = false;
        return currentToken;
      }

      throw apiError;
    }
  } catch (error) {
    console.error('Error refreshing access token:', error);
    isRefreshing = false;
    throw error;
  }
};

/**
 * Setup axios interceptors to handle token refresh
 */
export const setupAxiosInterceptors = () => {
  // Request interceptor
  axios.interceptors.request.use(
    async (config) => {
      try {
        // Add auth token to request if available
        const token = await getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('Error in request interceptor:', error);
        // Continue with the request even if we can't add the token
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor
  axios.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      // If error is 401 and we haven't tried to refresh the token yet
      if (
        error.response &&
        error.response.status === 401 &&
        !originalRequest._retry
      ) {
        originalRequest._retry = true;

        try {
          // Refresh the token
          const newToken = await refreshAccessToken();

          // Update the authorization header
          originalRequest.headers.Authorization = `Bearer ${newToken}`;

          // Retry the original request
          return axios(originalRequest);
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);

          // Check if we still have a valid auth token
          try {
            const currentToken = await getAuthToken();
            if (currentToken) {
              console.log('Using existing token for retry after refresh failure');
              originalRequest.headers.Authorization = `Bearer ${currentToken}`;
              return axios(originalRequest);
            }
          } catch (tokenError) {
            console.error('Error getting current token:', tokenError);
          }

          return Promise.reject(refreshError);
        }
      }

      return Promise.reject(error);
    }
  );
};
