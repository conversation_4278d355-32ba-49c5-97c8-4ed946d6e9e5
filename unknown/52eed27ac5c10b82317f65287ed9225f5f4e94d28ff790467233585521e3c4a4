import axios from 'axios';
import { API_URL, USER_TYPES } from '../../config/constants';
import { getAuthToken, getUserData } from '../authStorage';

// Get all products
export const getAllProducts = async () => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('ProductAPI: User type:', userType);

        // Only admins should be able to fetch admin products
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch admin products');
            throw new Error('Unauthorized: Admin access required');
        }

        console.log('Fetching products from:', `${API_URL}/admin/products`);
        console.log('Auth token available:', !!token);

        const response = await axios.get(`${API_URL}/admin/products`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        console.log('Products API response status:', response.status);
        console.log('Products API response data type:', typeof response.data);
        console.log('Products API response data keys:', Object.keys(response.data || {}));

        return response.data;
    } catch (error) {
        console.error('Error fetching products:', error);
        console.error('Error details:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url
        });
        throw error;
    }
};

// Get product by ID
export const getProductById = async (productId) => {
    try {
        if (!productId) {
            console.error('Invalid product ID provided:', productId);
            throw new Error('Invalid product ID provided');
        }

        console.log('Fetching product details for ID:', productId);

        const token = await getAuthToken();
        console.log('Auth token available:', !!token);

        const response = await axios.get(`${API_URL}/admin/products/${productId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        console.log('Product API response status:', response.status);

        if (response.data && response.data.product) {
            console.log('Product found:', response.data.product.name);
            return response.data;
        } else {
            console.error('Product API returned unexpected data format:', response.data);
            throw new Error('Product data not found in API response');
        }
    } catch (error) {
        console.error(`Error fetching product ${productId}:`, error);
        console.error('Error details:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url
        });

        // If it's a 404, provide a more specific error
        if (error.response && error.response.status === 404) {
            const errorMessage = error.response.data?.message || 'Product not found';
            console.error('Product not found:', errorMessage);

            // Log available product IDs if provided in the error response
            if (error.response.data?.availableIds) {
                console.log('Available products:', error.response.data.availableIds);
            }

            throw new Error(errorMessage);
        }

        throw error;
    }
};

// Create product
export const createProduct = async (productData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('ProductAPI: User type for createProduct:', userType);

        // Only admins should be able to create products
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can create products');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.post(`${API_URL}/admin/products`, productData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error creating product:', error);
        throw error;
    }
};

// Update product
export const updateProduct = async (productId, productData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('ProductAPI: User type for updateProduct:', userType);

        // Only admins should be able to update products
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can update products');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.put(`${API_URL}/admin/products/${productId}`, productData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error updating product ${productId}:`, error);
        throw error;
    }
};

// Delete product
export const deleteProduct = async (productId) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('ProductAPI: User type for deleteProduct:', userType);

        // Only admins should be able to delete products
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can delete products');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.delete(`${API_URL}/admin/products/${productId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error deleting product ${productId}:`, error);
        throw error;
    }
};
