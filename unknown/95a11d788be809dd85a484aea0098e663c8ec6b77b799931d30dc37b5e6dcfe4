/**
 * This script seeds the database with initial data for testing.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');
const Product = require('../models/Product');
const Category = require('../models/Category');
const User = require('../models/User');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Connect to MongoDB
async function seedDatabase() {
  try {
    // Set mongoose options
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 30000,
      connectTimeoutMS: 30000
    };

    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI, options);
    console.log(`MongoDB Connected: ${mongoose.connection.host}`);

    // Create categories
    console.log('Creating categories...');
    const categories = [
      {
        name: 'Chicken',
        description: 'Fresh chicken products',
        image: 'https://via.placeholder.com/150'
      },
      {
        name: 'Mutton',
        description: 'Fresh mutton products',
        image: 'https://via.placeholder.com/150'
      },
      {
        name: 'Seafood',
        description: 'Fresh seafood products',
        image: 'https://via.placeholder.com/150'
      }
    ];

    // Clear existing categories
    await Category.deleteMany({});
    
    // Insert new categories
    const createdCategories = await Category.insertMany(categories);
    console.log(`Created ${createdCategories.length} categories`);

    // Create products
    console.log('Creating products...');
    const products = [];
    
    // Add products for each category
    for (let i = 0; i < createdCategories.length; i++) {
      const category = createdCategories[i];
      
      // Create 3 products per category
      for (let j = 1; j <= 3; j++) {
        const price = Math.floor(Math.random() * 300) + 100; // Random price between 100 and 400
        const discountPercentage = Math.floor(Math.random() * 20); // Random discount between 0 and 20%
        const discountPrice = price * (1 - (discountPercentage / 100));
        
        products.push({
          name: `${category.name} Product ${j}`,
          description: `This is a ${category.name.toLowerCase()} product ${j}`,
          price: price,
          discount_price: discountPrice,
          discountPercentage: discountPercentage,
          weight: '500g',
          pieces: '1 piece',
          isAvailable: true,
          stock: Math.floor(Math.random() * 50) + 10, // Random stock between 10 and 60
          offer: discountPercentage > 0 ? `${discountPercentage}% OFF` : '',
          reward_points: Math.floor(price / 10),
          category: category._id,
          image: 'https://via.placeholder.com/300',
          id: `PROD${i}${j}`
        });
      }
    }

    // Clear existing products
    await Product.deleteMany({});
    
    // Insert new products
    const createdProducts = await Product.insertMany(products);
    console.log(`Created ${createdProducts.length} products`);

    // Update categories with products
    for (const product of createdProducts) {
      await Category.findByIdAndUpdate(product.category, {
        $push: { products: product._id }
      });
    }

    // Create admin user if it doesn't exist
    const adminUser = await User.findOne({ phoneNumber: '8825549901' });
    if (!adminUser) {
      console.log('Creating admin user...');
      const newAdmin = new User({
        name: 'Admin',
        phoneNumber: '8825549901',
        email: '<EMAIL>',
        role: 'admin',
        isVerified: true
      });
      await newAdmin.save();
      console.log('Admin user created');
    } else {
      console.log('Admin user already exists');
    }

    console.log('Database seeded successfully!');
    console.log('You can now start your server with: npm start');
    
    await mongoose.disconnect();
  } catch (error) {
    console.error('Error seeding database:');
    console.error(`Error message: ${error.message}`);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedDatabase();
