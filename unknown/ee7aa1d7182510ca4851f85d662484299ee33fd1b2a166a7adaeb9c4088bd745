import axios from 'axios';
import { API_URL, USER_TYPES } from '../../config/constants';
import { getAuthToken, getUserData } from '../authStorage';

// Get all users
export const getAllUsers = async () => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('UserAPI: User type:', userType);

        // Only admins should be able to fetch all users
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch all users');
            throw new Error('Unauthorized: Admin access required');
        }

        console.log('Fetching users from:', `${API_URL}/admin/users`);
        console.log('Auth token available:', !!token);

        const response = await axios.get(`${API_URL}/admin/users`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        console.log('Users API response status:', response.status);
        console.log('Users API response data type:', typeof response.data);
        console.log('Users API response data keys:', Object.keys(response.data || {}));

        return response.data;
    } catch (error) {
        console.error('Error fetching users:', error);
        console.error('Error details:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url
        });
        throw error;
    }
};

// Get user by ID
export const getUserById = async (userId) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('UserAPI: User type for getUserById:', userType);

        // Only admins should be able to fetch user by ID through admin API
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch user by ID through admin API');
            throw new Error('Unauthorized: Admin access required');
        }

        try {
            // Try to fetch from admin/users/:userId endpoint
            const response = await axios.get(`${API_URL}/admin/users/${userId}`, {
                headers: {
                    Authorization: `Bearer ${token}`
                },
                timeout: 10000 // 10 second timeout
            });
            return response.data;
        } catch (directError) {
            console.log(`Direct user fetch failed, trying to find user in all users: ${directError.message}`);

            // If direct fetch fails, try to get all users and find the user
            const allUsersResponse = await getAllUsers();

            if (allUsersResponse && allUsersResponse.users && allUsersResponse.users.length > 0) {
                // Find the user in the list by ID
                const foundUser = allUsersResponse.users.find(u =>
                    (u.id === userId || u._id === userId ||
                     (u._id && u._id.toString() === userId) ||
                     (u.id && u.id.toString() === userId))
                );

                if (foundUser) {
                    console.log('User found in all users list');
                    return { user: foundUser };
                }
            }

            // If we still can't find the user, throw the original error
            throw directError;
        }
    } catch (error) {
        console.error(`Error fetching user ${userId}:`, error);
        throw error;
    }
};

// Update user
export const updateUser = async (userId, userData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData2 = await getUserData();
        const userType = userData2?.userType?.toUpperCase();

        console.log('UserAPI: User type for updateUser:', userType);

        // Only admins should be able to update users through admin API
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can update users through admin API');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.put(`${API_URL}/admin/users/${userId}`, userData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error updating user ${userId}:`, error);
        throw error;
    }
};

// Delete user
export const deleteUser = async (userId) => {
    try {
        const token = await getAuthToken();
        const response = await axios.delete(`${API_URL}/admin/users/${userId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error deleting user ${userId}:`, error);
        throw error;
    }
};
