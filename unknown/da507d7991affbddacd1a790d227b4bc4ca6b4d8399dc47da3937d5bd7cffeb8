import React, { useState, useEffect, useCallback } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    ScrollView,
    Alert,
    ActivityIndicator,
    KeyboardAvoidingView,
    Platform
} from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { useAdminDeliveryPartner } from '../../context/AdminDeliveryPartnerContext';
import { getDeliveryPartnerById, updateDeliveryPartner, createDeliveryPartner } from '../../utils/api/deliveryPartnerApi';

const EditDeliveryPartnerScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { partnerId, isNewPartner, onGoBack } = route.params || {};

    const { refreshData: refreshContextData, deliveryPartners } = useAdminDeliveryPartner();

    const [loading, setLoading] = useState(!isNewPartner);
    const [error, setError] = useState(null);
    const [partner, setPartner] = useState(null);
    const [formData, setFormData] = useState({
        name: '',
        phone: '',
        email: '',
        vehicleType: 'Two Wheeler',
        vehicleNumber: '',
        location: 'Chennai'
    });

    // Function to fetch partner data from API
    const fetchPartnerData = useCallback(async () => {
        if (isNewPartner) {
            // Creating a new partner, no need to fetch data
            setLoading(false);
            return;
        }

        try {
            setError(null);
            setLoading(true);
            console.log(`Fetching delivery partner ${partnerId} data for editing`);

            // Fetch partner data from API
            const response = await getDeliveryPartnerById(partnerId);

            if (response && response.deliveryPartner) {
                const partnerData = response.deliveryPartner;
                console.log('Partner data fetched successfully for editing:', partnerData.name);
                setPartner(partnerData);
                setFormData({
                    name: partnerData.name || '',
                    phone: partnerData.phone || partnerData.phoneNumber || '',
                    email: partnerData.email || '',
                    vehicleType: partnerData.vehicleType || 'Two Wheeler',
                    vehicleNumber: partnerData.vehicleNumber || '',
                    location: partnerData.location || 'Chennai'
                });
            } else {
                console.error('No partner data returned from API for editing');
                setError('Could not fetch partner data');
            }
        } catch (err) {
            console.error('Error fetching partner data for editing:', err);
            setError('Failed to load partner data. Please try again.');
        } finally {
            setLoading(false);
        }
    }, [partnerId, isNewPartner]);

    // Fetch data when screen comes into focus
    useFocusEffect(
        useCallback(() => {
            fetchPartnerData();
        }, [fetchPartnerData])
    );

    if (loading) {
        return (
            <View className="flex-1 bg-snow justify-center items-center">
                <ActivityIndicator size="large" color="#A31621" />
            </View>
        );
    }

    if (error || (!isNewPartner && !partner)) {
        return (
            <View className="flex-1 bg-snow">
                <View className="bg-madder p-6 pt-16 pb-6">
                    <View className="flex-row items-center">
                        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                            <MaterialIcons name="arrow-back" size={24} color="white" />
                        </TouchableOpacity>
                        <Text className="text-2xl text-white font-bold">{isNewPartner ? 'Add Partner' : 'Edit Partner'}</Text>
                    </View>
                </View>

                <View className="flex-1 justify-center items-center p-6">
                    <MaterialIcons name="error-outline" size={64} color="#A31621" />
                    <Text className="text-lg text-gray-700 mt-4 text-center">
                        {error || "Delivery partner not found"}
                    </Text>
                    <View className="flex-row mt-6">
                        <TouchableOpacity
                            className="mr-2 bg-madder px-6 py-2 rounded-full"
                            onPress={() => navigation.goBack()}
                        >
                            <Text className="text-white font-medium">Go Back</Text>
                        </TouchableOpacity>
                        {!isNewPartner && (
                            <TouchableOpacity
                                className="ml-2 bg-blue-500 px-6 py-2 rounded-full"
                                onPress={fetchPartnerData}
                            >
                                <Text className="text-white font-medium">Try Again</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
            </View>
        );
    }

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const validateForm = () => {
        if (!formData.name.trim()) {
            Alert.alert("Error", "Name is required");
            return false;
        }

        if (!formData.phone.trim()) {
            Alert.alert("Error", "Phone number is required");
            return false;
        }

        if (formData.phone.trim().length !== 10 || !/^\d+$/.test(formData.phone.trim())) {
            Alert.alert("Error", "Please enter a valid 10-digit phone number");
            return false;
        }

        if (formData.email.trim() && !/\S+@\S+\.\S+/.test(formData.email.trim())) {
            Alert.alert("Error", "Please enter a valid email address");
            return false;
        }

        if (!formData.vehicleNumber.trim()) {
            Alert.alert("Error", "Vehicle number is required");
            return false;
        }

        // Vehicle number validation (format: "TN 23 CK 1234")
        const vehicleNumberRegex = /^[A-Z]{2} \d{2} [A-Z]{2} \d{4}$/;
        if (!vehicleNumberRegex.test(formData.vehicleNumber)) {
            Alert.alert("Error", "Vehicle number must be in the format: TN 23 CK 1234");
            return false;
        }

        return true;
    };

    const handleSave = async () => {
        if (!validateForm()) return;

        setLoading(true);

        try {
            const updatedData = {
                name: formData.name.trim(),
                phoneNumber: formData.phone.trim(), // API expects phoneNumber
                email: formData.email.trim(),
                vehicleType: "Two Wheeler", // Always set to "Two Wheeler"
                vehicleNumber: formData.vehicleNumber.trim(),
                location: formData.location.trim()
            };

            if (isNewPartner) {
                // Create a new partner with default values
                const newPartnerId = `DP${String(deliveryPartners.length + 1).padStart(3, '0')}`;

                const newPartnerData = {
                    ...updatedData,
                    id: newPartnerId,
                    joinedDate: new Date().toISOString(),
                    totalDeliveries: 0,
                    rating: 5.0,
                    isAvailable: true,
                    earnings: 0,
                    completedOrders: 0,
                    cancelledOrders: 0,
                    lastActive: new Date().toISOString(),
                    inTransitOrders: 0
                };

                console.log('Creating new delivery partner:', newPartnerData);
                const response = await createDeliveryPartner(newPartnerData);
                console.log('Create response:', response);

                // Refresh context data
                refreshContextData();

                Alert.alert(
                    "Success",
                    "New delivery partner added successfully",
                    [{
                        text: "OK",
                        onPress: () => {
                            // Call the onGoBack callback if provided
                            if (onGoBack) {
                                onGoBack();
                            }
                            navigation.goBack();
                        }
                    }]
                );
            } else {
                // Update existing partner
                console.log(`Updating delivery partner ${partnerId}:`, updatedData);
                const response = await updateDeliveryPartner(partnerId, updatedData);
                console.log('Update response:', response);

                // Refresh context data
                refreshContextData();

                Alert.alert(
                    "Success",
                    "Delivery partner profile updated successfully",
                    [{
                        text: "OK",
                        onPress: () => {
                            // Call the onGoBack callback if provided
                            if (onGoBack) {
                                onGoBack();
                            }
                            navigation.goBack();
                        }
                    }]
                );
            }
        } catch (error) {
            console.error('Error saving delivery partner:', error);
            Alert.alert(
                "Error",
                "Failed to save delivery partner. Please try again.",
                [{ text: "OK" }]
            );
        } finally {
            setLoading(false);
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            className="flex-1 bg-snow"
        >
            <View className="bg-madder p-6 pt-16 pb-10 rounded-b-3xl">
                <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center">
                        <TouchableOpacity
                            onPress={() => navigation.goBack()}
                            className="mr-4 bg-white/20 p-2 rounded-full"
                        >
                            <MaterialIcons name="arrow-back" size={24} color="white" />
                        </TouchableOpacity>
                        <Text className="text-2xl text-white font-bold">{isNewPartner ? 'Add Partner' : 'Edit Partner'}</Text>
                    </View>

                    {!isNewPartner && (
                        <View className="bg-white/20 px-3 py-1 rounded-full">
                            <Text className="text-white font-medium">ID: {partner?.id || partner?._id}</Text>
                        </View>
                    )}
                </View>
            </View>

            <ScrollView
                className="flex-1 p-4"
                showsVerticalScrollIndicator={false}
            >


                {/* All Information in One Card */}
                <View className="mt-4">
                    {/* Name Field */}
                    <View className="mb-6">
                        <View className="flex-row items-center mb-2">
                            <MaterialIcons name="person" size={22} color="#A31621" />
                            <Text className="text-gray-700 ml-2 font-medium text-lg">Full Name</Text>
                        </View>
                        <View className="relative">
                            <TextInput
                                className="bg-white p-5 rounded-2xl text-gray-800 shadow-sm border border-gray-100"
                                value={formData.name}
                                onChangeText={(text) => handleInputChange('name', text)}
                                placeholder="Enter partner's full name"
                                placeholderTextColor="#9CA3AF"
                            />
                            <View className="absolute right-4 top-5">
                                {formData.name ? (
                                    <MaterialIcons name="check-circle" size={20} color="#10B981" />
                                ) : (
                                    <MaterialIcons name="info-outline" size={20} color="#9CA3AF" />
                                )}
                            </View>
                        </View>
                    </View>

                    {/* Phone Field */}
                    <View className="mb-6">
                        <View className="flex-row items-center mb-2">
                            <MaterialIcons name="phone" size={22} color="#A31621" />
                            <Text className="text-gray-700 ml-2 font-medium text-lg">Phone Number</Text>
                        </View>
                        <View className="relative">
                            <TextInput
                                className="bg-white p-5 rounded-2xl text-gray-800 shadow-sm border border-gray-100"
                                value={formData.phone}
                                onChangeText={(text) => {
                                    // Only allow digits
                                    const formattedText = text.replace(/[^0-9]/g, '');
                                    handleInputChange('phone', formattedText);
                                }}
                                placeholder="Enter 10-digit phone number"
                                placeholderTextColor="#9CA3AF"
                                keyboardType="phone-pad"
                                maxLength={10}
                            />
                            <View className="absolute right-4 top-5">
                                {formData.phone && formData.phone.length === 10 ? (
                                    <MaterialIcons name="check-circle" size={20} color="#10B981" />
                                ) : (
                                    <View className="flex-row items-center">
                                        <MaterialIcons name="info-outline" size={20} color="#9CA3AF" />
                                        {formData.phone ? (
                                            <Text className="text-gray-500 text-xs ml-1">{formData.phone.length}/10</Text>
                                        ) : null}
                                    </View>
                                )}
                            </View>
                        </View>
                    </View>

                    {/* Email Field */}
                    <View className="mb-6">
                        <View className="flex-row items-center mb-2">
                            <MaterialIcons name="email" size={22} color="#A31621" />
                            <Text className="text-gray-700 ml-2 font-medium text-lg">Email Address</Text>
                        </View>
                        <View className="relative">
                            <TextInput
                                className="bg-white p-5 rounded-2xl text-gray-800 shadow-sm border border-gray-100"
                                value={formData.email}
                                onChangeText={(text) => handleInputChange('email', text)}
                                placeholder="Enter email address (optional)"
                                placeholderTextColor="#9CA3AF"
                                keyboardType="email-address"
                                autoCapitalize="none"
                            />
                            <View className="absolute right-4 top-5">
                                <Text className="text-gray-400 text-xs font-medium">Optional</Text>
                            </View>
                        </View>
                    </View>

                    {/* Vehicle Type Field - Non-editable */}
                    <View className="mb-6">
                        <View className="flex-row items-center mb-2">
                            <MaterialIcons name="motorcycle" size={22} color="#A31621" />
                            <Text className="text-gray-700 ml-2 font-medium text-lg">Vehicle Type</Text>
                            <View className="ml-2 px-2 py-1 bg-gray-100 rounded-full">
                                <Text className="text-xs text-gray-500">Non-editable</Text>
                            </View>
                        </View>
                        <View className="relative">
                            <View className="bg-gray-50 p-5 rounded-2xl border border-gray-100 flex-row justify-between items-center">
                                <Text className="text-gray-800">Two Wheeler</Text>
                                <MaterialIcons name="lock" size={18} color="#9CA3AF" />
                            </View>
                        </View>
                    </View>

                    {/* Vehicle Number Field */}
                    <View className="mb-6">
                        <View className="flex-row items-center mb-2">
                            <MaterialIcons name="directions-car" size={22} color="#A31621" />
                            <Text className="text-gray-700 ml-2 font-medium text-lg">Vehicle Number</Text>
                        </View>
                        <View className="relative">
                            <TextInput
                                className="bg-white p-5 rounded-2xl text-gray-800 shadow-sm border border-gray-100"
                                value={formData.vehicleNumber}
                                onChangeText={(text) => {
                                    // Format as "TN 23 CK 1234" while typing
                                    let formatted = text.toUpperCase();

                                    // Remove any non-alphanumeric characters
                                    formatted = formatted.replace(/[^A-Z0-9]/g, '');

                                    // Add spaces in the correct positions
                                    if (formatted.length > 2) {
                                        formatted = formatted.slice(0, 2) + ' ' + formatted.slice(2);
                                    }
                                    if (formatted.length > 5) {
                                        formatted = formatted.slice(0, 5) + ' ' + formatted.slice(5);
                                    }
                                    if (formatted.length > 8) {
                                        formatted = formatted.slice(0, 8) + ' ' + formatted.slice(8);
                                    }

                                    // Limit to the correct length (2+2+2+4 chars + 3 spaces = 13)
                                    if (formatted.length > 13) {
                                        formatted = formatted.slice(0, 13);
                                    }

                                    handleInputChange('vehicleNumber', formatted);
                                }}
                                placeholder="Format: TN 23 CK 1234"
                                placeholderTextColor="#9CA3AF"
                                autoCapitalize="characters"
                            />
                            <View className="absolute right-4 top-5">
                                {/^[A-Z]{2} \d{2} [A-Z]{2} \d{4}$/.test(formData.vehicleNumber) ? (
                                    <MaterialIcons name="check-circle" size={20} color="#10B981" />
                                ) : (
                                    <MaterialIcons name="info-outline" size={20} color="#9CA3AF" />
                                )}
                            </View>
                        </View>
                    </View>

                    {/* Location Field */}
                    <View className="mb-6">
                        <View className="flex-row items-center mb-2">
                            <MaterialIcons name="location-on" size={22} color="#A31621" />
                            <Text className="text-gray-700 ml-2 font-medium text-lg">Current Location</Text>
                        </View>
                        <View className="relative">
                            <TextInput
                                className="bg-white p-5 rounded-2xl text-gray-800 shadow-sm border border-gray-100"
                                value={formData.location}
                                onChangeText={(text) => handleInputChange('location', text)}
                                placeholder="Enter current location (optional)"
                                placeholderTextColor="#9CA3AF"
                            />
                            <View className="absolute right-4 top-5">
                                <Text className="text-gray-400 text-xs font-medium">Optional</Text>
                            </View>
                        </View>
                    </View>
                </View>



                {/* Action Buttons */}
                <View className="mb-8 mt-4">
                    <TouchableOpacity
                        className={`bg-madder py-3 px-6 rounded-xl items-center shadow-md ${loading ? 'opacity-70' : ''} self-center`}
                        onPress={handleSave}
                        disabled={loading}
                        style={{
                            shadowColor: "#A31621",
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.15,
                            shadowRadius: 4,
                            elevation: 3
                        }}
                    >
                        {loading ? (
                            <ActivityIndicator size="small" color="white" />
                        ) : (
                            <View className="flex-row items-center">
                                <MaterialIcons name="save" size={18} color="white" />
                                <Text className="font-bold text-white ml-2">
                                    {isNewPartner ? 'Add Partner' : 'Save Changes'}
                                </Text>
                            </View>
                        )}
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="py-4 items-center mt-4"
                        onPress={() => navigation.goBack()}
                        disabled={loading}
                    >
                        <Text className="font-medium text-gray-600">Cancel and go back</Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default EditDeliveryPartnerScreen;
