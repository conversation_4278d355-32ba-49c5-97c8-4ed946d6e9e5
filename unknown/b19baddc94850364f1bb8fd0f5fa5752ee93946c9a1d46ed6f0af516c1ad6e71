const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Admin = require('../models/Admin');

// Load environment variables
dotenv.config();
console.log('MongoDB URI:', process.env.MONGO_URI ? 'URI is set' : 'URI is not set');

// Admin data with the specific phone number
const adminData = {
    name: 'Admin User',
    phoneNumber: '8825549901', // The specific admin phone number
    email: '<EMAIL>',
    role: 'ADMIN'
};

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
    .then(async () => {
        console.log('MongoDB Connected');
        
        try {
            // Check if admin already exists
            const existingAdmin = await Admin.findOne({ phoneNumber: adminData.phoneNumber });
            
            if (existingAdmin) {
                console.log('Admin already exists with phone number 8825549901:', existingAdmin);
                process.exit(0);
            }
            
            // Create new admin
            const admin = new Admin(adminData);
            const savedAdmin = await admin.save();
            console.log('Admin created with phone number 8825549901:', savedAdmin);
            
            process.exit(0);
        } catch (error) {
            console.error('Error:', error);
            process.exit(1);
        }
    })
    .catch(err => {
        console.error('MongoDB connection error:', err);
        process.exit(1);
    });
