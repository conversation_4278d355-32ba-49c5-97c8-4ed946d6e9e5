/**
 * Migration script to convert coins from array to number
 * Run this script with: node backend/scripts/migrateCoins.js
 */

const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB with direct URI
const MONGO_URI = 'mongodb+srv://MeatHub:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

mongoose.connect(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected for migration'))
.catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
});

async function migrateCoins() {
    try {
        console.log('Starting coins migration...');
        
        // Find all users
        const users = await User.find({}).lean();
        
        console.log(`Found ${users.length} users to check for coins migration`);
        
        let migratedCount = 0;
        let alreadyMigratedCount = 0;
        let errorCount = 0;
        
        for (const user of users) {
            try {
                // Check if coins is an array
                if (Array.isArray(user.coins)) {
                    console.log(`Migrating coins for user: ${user._id} (${user.name || 'unnamed'})`);
                    
                    // Calculate total coins from the array
                    let totalCoins = 0;
                    if (user.coins && user.coins.length > 0) {
                        totalCoins = user.coins.reduce((sum, coin) => sum + (coin.amount || 0), 0);
                    }
                    
                    // Update the user with the new coins value
                    await User.updateOne(
                        { _id: user._id },
                        { $set: { coins: totalCoins } }
                    );
                    
                    migratedCount++;
                    console.log(`  - Migrated coins: ${totalCoins}`);
                } else if (typeof user.coins === 'number') {
                    alreadyMigratedCount++;
                    console.log(`User ${user._id} already has coins as a number: ${user.coins}`);
                } else if (user.coins === undefined) {
                    // Set coins to 0 for users without coins
                    await User.updateOne(
                        { _id: user._id },
                        { $set: { coins: 0 } }
                    );
                    migratedCount++;
                    console.log(`  - Set coins to 0 for user without coins field`);
                } else {
                    console.log(`User ${user._id} has coins in an unexpected format:`, user.coins);
                    errorCount++;
                }
            } catch (error) {
                console.error(`Error migrating coins for user ${user._id}:`, error);
                errorCount++;
            }
        }
        
        console.log('\nMigration Summary:');
        console.log(`- ${migratedCount} users had their coins migrated from array to number`);
        console.log(`- ${alreadyMigratedCount} users already had coins as a number`);
        console.log(`- ${errorCount} users encountered errors during migration`);
        console.log('\nMigration completed!');
        
    } catch (error) {
        console.error('Error during migration:', error);
    } finally {
        // Close the MongoDB connection
        mongoose.connection.close();
        console.log('MongoDB connection closed');
    }
}

// Run the migration
migrateCoins();
