import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Animated, Switch, ActivityIndicator, Alert, Modal } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useUser } from '../context/UserContext';
import { useLocation } from '../context/LocationContext';
import * as Location from 'expo-location';
import { isValidPincode, isWithinDeliveryZone, getPincodeMessage } from '../utils/locationUtils';
import DeliveryLocationPicker from '../Components/DeliveryLocationPicker';
import LocationMapView from '../Components/MapView';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import PincodeAlert from '../Components/PincodeAlert';

const AddEditAddressScreen = ({ route }) => {
    const navigation = useNavigation();
    const { currentUser, addUserAddress, updateUserAddress, updateUserProfile } = useUser();

    const mode = route.params?.mode || 'add';
    const existingAddress = route.params?.address;
    const isMainAddress = route.params?.isMainAddress || false;

    const [addressType, setAddressType] = useState(existingAddress?.type || 'Home');

    // Use 5-field address structure
    const [doorNo, setDoorNo] = useState(existingAddress?.doorNo || '');
    const [streetName, setStreetName] = useState(existingAddress?.streetName || '');
    const [area, setArea] = useState(existingAddress?.area || '');
    const [district, setDistrict] = useState(existingAddress?.district || '');
    const [pincode, setPincode] = useState(existingAddress?.pincode || '');

    // Location coordinates
    const [latitude, setLatitude] = useState(existingAddress?.latitude || null);
    const [longitude, setLongitude] = useState(existingAddress?.longitude || null);
    const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
    const [isWithinZone, setIsWithinZone] = useState(true);
    const [isLoadingLocation, setIsLoadingLocation] = useState(false);

    // Map view state
    const [showMapPicker, setShowMapPicker] = useState(false);
    const [showMapView, setShowMapView] = useState(false);
    const [selectedLocation, setSelectedLocation] = useState(
        existingAddress?.latitude && existingAddress?.longitude
            ? { latitude: existingAddress.latitude, longitude: existingAddress.longitude }
            : null
    );

    // For primary address
    const [isPrimary, setIsPrimary] = useState(isMainAddress || existingAddress?.isPrimary || false);

    const [errors, setErrors] = useState({});

    // Animation for toast notification
    const toastAnimation = useRef(new Animated.Value(0)).current;
    const [toastVisible, setToastVisible] = useState(false);
    const [toastMessage, setToastMessage] = useState('');
    const [toastType, setToastType] = useState('success');

    // Use the location context
    const { locationPermissionStatus, requestPermission, getLocation: getContextLocation } = useLocation();

    // Check if existing address is within delivery zone when component mounts
    useEffect(() => {
        if (existingAddress?.latitude && existingAddress?.longitude) {
            checkDeliveryZone(existingAddress.latitude, existingAddress.longitude);
        }

        // Update local permission state based on context
        setLocationPermissionGranted(locationPermissionStatus === 'granted');
    }, [locationPermissionStatus]);

    // Get current location
    const getLocation = async () => {
        try {
            setIsLoadingLocation(true);

            // If permission is not granted, request it
            if (locationPermissionStatus !== 'granted') {
                const granted = await requestPermission();
                if (!granted) {
                    Alert.alert(
                        "Location Permission Required",
                        "We need your location to provide accurate delivery service. Please enable location services for this app in your device settings."
                    );
                    return;
                }
            }

            // Get location from context
            const location = await getContextLocation();

            if (location) {
                setLatitude(location.latitude);
                setLongitude(location.longitude);
                setSelectedLocation(location);

                // Check if the location is within delivery zone
                checkDeliveryZone(location.latitude, location.longitude);

                // Show the map picker
                setShowMapPicker(true);

                showToast("Location captured successfully", "success");
            } else {
                showToast("Could not get your location", "error");
            }
        } catch (error) {
            console.error('Error getting location:', error);
            showToast("Error getting your location", "error");
        } finally {
            setIsLoadingLocation(false);
        }
    };

    // Handle location selection from the map
    const handleLocationSelected = (location) => {
        if (location) {
            setLatitude(location.latitude);
            setLongitude(location.longitude);
            setSelectedLocation(location);

            // Check if the location is within delivery zone
            checkDeliveryZone(location.latitude, location.longitude);
        }
    };

    // Check if location is within delivery zone
    const checkDeliveryZone = (lat, lng) => {
        if (!lat || !lng) return;

        const locationObj = { latitude: lat, longitude: lng };
        const withinZone = isWithinDeliveryZone(locationObj);
        setIsWithinZone(withinZone);

        if (!withinZone) {
            Alert.alert(
                "Outside Delivery Zone",
                "The selected location appears to be outside our delivery zone. You can still save this address, but we may not be able to deliver to this location."
            );
        }
    };

    // Pincode validation state
    const [showPincodeAlert, setShowPincodeAlert] = useState(false);
    const [pincodeMessage, setPincodeMessage] = useState(null);
    const [canSaveAddress, setCanSaveAddress] = useState(true);

    // Check if pincode is valid with enhanced messaging
    const checkPincode = (code) => {
        setPincode(code);

        if (code.length === 6) {
            const message = getPincodeMessage(code);
            setPincodeMessage(message);
            setCanSaveAddress(message.type === 'success');

            if (message.type !== 'success') {
                setErrors(prev => ({
                    ...prev,
                    pincode: "Service not available in this pincode"
                }));
                setIsWithinZone(false);
                setShowPincodeAlert(true);
            } else {
                setErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors.pincode;
                    return newErrors;
                });
                setIsWithinZone(true);
            }
        }
    };

    // Show toast notification
    const showToast = (message, type = 'success') => {
        setToastMessage(message);
        setToastType(type);
        setToastVisible(true);

        Animated.sequence([
            // Animate in
            Animated.timing(toastAnimation, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            // Hold
            Animated.delay(2000),
            // Animate out
            Animated.timing(toastAnimation, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            })
        ]).start(() => {
            setToastVisible(false);
        });
    };

    const validate = () => {
        let tempErrors = {};

        if (!doorNo.trim()) tempErrors.doorNo = "Door/Flat number is required";
        if (!streetName.trim()) tempErrors.streetName = "Street name is required";
        if (!area.trim()) tempErrors.area = "Area/Locality is required";
        if (!district.trim()) tempErrors.district = "District is required";
        if (!pincode.trim()) tempErrors.pincode = "Pincode is required";
        else if (!/^\d{6}$/.test(pincode)) tempErrors.pincode = "Pincode must be 6 digits";

        // Log validation results for debugging
        console.log('Validating address fields:', {
            doorNo: doorNo.trim() ? 'Valid' : 'Invalid',
            streetName: streetName.trim() ? 'Valid' : 'Invalid',
            area: area.trim() ? 'Valid' : 'Invalid',
            district: district.trim() ? 'Valid' : 'Invalid',
            pincode: pincode.trim() && /^\d{6}$/.test(pincode) ? 'Valid' : 'Invalid'
        });

        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    };

    const [isLoading, setIsLoading] = useState(false);

    const handleSave = async () => {
        // Check if address can be saved (valid pincode)
        if (!canSaveAddress) {
            const message = getPincodeMessage(pincode.trim());
            setPincodeMessage(message);
            setShowPincodeAlert(true);
            return;
        }

        if (validate()) {
            setIsLoading(true);

            try {
                // Create full address string in a consistent format
                const fullAddress = `${doorNo}, ${streetName}, ${area}, ${district}, ${pincode}`;

                const addressData = {
                    type: addressType,
                    addressType: addressType, // Add addressType field to match schema
                    doorNo: doorNo.trim(),
                    streetName: streetName.trim(),
                    area: area.trim(),
                    district: district.trim(),
                    pincode: pincode.trim(),
                    fullAddress: fullAddress.trim(), // Add full address for backward compatibility
                    // Include coordinates in both formats for compatibility
                    coordinates: {
                        latitude: latitude || null,
                        longitude: longitude || null
                    },
                    latitude: latitude || null,
                    longitude: longitude || null,
                    isPrimary,
                    isWithinDeliveryZone: isWithinZone,
                    allowDuplicate: true // Always allow adding duplicate addresses
                };

                console.log('Saving address data:', addressData);

                if (isMainAddress) {
                    // If this is the main address, update the user profile
                    console.log('Updating main address in user profile');

                    // Create the address object for the user profile
                    // Include the user's name since it's required by the API
                    const profileUpdate = {
                        name: currentUser?.name || '', // Include the current name
                        address: {
                            doorNo: doorNo.trim(),
                            streetName: streetName.trim(),
                            area: area.trim(),
                            district: district.trim(),
                            pincode: pincode.trim(),
                            fullAddress: fullAddress.trim(),
                            addressType: addressType, // Include addressType
                            // Include coordinates in both formats for compatibility
                            coordinates: {
                                latitude: latitude || null,
                                longitude: longitude || null
                            },
                            latitude: latitude || null,
                            longitude: longitude || null,
                            isPrimary: true, // Set as primary address
                            isWithinDeliveryZone: isWithinZone
                        }
                    };

                    console.log('Updating main address with data:', profileUpdate);

                    // Update the user profile with the new address
                    // This will also update the primary address in the addresses array
                    await updateUserProfile(profileUpdate);
                    console.log('Main address updated in user profile');

                    showToast("Main address updated successfully");
                } else if (mode === 'add') {
                    // Regular add address
                    console.log('Adding new address');
                    await addUserAddress(addressData);
                    showToast("Address added successfully");
                } else {
                    // Regular update address
                    console.log('Updating address with ID:', existingAddress._id || existingAddress.id);
                    await updateUserAddress(existingAddress._id || existingAddress.id, addressData);
                    showToast("Address updated successfully");
                }

                // Wait for toast to show before navigating back
                setTimeout(() => {
                    navigation.goBack();
                }, 2000);
            } catch (error) {
                console.error('Error saving address:', error);

                // Provide more specific error messages
                let errorMessage = "Failed to save address. Please try again.";

                // Check if we have a response with error details
                if (error.response) {
                    console.error('Error response status:', error.response.status);
                    console.error('Error response data:', error.response.data);

                    if (error.response.data && error.response.data.message) {
                        errorMessage = error.response.data.message;

                        // Special handling for main address update errors
                        if (isMainAddress && errorMessage === "Name is required") {
                            errorMessage = "Failed to update main address. Please try again or contact support.";
                            console.error("Main address update failed - name is required but was missing or empty");
                        }
                    }

                    // Handle 400 Bad Request errors specifically
                    if (error.response.status === 400) {
                        console.error('Bad request error. Checking all fields again...');
                        // Double check all required fields
                        const missingFields = [];
                        if (!doorNo.trim()) missingFields.push('Door/Flat number');
                        if (!streetName.trim()) missingFields.push('Street name');
                        if (!area.trim()) missingFields.push('Area/Locality');
                        if (!district.trim()) missingFields.push('District');
                        if (!pincode.trim() || !/^\d{6}$/.test(pincode)) missingFields.push('Pincode');

                        if (missingFields.length > 0) {
                            errorMessage = `Missing or invalid fields: ${missingFields.join(', ')}`;
                        }
                    }
                }

                // Show error in UI
                Alert.alert(
                    "Error Saving Address",
                    errorMessage,
                    [{ text: "OK" }]
                );

                showToast(errorMessage, "error");
            } finally {
                setIsLoading(false);
            }
        }
    };

    // Toast animation styles
    const toastTranslateY = toastAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [100, 0],
    });

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-6 pt-16 pb-6 flex-row items-center">
                <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                    <MaterialIcons name="arrow-back" size={24} color="white" />
                </TouchableOpacity>
                <Text className="text-xl text-white font-bold">
                    {mode === 'add'
                        ? 'Add New Address'
                        : isMainAddress
                            ? 'Edit Primary Address'
                            : 'Edit Address'}
                </Text>
            </View>

            <ScrollView className="p-4">
                {isMainAddress && (
                    <View className="bg-blue-50 rounded-xl p-4 mb-4 border border-blue-200">
                        <View className="flex-row items-start">
                            <MaterialIcons name="info" size={20} color="#3B82F6" className="mt-0.5" />
                            <Text className="text-blue-700 ml-2 flex-1">
                                This is your primary address that will be used as the default for all orders unless you select another address.
                            </Text>
                        </View>
                    </View>
                )}

                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                    <Text className="text-lg font-bold mb-4">Address Type</Text>

                    <View className="flex-row mb-4">
                        <TouchableOpacity
                            className={`flex-1 py-3 rounded-xl mr-2 items-center ${addressType === 'Home' ? 'bg-madder' : 'bg-gray-100'}`}
                            onPress={() => setAddressType('Home')}
                        >
                            <Text className={`font-medium ${addressType === 'Home' ? 'text-white' : 'text-gray-700'}`}>Home</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className={`flex-1 py-3 rounded-xl mr-2 items-center ${addressType === 'Work' ? 'bg-madder' : 'bg-gray-100'}`}
                            onPress={() => setAddressType('Work')}
                        >
                            <Text className={`font-medium ${addressType === 'Work' ? 'text-white' : 'text-gray-700'}`}>Work</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className={`flex-1 py-3 rounded-xl items-center ${addressType === 'Other' ? 'bg-madder' : 'bg-gray-100'}`}
                            onPress={() => setAddressType('Other')}
                        >
                            <Text className={`font-medium ${addressType === 'Other' ? 'text-white' : 'text-gray-700'}`}>Other</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Location Selection Options */}
                    <View className="mb-4">
                        <Text className="text-lg font-bold mb-2">Select Location</Text>

                        {/* Use Current Location Button */}
                        <TouchableOpacity
                            className="flex-row items-center justify-center bg-blue-50 p-3 rounded-xl mb-4 border border-blue-200"
                            onPress={getLocation}
                            disabled={isLoadingLocation}
                        >
                            {isLoadingLocation ? (
                                <ActivityIndicator size="small" color="#3B82F6" />
                            ) : (
                                <MaterialIcons name="my-location" size={20} color="#3B82F6" />
                            )}
                            <Text className="text-blue-700 font-medium ml-2">
                                {isLoadingLocation ? "Getting your location..." : "Use my current location"}
                            </Text>
                        </TouchableOpacity>

                        {/* Select on Map Button */}
                        <TouchableOpacity
                            className="flex-row items-center justify-center bg-blue-50 p-3 rounded-xl mb-4 border border-blue-200"
                            onPress={() => setShowMapPicker(true)}
                        >
                            <MaterialIcons name="map" size={20} color="#3B82F6" />
                            <Text className="text-blue-700 font-medium ml-2">
                                Select location on map
                            </Text>
                        </TouchableOpacity>

                        {/* View Selected Location Button */}
                        {selectedLocation && (
                            <TouchableOpacity
                                className="flex-row items-center justify-center bg-green-50 p-3 rounded-xl mb-4 border border-green-200"
                                onPress={() => setShowMapView(true)}
                            >
                                <MaterialIcons name="location-on" size={20} color="#10B981" />
                                <Text className="text-green-700 font-medium ml-2">
                                    View selected location
                                </Text>
                            </TouchableOpacity>
                        )}

                        {/* Location Status Indicator */}
                        {latitude && longitude && (
                            <View className={`mb-4 p-3 rounded-xl ${isWithinZone ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
                                <View className="flex-row items-center">
                                    <MaterialIcons
                                        name={isWithinZone ? "check-circle" : "warning"}
                                        size={20}
                                        color={isWithinZone ? "#10B981" : "#F59E0B"}
                                    />
                                    <Text className={`font-medium ml-2 ${isWithinZone ? 'text-green-700' : 'text-yellow-700'}`}>
                                        {isWithinZone
                                            ? "Location captured successfully"
                                            : "Location is outside our primary delivery zone"}
                                    </Text>
                                </View>
                            </View>
                        )}
                    </View>

                    {/* Google Places Autocomplete */}
                    <View className="mb-4">
                        <Text className="text-gray-700 font-medium mb-2">Search for your address</Text>
                        <GooglePlacesAutocomplete
                            placeholder="Search for your address"
                            fetchDetails={true}
                            onPress={(_, details = null) => {
                                if (details) {
                                    // Extract address components
                                    const addressComponents = details.address_components;

                                    // Find components by type
                                    const streetNumber = addressComponents.find(component =>
                                        component.types.includes('street_number'))?.long_name || '';

                                    const route = addressComponents.find(component =>
                                        component.types.includes('route'))?.long_name || '';

                                    const locality = addressComponents.find(component =>
                                        component.types.includes('locality'))?.long_name || '';

                                    const administrative_area = addressComponents.find(component =>
                                        component.types.includes('administrative_area_level_1'))?.long_name || '';

                                    const postal_code = addressComponents.find(component =>
                                        component.types.includes('postal_code'))?.long_name || '';

                                    // Set address fields
                                    setDoorNo(streetNumber);
                                    setStreetName(route);
                                    setArea(locality);
                                    setDistrict(administrative_area);
                                    setPincode(postal_code);

                                    // Set coordinates
                                    if (details.geometry && details.geometry.location) {
                                        setLatitude(details.geometry.location.lat);
                                        setLongitude(details.geometry.location.lng);

                                        // Check if within delivery zone
                                        checkDeliveryZone(
                                            details.geometry.location.lat,
                                            details.geometry.location.lng
                                        );
                                    }
                                }
                            }}
                            query={{
                                key: 'AIzaSyC4IwgWpiexsb328mgLejrbnPCyhrwVGbs',
                                language: 'en',
                                components: 'country:in'
                            }}
                            styles={{
                                container: {
                                    flex: 0,
                                },
                                textInputContainer: {
                                    borderWidth: 1,
                                    borderColor: '#E5E7EB',
                                    borderRadius: 12,
                                    backgroundColor: 'white',
                                },
                                textInput: {
                                    height: 48,
                                    color: '#1F2937',
                                    fontSize: 16,
                                    borderRadius: 12,
                                },
                                predefinedPlacesDescription: {
                                    color: '#1F2937',
                                },
                                listView: {
                                    backgroundColor: 'white',
                                    borderRadius: 12,
                                    marginTop: 5,
                                },
                                row: {
                                    padding: 13,
                                    height: 'auto',
                                    flexDirection: 'row',
                                },
                                separator: {
                                    height: 1,
                                    backgroundColor: '#E5E7EB',
                                },
                                description: {
                                    fontSize: 14,
                                },
                            }}
                        />
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-700 font-medium mb-2">Door/Flat Number</Text>
                        <TextInput
                            className={`border rounded-xl p-3 text-gray-700 ${errors.doorNo ? 'border-red-500' : 'border-gray-300'}`}
                            placeholder="e.g., 42 or Flat 3B"
                            value={doorNo}
                            onChangeText={setDoorNo}
                        />
                        {errors.doorNo && <Text className="text-red-500 text-xs mt-1">{errors.doorNo}</Text>}
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-700 font-medium mb-2">Street Name</Text>
                        <TextInput
                            className={`border rounded-xl p-3 text-gray-700 ${errors.streetName ? 'border-red-500' : 'border-gray-300'}`}
                            placeholder="e.g., Main Street"
                            value={streetName}
                            onChangeText={setStreetName}
                        />
                        {errors.streetName && <Text className="text-red-500 text-xs mt-1">{errors.streetName}</Text>}
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-700 font-medium mb-2">Area/Locality</Text>
                        <TextInput
                            className={`border rounded-xl p-3 text-gray-700 ${errors.area ? 'border-red-500' : 'border-gray-300'}`}
                            placeholder="e.g., Jayanagar"
                            value={area}
                            onChangeText={setArea}
                        />
                        {errors.area && <Text className="text-red-500 text-xs mt-1">{errors.area}</Text>}
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-700 font-medium mb-2">City</Text>
                        <TextInput
                            className={`border rounded-xl p-3 text-gray-700 ${errors.district ? 'border-red-500' : 'border-gray-300'}`}
                            placeholder="e.g., Bangalore"
                            value={district}
                            onChangeText={setDistrict}
                        />
                        {errors.district && <Text className="text-red-500 text-xs mt-1">{errors.district}</Text>}
                    </View>

                    <View className="mb-4">
                        <Text className="text-gray-700 font-medium mb-2">Pincode</Text>
                        <TextInput
                            className={`border rounded-xl p-3 text-gray-700 ${errors.pincode ? 'border-red-500' : 'border-gray-300'}`}
                            placeholder="Enter your pincode"
                            value={pincode}
                            onChangeText={checkPincode}
                            keyboardType="number-pad"
                            maxLength={6}
                        />
                        {errors.pincode && (
                            <View className="flex-row items-center mt-1">
                                <MaterialIcons name="warning" size={14} color="#EF4444" />
                                <Text className="text-red-500 text-xs ml-1">{errors.pincode}</Text>
                            </View>
                        )}
                        {pincode.length === 6 && !errors.pincode && (
                            <View className="flex-row items-center mt-1">
                                <MaterialIcons name="check-circle" size={14} color="#10B981" />
                                <Text className="text-green-500 text-xs ml-1">Valid delivery pincode</Text>
                            </View>
                        )}
                    </View>

                    <View className="flex-row justify-between items-center py-2">
                        <Text className="text-gray-700 font-medium">Set as primary address</Text>
                        <Switch
                            trackColor={{ false: "#E5E7EB", true: "#A31621" }}
                            thumbColor="#FFFFFF"
                            ios_backgroundColor="#E5E7EB"
                            onValueChange={setIsPrimary}
                            value={isPrimary}
                        />
                    </View>
                </View>

                <TouchableOpacity
                    className="bg-madder py-4 rounded-xl items-center mb-4"
                    onPress={handleSave}
                    disabled={isLoading}
                >
                    {isLoading ? (
                        <View className="flex-row items-center">
                            <ActivityIndicator size="small" color="white" />
                            <Text className="text-white font-bold ml-2">Saving...</Text>
                        </View>
                    ) : (
                        <Text className="text-white font-bold">Save Address</Text>
                    )}
                </TouchableOpacity>
            </ScrollView>

            {/* Toast Notification */}
            {toastVisible && (
                <Animated.View
                    className={`absolute bottom-20 left-5 right-5 ${toastType === 'success' ? 'bg-green-500' : 'bg-red-500'} rounded-lg p-4 flex-row items-center`}
                    style={{
                        transform: [{ translateY: toastTranslateY }],
                        opacity: toastAnimation,
                        shadowColor: "#000",
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.25,
                        shadowRadius: 3.84,
                        elevation: 5,
                    }}
                >
                    <MaterialIcons
                        name={toastType === 'success' ? 'check-circle' : 'error'}
                        size={24}
                        color="white"
                    />
                    <Text className="text-white font-medium ml-2 flex-1">{toastMessage}</Text>
                </Animated.View>
            )}

            {/* Location Picker Modal */}
            <Modal
                visible={showMapPicker}
                animationType="slide"
                onRequestClose={() => setShowMapPicker(false)}
            >
                <DeliveryLocationPicker
                    onLocationSelected={(addressData) => {
                        // Extract location coordinates
                        if (addressData && addressData.coordinates) {
                            handleLocationSelected(addressData.coordinates);

                            // Also update address fields if available
                            if (addressData.flatNumber) setDoorNo(addressData.flatNumber);
                            if (addressData.street) setStreetName(addressData.street);
                            if (addressData.locality) setArea(addressData.locality);
                            if (addressData.area) setDistrict(addressData.area);
                            if (addressData.city) setDistrict(prev => prev || addressData.city);
                            if (addressData.pincode) setPincode(addressData.pincode);
                            if (addressData.addressType) setAddressType(addressData.addressType);
                        }
                        setShowMapPicker(false);
                    }}
                    onClose={() => setShowMapPicker(false)}
                    onValidationChange={(isValid) => setIsWithinZone(isValid)}
                />
            </Modal>

            {/* Map View Modal */}
            <Modal
                visible={showMapView}
                animationType="slide"
                onRequestClose={() => setShowMapView(false)}
            >
                <LocationMapView
                    location={selectedLocation}
                    address={`${doorNo}\n${streetName}\n${area}\n${district}\nPincode: ${pincode}`}
                    title="Selected Location"
                    onClose={() => setShowMapView(false)}
                    showDirectionsButton={false}
                />
            </Modal>

            {/* Pincode Alert Modal */}
            <PincodeAlert
                visible={showPincodeAlert}
                onClose={() => setShowPincodeAlert(false)}
                onBrowseProducts={() => {
                    setShowPincodeAlert(false);
                    // Navigate to home screen to browse products
                    navigation.navigate('Home');
                }}
                pincodeMessage={pincodeMessage}
                showBrowseOption={true}
            />
        </View>
    );
};

export default AddEditAddressScreen;
