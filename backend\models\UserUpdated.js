const mongoose = require("mongoose");

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: false, // Allow empty name for new users
      default: '',
    },
    number: {
      type: String,
      required: true,
      unique: true,
      trim: true, // Remove whitespace
    },
    email: {
      type: String,
      required: false, // Made optional
      unique: false, // Removed unique constraint
      default: null, // Changed back to null
    },
    otpCode: {
      type: String,
      default: null,
    },
    otpExpiry: {
      type: Date,
      default: null,
    },
    // Updated address structure with 5 sections
    address: {
      doorNo: {
        type: String,
        default: "",
      },
      streetName: {
        type: String,
        default: "",
      },
      area: {
        type: String,
        default: "",
      },
      district: {
        type: String,
        default: "",
      },
      pincode: {
        type: String,
        default: "",
      },
      // Keep the full address string for backward compatibility
      fullAddress: {
        type: String,
        default: "",
      }
    },
    coins: [
      {
        amount: { type: Number, default: 0 },
        source: { type: String, default: 'System' }, // Source of the coins (order, promotion, etc.)
        createdAt: { type: Date, default: Date.now }, // When the coins were added
        isUsed: { type: Boolean, default: false }, // Whether the coins have been used
        usedFor: { type: String, default: null }, // Order ID or purpose for which coins were used
        usedAt: { type: Date, default: null }, // When the coins were used
      },
    ],
    orderHistory: [
      {
        orderId: { type: mongoose.Schema.Types.ObjectId, ref: "Order" },
        date: { type: Date, default: Date.now },
      },
    ],
    isAdmin: {
      type: Boolean,
      required: true,
      default: false,
    },
    refreshToken: {
      type: String,
      default: null,
    },
  },
  { timestamps: true }
);

const User = mongoose.model("User", userSchema);
module.exports = User;
