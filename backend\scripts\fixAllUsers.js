const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('../models/User');

// Load environment variables
dotenv.config();
console.log('MongoDB URI:', process.env.MONGO_URI ? 'URI is set' : 'URI is not set');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
    .then(async () => {
        console.log('MongoDB Connected');
        
        try {
            // Find all users
            const users = await User.find({});
            console.log(`Found ${users.length} users`);
            
            // Update each user to add a generated email if missing
            for (const user of users) {
                if (!user.email) {
                    console.log(`Updating user: ${user.name || 'unnamed'} (${user.number})`);
                    
                    // Generate an email using the phone number
                    const generatedEmail = `user${user.number}@gmail.com`;
                    console.log(`Generated email: ${generatedEmail}`);
                    
                    // Update the user's email
                    user.email = generatedEmail;
                    
                    // Save the user
                    await user.save();
                    console.log(`Updated user: ${user.name || 'unnamed'} (${user.number}) with email: ${generatedEmail}`);
                } else {
                    console.log(`User already has email: ${user.name || 'unnamed'} (${user.number}) - ${user.email}`);
                }
            }
            
            console.log('All users have been updated');
            process.exit(0);
        } catch (error) {
            console.error('Error fixing users:', error);
            process.exit(1);
        }
    })
    .catch(err => {
        console.error('MongoDB connection error:', err);
        process.exit(1);
    });
