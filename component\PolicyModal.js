import React, { useRef, useEffect, useState, useMemo } from 'react';
import { View, Text, Modal, ScrollView, TouchableOpacity, Animated, StyleSheet, Easing } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";

const PolicyModal = ({ visible, onClose, type }) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.8)).current;
    const [isAnimating, setIsAnimating] = useState(false);

    useEffect(() => {
        if (visible) {
            setIsAnimating(true);
            // Reset animations
            fadeAnim.setValue(0);
            scaleAnim.setValue(0.8);

            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 200,
                    useNativeDriver: true,
                    easing: Easing.out(Easing.cubic),
                }),
                Animated.spring(scaleAnim, {
                    toValue: 1,
                    tension: 100,
                    friction: 8,
                    useNativeDriver: true,
                })
            ]).start(() => {
                setIsAnimating(false);
            });
        }
    }, [visible]);

    const handleClose = () => {
        if (isAnimating) return; // Prevent multiple close calls during animation

        setIsAnimating(true);
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 150,
                useNativeDriver: true,
                easing: Easing.in(Easing.cubic),
            }),
            Animated.timing(scaleAnim, {
                toValue: 0.8,
                duration: 150,
                useNativeDriver: true,
                easing: Easing.in(Easing.cubic),
            })
        ]).start(() => {
            setIsAnimating(false);
            onClose();
        });
    };

    const privacyPolicyContent = `Meat Now Privacy Policy

This privacy policy applies to the Meat Now app (hereby referred to as "Application") for mobile devices that was created by Meat Now Team (hereby referred to as "Service Provider") as a Free service. This service is intended for use "AS IS".

Information Collection and Use
The Application collects information when you download and use it. This information may include information such as:

• Your device's Internet Protocol address (e.g. IP address)
• The pages of the Application that you visit, the time and date of your visit, the time spent on those pages
• The time spent on the Application
• The operating system you use on your mobile device

The Application collects your device's location, which helps the Service Provider determine your approximate geographical location and make use of in below ways:

Geolocation Services: The Service Provider utilizes location data to provide features such as personalized content, relevant recommendations, and location-based services.

Analytics and Improvements: Aggregated and anonymized location data helps the Service Provider to analyze user behavior, identify trends, and improve the overall performance and functionality of the Application.

Third-Party Services: Periodically, the Service Provider may transmit anonymized location data to external services. These services assist them in enhancing the Application and optimizing their offerings.

The Service Provider may use the information you provided to contact you from time to time to provide you with important information, required notices and marketing promotions.

For a better experience, while using the Application, the Service Provider may require you to provide us with certain personally identifiable information, including but not limited to your name, email address, and phone number. The information that the Service Provider request will be retained by them and used as described in this privacy policy.

Third Party Access
Only aggregated, anonymized data is periodically transmitted to external services to aid the Service Provider in improving the Application and their service. The Service Provider may share your information with third parties in the ways that are described in this privacy statement.

Please note that the Application utilizes third-party services that have their own Privacy Policy about handling data. Below are the links to the Privacy Policy of the third-party service providers used by the Application:

• Google Play Services

The Service Provider may disclose User Provided and Automatically Collected Information:
• as required by law, such as to comply with a subpoena, or similar legal process;
• when they believe in good faith that disclosure is necessary to protect their rights, protect your safety or the safety of others, investigate fraud, or respond to a government request;
• with their trusted services providers who work on their behalf, do not have an independent use of the information we disclose to them, and have agreed to adhere to the rules set forth in this privacy statement.

Opt-Out Rights
You can stop all collection of information by the Application easily by uninstalling it. You may use the standard uninstall processes as may be available as part of your mobile device or via the mobile application marketplace or network.

Data Retention Policy
The Service Provider will retain User Provided data for as long as you use the Application and for a reasonable time thereafter. If you'd like them to delete User Provided Data that you have provided via the Application, please contact <NAME_EMAIL> and they will respond in a reasonable time.

Children
The Service Provider does not use the Application to knowingly solicit data from or market to children under the age of 13.

The Application does not address anyone under the age of 13. The Service Provider does not knowingly collect personally identifiable information from children under 13 years of age. In the case the Service Provider discover that a child under 13 has provided personal information, the Service Provider will immediately delete this from their servers. If you are a parent or guardian and you are aware that your child has provided us with personal information, please contact the Service Provider (<EMAIL>) so that they will be able to take the necessary actions.

Security
The Service Provider is concerned about safeguarding the confidentiality of your information. The Service Provider provides physical, electronic, and procedural safeguards to protect information the Service Provider processes and maintains.

Changes
This Privacy Policy may be updated from time to time for any reason. The Service Provider will notify you of any changes to the Privacy Policy by updating this page with the new Privacy Policy. You are advised to consult this Privacy Policy regularly for any changes, as continued use is deemed approval of all changes.

This privacy policy is effective as of 2025-05-20

Your Consent
By using the Application, you are consenting to the processing of your information as set forth in this Privacy Policy now and as amended by us.

Contact Us
If you have any questions regarding privacy while using the Application, or have questions about the practices, please contact the Service Provider via <NAME_EMAIL>.

This privacy policy page was generated by App Privacy Policy Generator`;

    const termsAndConditionsContent = `Terms and Conditions – Meat Now

Welcome to Meat Now! These terms and conditions ("Terms") govern your use of the Meat Now mobile application ("Service") operated by the Meat Now Team ("we", "us", or "our").

By accessing or using our Service, you agree to be bound by these Terms. If you disagree with any part of these terms, please refrain from using the Service.

1. ACCEPTANCE OF TERMS
By downloading, installing, or using the Meat Now app, you acknowledge that you have read, understood, and agree to be bound by these Terms and our Privacy Policy.

2. DESCRIPTION OF SERVICE
Meat Now is a food delivery application that enables users to:
• Browse and order fresh meat and related products
• Schedule delivery slots
• Make payments using multiple payment methods
• Track order status and delivery
• Manage user profiles and preferences

3. USER ACCOUNTS
• You must provide accurate and complete information when creating an account
• You are responsible for maintaining the confidentiality of your account credentials
• Only one account per phone number is permitted

4. ORDERING AND DELIVERY
• All orders are subject to availability and confirmation
• Deliveries are available only in designated service areas
• Delivery fees may apply based on order value and location
• We reserve the right to accept, refuse, or cancel orders at our discretion

5. PAYMENT TERMS
• Payments can be made via Cash on Delivery (COD) or UPI
• All prices listed are inclusive of applicable taxes
• Offers and promotions are subject to their own terms
• Refunds are processed in accordance with our refund policy

6. CANCELLATION AND REFUND POLICY
• Orders can be cancelled only before preparation begins
• Cancellations after preparation may not be eligible for a refund
• Refunds for eligible cancellations will be processed within 5–7 business days
• If products are delivered damaged or incorrect, replacements or refunds will be provided

7. USER CONDUCT
You agree not to:
• Use the Service for any illegal or unauthorized purpose
• Provide false, misleading, or outdated information
• Disrupt or interfere with the Service or servers
• Attempt unauthorized access to our systems

8. INTELLECTUAL PROPERTY
• All trademarks, logos, content, and intellectual property belong to the Meat Now team
• You may not reproduce, distribute, or create derivative works without explicit permission
• User-generated content remains your property, but by submitting it, you grant us a license to use it

9. LIMITATION OF LIABILITY
• The Service is provided "as is" without warranties of any kind
• We are not responsible for indirect, incidental, or consequential damages
• Our maximum liability is limited to the amount paid for the disputed order

10. PRIVACY AND DATA PROTECTION
• We are committed to protecting your privacy
• Your data is collected and used in accordance with our Privacy Policy
• We implement reasonable safeguards to protect your personal data

11. MODIFICATIONS TO TERMS
• We reserve the right to change these Terms at any time
• Any updates will take effect immediately upon posting
• Continued use of the Service constitutes your acceptance of the revised Terms

12. TERMINATION
• We may suspend or terminate your account if you violate these Terms
• If you wish to delete your account, you must submit a request using the Account Deletion Request Form
• Upon verifying your request, your account will be deleted, and you will receive a confirmation
• Termination does not affect any obligations related to previously placed orders

13. GOVERNING LAW
These Terms are governed by the laws of India. Any disputes will be subject to the jurisdiction of the courts of Tamil Nadu.

14. CONTACT INFORMATION
For questions, concerns, or feedback regarding these Terms, please contact us:
📞 Customer Support: Available via the app support section

15. SEVERABILITY
If any part of these Terms is deemed unenforceable, the remaining sections shall remain valid and in effect.

By using Meat Now, you acknowledge that you have read, understood, and agreed to all of the above Terms and Conditions.`;

    // Memoize content to prevent re-computation during animations
    const { content, title } = useMemo(() => ({
        content: type === 'privacy' ? privacyPolicyContent : termsAndConditionsContent,
        title: type === 'privacy' ? 'Privacy Policy' : 'Terms and Conditions'
    }), [type]);

    // Render content immediately when visible
    const shouldRenderContent = visible;

    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="none"
            onRequestClose={handleClose}
            statusBarTranslucent={true}
        >
            <Animated.View
                style={[styles.overlay, { opacity: fadeAnim }]}
                pointerEvents={isAnimating ? 'none' : 'auto'}
            >
                <Animated.View
                    style={[
                        styles.modalContainer,
                        {
                            transform: [{ scale: scaleAnim }],
                            opacity: fadeAnim
                        }
                    ]}
                >
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>{title}</Text>
                        <TouchableOpacity
                            onPress={handleClose}
                            style={styles.closeButton}
                            disabled={isAnimating}
                            activeOpacity={0.7}
                        >
                            <MaterialIcons name="close" size={24} color="#6B7280" />
                        </TouchableOpacity>
                    </View>

                    {/* Content - Only render when not animating */}
                    {shouldRenderContent ? (
                        <ScrollView
                            style={styles.contentContainer}
                            showsVerticalScrollIndicator={false}
                            removeClippedSubviews={true}
                            maxToRenderPerBatch={10}
                            windowSize={10}
                        >
                            <Text style={styles.contentText}>{content}</Text>
                        </ScrollView>
                    ) : (
                        <View style={styles.contentContainer}>
                            <View style={styles.loadingContainer}>
                                <Text style={styles.loadingText}>Loading...</Text>
                            </View>
                        </View>
                    )}

                    {/* Footer */}
                    <View style={styles.footer}>
                        <TouchableOpacity
                            onPress={handleClose}
                            style={[styles.acceptButton, isAnimating && styles.disabledButton]}
                            disabled={isAnimating}
                            activeOpacity={0.8}
                        >
                            <Text style={styles.acceptButtonText}>I Understand</Text>
                        </TouchableOpacity>
                    </View>
                </Animated.View>
            </Animated.View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 20,
        height: '90%',
        marginHorizontal: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.25,
        shadowRadius: 8,
        elevation: 10,
        // Performance optimizations
        shouldRasterizeIOS: true,
        renderToHardwareTextureAndroid: true,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#F3F4F6',
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#1F2937',
        flex: 1,
    },
    closeButton: {
        padding: 4,
    },
    contentContainer: {
        flex: 1,
        paddingHorizontal: 24,
        paddingVertical: 20,
    },
    contentText: {
        fontSize: 16,
        lineHeight: 26,
        color: '#1F2937',
        textAlign: 'left',
        fontWeight: '400',
    },
    footer: {
        padding: 20,
        borderTopWidth: 1,
        borderTopColor: '#F3F4F6',
    },
    acceptButton: {
        backgroundColor: '#A31621',
        paddingVertical: 14,
        borderRadius: 12,
        alignItems: 'center',
        shadowColor: '#A31621',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 3,
        elevation: 3,
    },
    acceptButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 16,
        color: '#6B7280',
        fontWeight: '500',
    },
    disabledButton: {
        opacity: 0.7,
    },
});

export default PolicyModal;
