import axios from 'axios';
import { API_URL } from '../../config/constants';
import { getAuthToken } from '../authStorage';

// Cache for products data
let productsCache = {
    data: null,
    timestamp: 0
};

// Get all products for user with caching
export const getUserProducts = async () => {
    try {
        // Check cache first (5 minutes cache)
        const now = new Date().getTime();
        const cacheAge = now - productsCache.timestamp;

        if (productsCache.data && cacheAge < 300000) { // 5 minutes
            console.log('Using cached products data, age:', cacheAge, 'ms');
            return productsCache.data;
        }

        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/products`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        // Update cache
        productsCache = {
            data: response.data,
            timestamp: now
        };

        return response.data;
    } catch (error) {
        console.error('Error fetching products:', error);

        // Return cached data if available on error
        if (productsCache.data) {
            console.log('API error, using cached products data as fallback');
            return productsCache.data;
        }

        throw error;
    }
};

// Get product by ID for user
export const getUserProductById = async (productId) => {
    try {
        if (!productId) {
            console.error('Invalid product ID provided:', productId);
            throw new Error('Invalid product ID provided');
        }

        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/products/${productId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        if (response.data && response.data.product) {
            return response.data;
        } else {
            throw new Error('Product data not found in API response');
        }
    } catch (error) {
        console.error(`Error fetching product ${productId}:`, error);
        throw error;
    }
};

// Get products by category for user
export const getUserProductsByCategory = async (categoryId) => {
    try {
        if (!categoryId) {
            console.error('Invalid category ID provided:', categoryId);
            throw new Error('Invalid category ID provided');
        }

        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/products/category/${categoryId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        return response.data;
    } catch (error) {
        console.error(`Error fetching products for category ${categoryId}:`, error);
        throw error;
    }
};

// Cache for bestselling products
let bestsellingCache = {
    data: null,
    timestamp: 0
};

// Function to clear all caches (for refresh functionality)
export const clearProductCaches = () => {
    productsCache = { data: null, timestamp: 0 };
    bestsellingCache = { data: null, timestamp: 0 };
    console.log('Product caches cleared');
};

// Get bestselling products for user with caching
export const getBestsellingProducts = async () => {
    try {
        // Check cache first (5 minutes cache)
        const now = new Date().getTime();
        const cacheAge = now - bestsellingCache.timestamp;

        if (bestsellingCache.data && cacheAge < 300000) { // 5 minutes
            console.log('Using cached bestselling data, age:', cacheAge, 'ms');
            return bestsellingCache.data;
        }

        const token = await getAuthToken();
        const response = await axios.get(`${API_URL}/products/bestselling`, {
            headers: {
                Authorization: `Bearer ${token}`
            },
            timeout: 10000 // 10 second timeout
        });

        // Update cache
        bestsellingCache = {
            data: response.data,
            timestamp: now
        };

        return response.data;
    } catch (error) {
        console.error('Error fetching bestselling products:', error);

        // Return cached data if available on error
        if (bestsellingCache.data) {
            console.log('API error, using cached bestselling data as fallback');
            return bestsellingCache.data;
        }

        throw error;
    }
};
