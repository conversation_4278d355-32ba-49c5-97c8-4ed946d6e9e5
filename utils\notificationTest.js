import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform, Alert } from 'react-native';
import Constants from 'expo-constants';

/**
 * Comprehensive notification test for debugging production builds
 */
export const runNotificationDiagnostics = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    platform: Platform.OS,
    device: Device.isDevice,
    appOwnership: Constants.appOwnership,
    sdkVersion: Constants.expoConfig?.sdkVersion,
    projectId: Constants.expoConfig?.extra?.eas?.projectId,
    tests: {}
  };

  console.log('🔍 Starting Notification Diagnostics...');
  console.log('📱 Platform:', Platform.OS);
  console.log('📱 Device:', Device.isDevice ? 'Physical' : 'Simulator');
  console.log('📱 App Ownership:', Constants.appOwnership);

  // Test 1: Device Check
  try {
    results.tests.deviceCheck = {
      passed: Device.isDevice,
      message: Device.isDevice ? 'Running on physical device' : 'Running on simulator - notifications not supported'
    };
  } catch (error) {
    results.tests.deviceCheck = {
      passed: false,
      error: error.message
    };
  }

  // Test 2: Permission Check
  try {
    const { status, granted, canAskAgain } = await Notifications.getPermissionsAsync();
    results.tests.permissionCheck = {
      passed: status === 'granted',
      status,
      granted,
      canAskAgain,
      message: `Permission status: ${status}`
    };
  } catch (error) {
    results.tests.permissionCheck = {
      passed: false,
      error: error.message
    };
  }

  // Test 3: Permission Request (if needed)
  if (results.tests.permissionCheck?.status !== 'granted' && results.tests.permissionCheck?.canAskAgain) {
    try {
      const { status } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
        },
        android: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
        },
      });
      results.tests.permissionRequest = {
        passed: status === 'granted',
        status,
        message: `Permission request result: ${status}`
      };
    } catch (error) {
      results.tests.permissionRequest = {
        passed: false,
        error: error.message
      };
    }
  }

  // Test 4: Notification Channel (Android)
  if (Platform.OS === 'android') {
    try {
      await Notifications.setNotificationChannelAsync('diagnostic', {
        name: 'Diagnostic Channel',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#A31621',
        sound: 'default',
      });
      results.tests.channelSetup = {
        passed: true,
        message: 'Android notification channel created successfully'
      };
    } catch (error) {
      results.tests.channelSetup = {
        passed: false,
        error: error.message
      };
    }
  }

  // Test 5: Push Token Generation
  try {
    const projectId = Constants.expoConfig?.extra?.eas?.projectId;
    if (!projectId) {
      throw new Error('No project ID found in configuration');
    }

    const tokenResult = await Notifications.getExpoPushTokenAsync({ projectId });
    results.tests.tokenGeneration = {
      passed: true,
      token: tokenResult.data,
      message: 'Push token generated successfully'
    };
  } catch (error) {
    results.tests.tokenGeneration = {
      passed: false,
      error: error.message
    };
  }

  // Test 6: Local Notification
  if (results.tests.permissionCheck?.passed || results.tests.permissionRequest?.passed) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧪 Diagnostic Test',
          body: 'If you see this, notifications are working!',
          data: { test: true },
          sound: 'default',
        },
        trigger: { seconds: 2 },
      });
      results.tests.localNotification = {
        passed: true,
        message: 'Local notification scheduled successfully'
      };
    } catch (error) {
      results.tests.localNotification = {
        passed: false,
        error: error.message
      };
    }
  }

  // Calculate overall status
  const passedTests = Object.values(results.tests).filter(test => test.passed).length;
  const totalTests = Object.keys(results.tests).length;
  results.overall = {
    passed: passedTests,
    total: totalTests,
    success: passedTests === totalTests,
    percentage: Math.round((passedTests / totalTests) * 100)
  };

  console.log('📊 Diagnostic Results:', results);
  return results;
};

/**
 * Show user-friendly diagnostic results
 */
export const showDiagnosticResults = async () => {
  const results = await runNotificationDiagnostics();
  
  let message = `Notification Diagnostic Results:\n\n`;
  message += `✅ Passed: ${results.overall.passed}/${results.overall.total} tests\n\n`;

  if (!results.tests.deviceCheck?.passed) {
    message += `❌ Device: Must use physical device\n`;
  }

  if (!results.tests.permissionCheck?.passed && !results.tests.permissionRequest?.passed) {
    message += `❌ Permissions: Not granted\n`;
  }

  if (!results.tests.tokenGeneration?.passed) {
    message += `❌ Push Token: Failed to generate\n`;
    message += `   Error: ${results.tests.tokenGeneration?.error}\n`;
  }

  if (results.overall.success) {
    message += `\n🎉 All tests passed! Notifications should work.`;
  } else {
    message += `\n⚠️ Some tests failed. Check console for details.`;
  }

  Alert.alert('Notification Diagnostics', message);
  return results;
};

/**
 * Quick notification test for OTP functionality
 */
export const testOtpNotification = async () => {
  try {
    const { status } = await Notifications.getPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please enable notifications first');
      return false;
    }

    await Notifications.scheduleNotificationAsync({
      content: {
        title: '🔐 OTP Test',
        body: 'Test OTP: 123456',
        data: { 
          type: 'otp_verification',
          otp: '123456',
          test: true 
        },
        sound: 'default',
      },
      trigger: { seconds: 1 },
    });

    Alert.alert('Test Sent', 'Check if you receive the OTP notification');
    return true;
  } catch (error) {
    console.error('OTP test failed:', error);
    Alert.alert('Test Failed', error.message);
    return false;
  }
};
