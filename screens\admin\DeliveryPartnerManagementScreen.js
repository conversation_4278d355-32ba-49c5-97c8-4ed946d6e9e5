import React, { useState, useRef } from 'react';
import {
    View,
    Text,
    FlatList,
    TouchableOpacity,
    TextInput,
    Alert,
    ActivityIndicator,
    RefreshControl,
    PanResponder
} from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useAdminDeliveryPartner } from '../../context/AdminDeliveryPartnerContext';

const DeliveryPartnerManagementScreen = () => {
    const navigation = useNavigation();
    const { deliveryPartners, refreshData } = useAdminDeliveryPartner();

    const [searchQuery, setSearchQuery] = useState('');
    const [filterStatus, setFilterStatus] = useState('all');
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(false);

    // Reference for the FlatList
    const flatListRef = useRef(null);

    // Handle swipe between filter tabs
    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => false,
            onMoveShouldSetPanResponder: (_, gestureState) => {
                return Math.abs(gestureState.dx) > 50 && Math.abs(gestureState.dy) < 100;
            },
            onPanResponderRelease: (_, gestureState) => {
                const filterOptions = ['all', 'available', 'unavailable'];
                const currentIndex = filterOptions.indexOf(filterStatus);

                // Swipe right to left (next filter)
                if (gestureState.dx < -50 && currentIndex < 2) {
                    const nextFilter = filterOptions[currentIndex + 1];
                    setFilterStatus(nextFilter);
                    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
                }

                // Swipe left to right (previous filter)
                if (gestureState.dx > 50 && currentIndex > 0) {
                    const prevFilter = filterOptions[currentIndex - 1];
                    setFilterStatus(prevFilter);
                    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
                }
            }
        })
    ).current;

    // Function to handle refresh
    const handleRefresh = async () => {
        try {
            setLoading(true);
            setRefreshing(true);
            console.log('Refreshing delivery partners list...');
            await refreshData();
            console.log('Delivery partners list refreshed successfully');
        } catch (error) {
            console.error('Error refreshing delivery partners:', error);
            Alert.alert('Error', 'Failed to refresh delivery partners list');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    // Refresh data when screen comes into focus
    useFocusEffect(
        React.useCallback(() => {
            handleRefresh();
            return () => {};
        }, [])
    );

    // Filter delivery partners based on search query and status
    const filteredPartners = deliveryPartners.filter(partner => {
        const matchesSearch =
            partner.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (partner.phone && partner.phone.includes(searchQuery)) ||
            (partner.phoneNumber && partner.phoneNumber.includes(searchQuery)) ||
            partner.id.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesStatus =
            filterStatus === 'all' ||
            (filterStatus === 'available' && partner.isAvailable) ||
            (filterStatus === 'unavailable' && !partner.isAvailable);

        return matchesSearch && matchesStatus;
    });

    // Availability is now managed by delivery partners themselves

    const renderPartnerItem = ({ item }) => (
        <TouchableOpacity
            className="bg-white rounded-xl mb-4 overflow-hidden shadow-sm"
            style={{
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
                elevation: 3
            }}
            onPress={() => navigation.navigate('DeliveryPartnerDetailScreen', { partnerId: item.id })}
            activeOpacity={0.7}
        >
            <View className="p-4">
                <View className="flex-row items-center">
                    <View className="w-14 h-14 rounded-full bg-madder/10 items-center justify-center mr-4">
                        <Text className="text-2xl font-bold text-madder">
                            {item.name ? item.name.charAt(0) : '?'}
                        </Text>
                    </View>

                    <View className="flex-1">
                        <View className="flex-row items-center justify-between">
                            <Text className="text-lg font-bold text-gray-800">{item.name}</Text>
                            <View className={`px-3 py-1 rounded-full ${item.isAvailable ? 'bg-green-100' : 'bg-gray-100'}`}>
                                <Text className={`text-xs font-medium ${item.isAvailable ? 'text-green-600' : 'text-gray-600'}`}>
                                    {item.isAvailable ? 'Available' : 'Unavailable'}
                                </Text>
                            </View>
                        </View>

                        <View className="flex-row items-center mt-1">
                            <MaterialIcons name="phone" size={14} color="#666" />
                            <Text className="text-gray-600 ml-1">{item.phone || item.phoneNumber}</Text>
                        </View>

                        <View className="flex-row items-center mt-1">
                            <MaterialIcons name="motorcycle" size={14} color="#666" />
                            <Text className="text-gray-500 text-xs ml-1">{item.vehicleNumber || item.vehicleType}</Text>
                        </View>
                    </View>
                </View>

                <View className="flex-row justify-between mt-4 pt-3 border-t border-gray-100">
                    <View className="flex-row items-center">
                        <MaterialIcons name="star" size={16} color="#FFD700" />
                        <Text className="ml-1 text-gray-700 font-medium">{item.rating || 0}</Text>
                    </View>

                    <View className="flex-row items-center">
                        <MaterialIcons name="local-shipping" size={16} color="#A31621" />
                        <Text className="ml-1 text-gray-700 font-medium">{item.totalDeliveries || 0} deliveries</Text>
                    </View>

                    {item.inTransitOrders > 0 && (
                        <View className="flex-row items-center">
                            <MaterialIcons name="directions-bike" size={16} color="#3B82F6" />
                            <Text className="ml-1 text-blue-600 font-medium">{item.inTransitOrders} in transit</Text>
                        </View>
                    )}
                </View>
            </View>

            <View className="bg-madder/5 px-4 py-3 flex-row justify-between items-center">
                <Text className="text-gray-500 text-xs">ID: {item.id}</Text>
                <View className="flex-row items-center">
                    <Text className="text-madder mr-1 text-sm font-medium">View Details</Text>
                    <MaterialIcons name="chevron-right" size={16} color="#A31621" />
                </View>
            </View>
        </TouchableOpacity>
    );

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-4 pt-12 pb-6 rounded-b-3xl">
                <View className="flex-row items-center mb-3">
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        className="mr-3 bg-white/20 p-1.5 rounded-full"
                    >
                        <MaterialIcons name="arrow-back" size={20} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">Delivery Partners</Text>
                </View>

                <View>
                    <View className="bg-white rounded-xl flex-row items-center px-3 shadow-sm">
                        <MaterialIcons name="search" size={20} color="#666" />
                        <TextInput
                            placeholder="Search by name, phone or ID..."
                            className="flex-1 py-2.5 px-2"
                            value={searchQuery}
                            onChangeText={setSearchQuery}
                        />
                        {searchQuery.length > 0 && (
                            <TouchableOpacity
                                onPress={() => setSearchQuery('')}
                                className="bg-gray-100 p-1 rounded-full"
                            >
                                <MaterialIcons name="close" size={18} color="#666" />
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
            </View>

            <View className="bg-white mx-4 mt-2 mb-1 rounded-xl p-1 shadow-sm flex-row">
                <TouchableOpacity
                    className={`flex-1 py-2 rounded-lg ${filterStatus === 'all' ? 'bg-madder' : 'bg-transparent'}`}
                    onPress={() => setFilterStatus('all')}
                >
                    <Text className={`text-center font-medium text-sm ${filterStatus === 'all' ? 'text-white' : 'text-gray-600'}`}>
                        All ({deliveryPartners.length})
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    className={`flex-1 py-2 rounded-lg ${filterStatus === 'available' ? 'bg-madder' : 'bg-transparent'}`}
                    onPress={() => setFilterStatus('available')}
                >
                    <Text className={`text-center font-medium text-sm ${filterStatus === 'available' ? 'text-white' : 'text-gray-600'}`}>
                        Available ({deliveryPartners.filter(p => p.isAvailable).length})
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    className={`flex-1 py-2 rounded-lg ${filterStatus === 'unavailable' ? 'bg-madder' : 'bg-transparent'}`}
                    onPress={() => setFilterStatus('unavailable')}
                >
                    <Text className={`text-center font-medium text-sm ${filterStatus === 'unavailable' ? 'text-white' : 'text-gray-600'}`}>
                        Unavailable ({deliveryPartners.filter(p => !p.isAvailable).length})
                    </Text>
                </TouchableOpacity>
            </View>

            <FlatList
                ref={flatListRef}
                className="px-4 pt-1"
                data={filteredPartners}
                keyExtractor={item => item.id}
                renderItem={renderPartnerItem}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 80 }}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={["#A31621"]}
                        tintColor="#A31621"
                    />
                }
                {...panResponder.panHandlers}
                ListEmptyComponent={
                    <View className="items-center justify-center py-12 bg-white rounded-xl mt-4 mx-2 shadow-sm">
                        {loading ? (
                            <>
                                <ActivityIndicator size="large" color="#A31621" />
                                <Text className="text-gray-600 mt-4 text-lg font-medium">Loading partners...</Text>
                            </>
                        ) : (
                            <>
                                <MaterialIcons name="people" size={80} color="#A31621" opacity={0.2} />
                                <Text className="text-gray-700 mt-6 text-xl font-bold">No delivery partners found</Text>
                                <Text className="text-gray-500 text-center mt-2 px-6 mb-4">
                                    {searchQuery ?
                                        "Try a different search term or clear the search" :
                                        "Add a new delivery partner to get started"}
                                </Text>

                                <TouchableOpacity
                                    className="bg-madder px-6 py-3 rounded-xl flex-row items-center mt-2"
                                    onPress={() => {
                                        navigation.navigate('EditDeliveryPartnerScreen', {
                                            isNewPartner: true,
                                            onGoBack: handleRefresh
                                        });
                                    }}
                                >
                                    <MaterialIcons name="add" size={20} color="white" />
                                    <Text className="text-white font-bold ml-2">Add Partner</Text>
                                </TouchableOpacity>
                            </>
                        )}
                    </View>
                }
            />

            <View className="absolute bottom-6 right-6 left-6 flex-row justify-between items-center">
                {filteredPartners.length > 0 && (
                    <View className="bg-white/90 backdrop-blur-md px-4 py-2 rounded-full shadow-sm">
                        <Text className="text-gray-700 font-medium">
                            {filteredPartners.length} partners found
                            {loading && " (Loading...)"}
                        </Text>
                    </View>
                )}

                {filteredPartners.length > 0 && (
                    <TouchableOpacity
                        className="bg-madder shadow-md p-4 rounded-full"
                        style={{
                            shadowColor: "#A31621",
                            shadowOffset: { width: 0, height: 4 },
                            shadowOpacity: 0.2,
                            shadowRadius: 5,
                            elevation: 5
                        }}
                        onPress={() => {
                            navigation.navigate('EditDeliveryPartnerScreen', {
                                isNewPartner: true,
                                onGoBack: handleRefresh
                            });
                        }}
                    >
                        <MaterialIcons name="add" size={28} color="white" />
                    </TouchableOpacity>
                )}
            </View>
        </View>
    );
};

export default DeliveryPartnerManagementScreen;
