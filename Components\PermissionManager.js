import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    Image,
    Animated,
    Alert,
    Platform,
    Linking
} from 'react-native';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import * as Notifications from 'expo-notifications';
import * as Location from 'expo-location';

const PermissionManager = ({ onPermissionsComplete }) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [notificationPermission, setNotificationPermission] = useState(null);
    const [locationPermission, setLocationPermission] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);

    // Animation values
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(50)).current;
    const iconScaleAnim = useRef(new Animated.Value(0)).current;

    const permissions = [
        {
            id: 'notification',
            title: 'Enable Notifications',
            description: 'Get instant OTP codes and order updates delivered directly to your device for faster login and real-time tracking.',
            icon: 'notifications-active',
            color: '#A31621',
            bgColor: '#FEF2F2'
        },
        {
            id: 'location',
            title: 'Location for Delivery',
            description: 'Allow location access only when selecting your delivery address on the map. No background tracking.',
            icon: 'location-on',
            color: '#059669',
            bgColor: '#F0FDF4'
        }
    ];

    useEffect(() => {
        // Start entrance animation
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 600,
                useNativeDriver: true,
            }),
            Animated.spring(iconScaleAnim, {
                toValue: 1,
                tension: 50,
                friction: 8,
                useNativeDriver: true,
            })
        ]).start();
    }, [currentStep]);

    const requestNotificationPermission = async () => {
        try {
            setIsProcessing(true);
            
            const { status: existingStatus } = await Notifications.getPermissionsAsync();
            
            if (existingStatus === 'granted') {
                setNotificationPermission('granted');
                return true;
            }

            const { status } = await Notifications.requestPermissionsAsync({
                ios: {
                    allowAlert: true,
                    allowBadge: true,
                    allowSound: true,
                    allowAnnouncements: false,
                },
                android: {
                    allowAlert: true,
                    allowBadge: true,
                    allowSound: true,
                },
            });

            setNotificationPermission(status);
            return status === 'granted';
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            setNotificationPermission('denied');
            return false;
        } finally {
            setIsProcessing(false);
        }
    };

    const requestLocationPermission = async () => {
        try {
            setIsProcessing(true);
            
            const { status: existingStatus } = await Location.getForegroundPermissionsAsync();
            
            if (existingStatus === 'granted') {
                setLocationPermission('granted');
                return true;
            }

            const { status } = await Location.requestForegroundPermissionsAsync();
            setLocationPermission(status);
            return status === 'granted';
        } catch (error) {
            console.error('Error requesting location permission:', error);
            setLocationPermission('denied');
            return false;
        } finally {
            setIsProcessing(false);
        }
    };

    const handlePermissionRequest = async (permissionType) => {
        let granted = false;
        
        if (permissionType === 'notification') {
            granted = await requestNotificationPermission();
        } else if (permissionType === 'location') {
            granted = await requestLocationPermission();
        }

        // Animate to next step or complete
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: -50,
                duration: 300,
                useNativeDriver: true,
            })
        ]).start(() => {
            if (currentStep < permissions.length - 1) {
                setCurrentStep(currentStep + 1);
                // Reset animations for next step
                fadeAnim.setValue(0);
                slideAnim.setValue(50);
                iconScaleAnim.setValue(0);
            } else {
                // All permissions processed
                onPermissionsComplete({
                    notification: notificationPermission,
                    location: locationPermission
                });
            }
        });
    };

    const handleSkip = () => {
        if (currentStep < permissions.length - 1) {
            // Set current permission as skipped and move to next
            if (permissions[currentStep].id === 'notification') {
                setNotificationPermission('denied');
            } else if (permissions[currentStep].id === 'location') {
                setLocationPermission('denied');
            }
            
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(slideAnim, {
                    toValue: -50,
                    duration: 300,
                    useNativeDriver: true,
                })
            ]).start(() => {
                setCurrentStep(currentStep + 1);
                fadeAnim.setValue(0);
                slideAnim.setValue(50);
                iconScaleAnim.setValue(0);
            });
        } else {
            // Skip last permission and complete
            if (permissions[currentStep].id === 'location') {
                setLocationPermission('denied');
            }
            onPermissionsComplete({
                notification: notificationPermission || 'denied',
                location: locationPermission || 'denied'
            });
        }
    };

    const openSettings = () => {
        Alert.alert(
            'Open Settings',
            'You can enable permissions later in your device settings.',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Open Settings',
                    onPress: () => {
                        if (Platform.OS === 'ios') {
                            Linking.openURL('app-settings:');
                        } else {
                            Linking.openSettings();
                        }
                    }
                }
            ]
        );
    };

    const currentPermission = permissions[currentStep];

    return (
        <View className="flex-1 bg-white">
            {/* Background decoration */}
            <View className="absolute top-0 right-0 w-[70%] h-[220px] overflow-hidden">
                <View className="absolute w-[300px] h-[300px] rounded-full bg-madder opacity-[0.05] top-[-100px] right-[-50px]" />
            </View>

            <View className="flex-1 px-6 justify-center">
                <Animated.View
                    style={{
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }]
                    }}
                    className="items-center"
                >
                    {/* Permission Icon */}
                    <Animated.View
                        style={{
                            transform: [{ scale: iconScaleAnim }],
                            backgroundColor: currentPermission.bgColor,
                        }}
                        className="w-24 h-24 rounded-full items-center justify-center mb-8 shadow-lg"
                    >
                        <MaterialIcons
                            name={currentPermission.icon}
                            size={40}
                            color={currentPermission.color}
                        />
                    </Animated.View>

                    {/* Title */}
                    <Text className="text-2xl font-bold text-gray-800 text-center mb-4">
                        {currentPermission.title}
                    </Text>

                    {/* Description */}
                    <Text className="text-base text-gray-600 text-center leading-6 mb-8 px-4">
                        {currentPermission.description}
                    </Text>

                    {/* Progress indicator */}
                    <View className="flex-row items-center mb-8">
                        {permissions.map((_, index) => (
                            <View
                                key={index}
                                className={`w-2 h-2 rounded-full mx-1 ${
                                    index === currentStep ? 'bg-madder' : 
                                    index < currentStep ? 'bg-green-500' : 'bg-gray-300'
                                }`}
                            />
                        ))}
                    </View>

                    {/* Action Buttons */}
                    <View className="w-full space-y-4">
                        <TouchableOpacity
                            onPress={() => handlePermissionRequest(currentPermission.id)}
                            disabled={isProcessing}
                            className="w-full py-4 bg-madder rounded-xl items-center shadow-md"
                            style={{
                                opacity: isProcessing ? 0.7 : 1
                            }}
                        >
                            <Text className="text-white font-semibold text-lg">
                                {isProcessing ? 'Processing...' : 'Allow'}
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            onPress={handleSkip}
                            disabled={isProcessing}
                            className="w-full py-4 bg-gray-100 rounded-xl items-center"
                        >
                            <Text className="text-gray-600 font-medium">
                                Skip for now
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            onPress={openSettings}
                            className="items-center py-2"
                        >
                            <Text className="text-sm text-gray-500 underline">
                                Manage in Settings
                            </Text>
                        </TouchableOpacity>
                    </View>
                </Animated.View>
            </View>

            {/* Bottom info */}
            <View className="px-6 pb-8">
                <View className="flex-row items-center justify-center p-3 bg-blue-50 rounded-lg">
                    <Ionicons name="shield-checkmark" size={16} color="#3B82F6" />
                    <Text className="ml-2 text-xs text-blue-700 text-center">
                        Your privacy is protected. Permissions can be changed anytime in settings.
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default PermissionManager;
