import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Image, ActivityIndicator, Alert, Modal } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useAdmin } from '../../context/AdminContext';
import { updateProduct } from '../../utils/api/productApi';
import { getAllCategories } from '../../utils/api/categoryApi';

const EditProductScreen = ({ route, navigation }) => {
    const { product } = route.params;
    const { updateProduct: updateAdminProduct } = useAdmin();
    const [productData, setProductData] = useState(product);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [categories, setCategories] = useState([]);
    const [categoriesLoading, setCategoriesLoading] = useState(true);
    const [showCategoryModal, setShowCategoryModal] = useState(false);



    // Fetch categories from the backend
    useEffect(() => {
        const fetchCategories = async () => {
            try {
                setCategoriesLoading(true);
                console.log('Fetching categories from API...');
                const response = await getAllCategories();

                if (Array.isArray(response)) {
                    console.log(`Fetched ${response.length} categories`);
                    setCategories(response);
                } else {
                    console.log('Invalid categories response format:', response);
                    setCategories([]);
                }
            } catch (error) {
                console.error('Error fetching categories:', error);
                setCategories([]);
                Alert.alert('Error', 'Failed to load categories. Please try again.');
            } finally {
                setCategoriesLoading(false);
            }
        };

        fetchCategories();
    }, []);

    // Function to calculate discount price from price and discount percentage
    const calculateDiscountPrice = (price, discountPercentage) => {
        if (!price || !discountPercentage) return '';
        const priceValue = parseFloat(price);
        const discountValue = parseFloat(discountPercentage);
        if (isNaN(priceValue) || isNaN(discountValue)) return '';

        const discountedPrice = priceValue * (1 - discountValue / 100);
        return discountedPrice.toFixed(2);
    };

    // Function to calculate discount percentage from price and discount price
    const calculateDiscountPercentage = (price, discountPrice) => {
        if (!price || !discountPrice) return '0';
        const priceValue = parseFloat(price);
        const discountPriceValue = parseFloat(discountPrice);
        if (isNaN(priceValue) || isNaN(discountPriceValue) || priceValue <= 0 || discountPriceValue >= priceValue) return '0';

        const discountPercentage = ((priceValue - discountPriceValue) / priceValue) * 100;
        return discountPercentage.toFixed(0);
    };

    // Function to generate offer text
    const generateOfferText = (discountPercentage) => {
        if (!discountPercentage || parseFloat(discountPercentage) <= 0) return '';
        return `${parseFloat(discountPercentage).toFixed(0)}% OFF`;
    };

    // Function to update price
    const updatePrice = (price) => {
        const updatedData = { ...productData, price };

        // If discount percentage exists, recalculate discount price
        if (updatedData.discountPercentage && parseFloat(updatedData.discountPercentage) > 0) {
            updatedData.discount_price = calculateDiscountPrice(price, updatedData.discountPercentage);
            updatedData.offer = generateOfferText(updatedData.discountPercentage);
        }
        // If discount price exists, recalculate discount percentage
        else if (updatedData.discount_price && parseFloat(updatedData.discount_price) > 0) {
            updatedData.discountPercentage = calculateDiscountPercentage(price, updatedData.discount_price);
            updatedData.offer = generateOfferText(updatedData.discountPercentage);
        }

        setProductData(updatedData);
    };

    // Function to update discount percentage
    const updateDiscountPercentage = (discountPercentage) => {
        // Ensure discount is between 0 and 100
        const discount = Math.min(Math.max(parseFloat(discountPercentage) || 0, 0), 100);

        const updatedData = {
            ...productData,
            discountPercentage: discount.toString()
        };

        // Calculate discount price if price exists
        if (updatedData.price) {
            updatedData.discount_price = calculateDiscountPrice(updatedData.price, discount);
            updatedData.offer = generateOfferText(discount);
        }

        setProductData(updatedData);
    };

    // Function to update discount price
    const updateDiscountPrice = (discountPrice) => {
        const updatedData = { ...productData, discount_price: discountPrice };

        // Calculate discount percentage if price exists
        if (updatedData.price) {
            updatedData.discountPercentage = calculateDiscountPercentage(updatedData.price, discountPrice);
            updatedData.offer = generateOfferText(updatedData.discountPercentage);
        }

        setProductData(updatedData);
    };



    // Ensure we have the latest product data and handle missing fields
    useEffect(() => {
        if (product) {
            // Ensure all required fields exist with default values if missing
            const sanitizedProduct = {
                name: product.name || '',
                price: product.price || 0,
                // Removed stock field
                category: product.category?._id || product.category || '',
                categoryName: product.category?.name || '',
                description: product.description || '',
                image: product.image || 'https://via.placeholder.com/150',
                discountPercentage: product.discountPercentage || 0,
                discount_price: product.discount_price || '',
                offer: product.offer || '',
                isAvailable: product.isAvailable !== undefined ? product.isAvailable : true,
                // Preserve ID and other fields
                _id: product._id,
                id: product.id,
                ...product
            };

            // Calculate discount price if it doesn't exist but discount percentage does
            if (!sanitizedProduct.discount_price && sanitizedProduct.discountPercentage > 0) {
                sanitizedProduct.discount_price = calculateDiscountPrice(
                    sanitizedProduct.price,
                    sanitizedProduct.discountPercentage
                );
            }

            // Generate offer text if it doesn't exist but discount percentage does
            if (!sanitizedProduct.offer && sanitizedProduct.discountPercentage > 0) {
                sanitizedProduct.offer = generateOfferText(sanitizedProduct.discountPercentage);
            }

            setProductData(sanitizedProduct);
        }
    }, [product]);

    const handleUpdate = async () => {
        try {
            setLoading(true);
            setError(null);

            // Validate required fields
            if (!productData.name?.trim()) {
                setError('Product name is required');
                Alert.alert("Validation Error", "Product name is required");
                setLoading(false);
                return;
            }

            if (!productData.price || parseFloat(productData.price) <= 0) {
                setError('Valid price is required');
                Alert.alert("Validation Error", "Please enter a valid price greater than 0");
                setLoading(false);
                return;
            }

            const productId = productData._id || productData.id;
            if (!productId) {
                setError('Product ID is missing');
                Alert.alert("Error", "Product ID is missing. Cannot update product.");
                setLoading(false);
                return;
            }

            // Prepare data for update - ensure numeric fields are properly formatted
            const dataToUpdate = {
                ...productData,
                name: productData.name.trim(),
                price: parseFloat(productData.price) || 0,
                discountPercentage: parseFloat(productData.discountPercentage) || 0,
                discount_price: parseFloat(productData.discount_price) || 0,
                offer: productData.offer || '',
                isAvailable: Boolean(productData.isAvailable)
            };

            console.log(`Updating product ${productId}:`, dataToUpdate);

            // Call API to update product
            const response = await updateProduct(productId, dataToUpdate);

            if (response && response.product) {
                console.log('Product updated successfully:', response.product);

                // Update product in admin context
                updateAdminProduct(productId, response.product);

                Alert.alert(
                    "Success",
                    "Product updated successfully",
                    [{ text: "OK", onPress: () => navigation.goBack() }]
                );
            } else {
                // If API doesn't return the updated product
                console.log('Product updated but no response data');

                // Update product in admin context with local data
                updateAdminProduct(productId, dataToUpdate);

                Alert.alert(
                    "Success",
                    "Product updated successfully",
                    [{ text: "OK", onPress: () => navigation.goBack() }]
                );
            }
        } catch (err) {
            console.error('Error updating product:', err);
            const errorMessage = err.message || 'Failed to update product. Please try again.';
            setError(errorMessage);
            Alert.alert("Error", errorMessage);
        } finally {
            setLoading(false);
        }
    };

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder px-6 pt-8 pb-4 rounded-b-3xl shadow-md">
                <View className="flex-row items-center">
                    <TouchableOpacity onPress={() => navigation.goBack()} className="mr-3 bg-white/20 p-2 rounded-full">
                        <MaterialIcons name="arrow-back" size={22} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">Edit Product</Text>
                </View>
            </View>

            {loading ? (
                <View className="flex-1 justify-center items-center">
                    <ActivityIndicator size="large" color="#A31621" />
                    <Text className="mt-4 text-gray-600">Updating product...</Text>
                </View>
            ) : (
                <ScrollView className="px-4 pt-6">
                    {error && (
                        <View className="bg-red-100 p-4 rounded-xl mb-6 border-l-4 border-l-red-500 shadow-sm">
                            <View className="flex-row items-center">
                                <MaterialIcons name="error-outline" size={20} color="#DC2626" />
                                <Text className="text-red-700 font-medium ml-2">{error}</Text>
                            </View>
                        </View>
                    )}

                    <View className="bg-white p-4 rounded-xl mb-4 shadow-sm">
                        <Text className="text-gray-800 font-medium text-base mb-3 ml-1">Product Image</Text>
                        <View className="overflow-hidden rounded-lg shadow-sm items-center justify-center bg-gray-50">
                            <Image
                                source={{ uri: productData.image || 'https://via.placeholder.com/150' }}
                                style={{ width: '100%', height: 200, aspectRatio: 1.5 }}
                                resizeMode="contain"
                            />
                        </View>
                    </View>

                    <View className="bg-white p-4 rounded-xl mb-4 shadow-sm">
                        <Text className="text-gray-800 font-medium text-base mb-3 ml-1">Basic Information</Text>

                        <View className="mb-3">
                            <Text className="text-gray-700 mb-1 ml-1 text-sm">Product Name *</Text>
                            <TextInput
                                value={productData.name}
                                onChangeText={(text) => setProductData({ ...productData, name: text })}
                                className="bg-snow p-3 rounded-lg"
                                placeholder="Enter product name"
                                placeholderTextColor="#9CA3AF"
                                style={{ fontSize: 14 }}
                            />
                        </View>

                        <View className="mb-3">
                            <Text className="text-gray-700 mb-1 ml-1 text-sm">Price (₹) *</Text>
                            <TextInput
                                value={productData.price?.toString()}
                                onChangeText={(text) => updatePrice(text)}
                                className="bg-snow p-3 rounded-lg"
                                keyboardType="numeric"
                                placeholder="Enter price"
                                placeholderTextColor="#9CA3AF"
                                style={{ fontSize: 14 }}
                            />
                        </View>
                    </View>

                    <View className="bg-white p-4 rounded-xl mb-4 shadow-sm">
                        <Text className="text-gray-800 font-medium text-base mb-3 ml-1">Discount Information</Text>

                        <View className="mb-3">
                            <Text className="text-gray-700 mb-1 ml-1 text-sm">Discount Percentage (%)</Text>
                            <TextInput
                                value={productData.discountPercentage?.toString()}
                                onChangeText={(text) => updateDiscountPercentage(text)}
                                className="bg-snow p-3 rounded-lg"
                                keyboardType="numeric"
                                placeholder="Enter discount percentage (0-100)"
                                placeholderTextColor="#9CA3AF"
                                style={{ fontSize: 14 }}
                            />
                        </View>

                        <View className="mb-3">
                            <Text className="text-gray-700 mb-1 ml-1 text-sm">Discount Price (₹)</Text>
                            <TextInput
                                value={productData.discount_price?.toString()}
                                onChangeText={(text) => updateDiscountPrice(text)}
                                className="bg-snow p-3 rounded-lg"
                                keyboardType="numeric"
                                placeholder="Enter discounted price"
                                placeholderTextColor="#9CA3AF"
                                style={{ fontSize: 14 }}
                            />
                        </View>

                    {productData.offer && (
                        <View className="mt-1 mb-2">
                            <Text className="text-gray-700 mb-1 ml-1 text-sm">Current Offer</Text>
                            <View className="bg-green-50 p-3 rounded-lg flex-row items-center">
                                <Text className="text-green-700 font-medium text-sm">
                                    {productData.offer}
                                </Text>
                            </View>
                        </View>
                    )}
                    </View>

                    {/* Stock field removed */}

                    <View className="bg-white p-4 rounded-xl mb-4 shadow-sm">
                        <Text className="text-gray-800 font-medium text-base mb-3 ml-1">Category Information</Text>

                        <View className="mb-2">
                            <Text className="text-gray-700 mb-1 ml-1 text-sm">Product Category</Text>
                            <TouchableOpacity
                                className="bg-snow p-3 rounded-lg flex-row justify-between items-center"
                                onPress={() => setShowCategoryModal(true)}
                                disabled={categoriesLoading}
                            >
                                {categoriesLoading ? (
                                    <View className="flex-row items-center">
                                        <ActivityIndicator size="small" color="#A31621" />
                                        <Text className="text-gray-400 ml-2 text-sm">Loading categories...</Text>
                                    </View>
                                ) : (
                                    <Text className={`${productData.categoryName ? "text-gray-800" : "text-gray-400"}`} style={{ fontSize: 14 }}>
                                        {productData.categoryName || "Select Category"}
                                    </Text>
                                )}
                                <MaterialIcons name="arrow-drop-down" size={20} color="#666" />
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View className="bg-white p-4 rounded-xl mb-4 shadow-sm">
                        <Text className="text-gray-800 font-medium text-base mb-3 ml-1">Product Availability</Text>

                        <View className="flex-row items-center justify-between mb-3">
                            <View className="flex-1 mr-3">
                                <Text className="text-base text-gray-800">
                                    {productData.isAvailable ? 'Product is Available' : 'Product is Unavailable'}
                                </Text>
                                <Text className="text-gray-500 mt-1 text-xs">
                                    {productData.isAvailable
                                        ? 'Customers can purchase this product'
                                        : 'This product will be shown as unavailable to customers'}
                                </Text>
                            </View>
                            <TouchableOpacity
                                onPress={() => setProductData({ ...productData, isAvailable: !productData.isAvailable })}
                                className={`w-12 h-7 rounded-full ${productData.isAvailable ? 'bg-green-500' : 'bg-gray-300'} justify-center`}
                                style={{ padding: 2 }}
                            >
                                <View
                                    className="w-5 h-5 rounded-full bg-white"
                                    style={{
                                        marginLeft: productData.isAvailable ? 19 : 2,
                                    }}
                                />
                            </TouchableOpacity>
                        </View>

                        <View className={`p-3 rounded-lg ${productData.isAvailable ? 'bg-green-50' : 'bg-red-50'}`}>
                            <Text className={`${productData.isAvailable ? 'text-green-700' : 'text-red-700'} text-xs`}>
                                {productData.isAvailable
                                    ? 'This product will be displayed normally in the app.'
                                    : 'This product will appear darkened with an "Unavailable" label.'}
                            </Text>
                        </View>
                    </View>

                    <View className="bg-white p-4 rounded-xl mb-4 shadow-sm">
                        <Text className="text-gray-800 font-medium text-base mb-3 ml-1">Product Description</Text>

                        <View className="mb-2">
                            <Text className="text-gray-700 mb-1 ml-1 text-sm">Description</Text>
                            <TextInput
                                value={productData.description}
                                onChangeText={(text) => setProductData({ ...productData, description: text })}
                                className="bg-snow p-3 rounded-lg min-h-[100px]"
                                placeholder="Enter product description"
                                multiline
                                numberOfLines={4}
                                textAlignVertical="top"
                                placeholderTextColor="#9CA3AF"
                                style={{ fontSize: 14 }}
                            />
                        </View>
                    </View>

                    <TouchableOpacity
                        onPress={handleUpdate}
                        className="bg-madder p-3 rounded-xl items-center mt-4 mb-8"
                    >
                        <Text className="text-white font-medium">Update Product</Text>
                    </TouchableOpacity>
                </ScrollView>
            )}

            {/* Category Selection Modal */}
            <Modal
                visible={showCategoryModal}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowCategoryModal(false)}
            >
                <View className="flex-1 justify-end bg-black/50">
                    <View className="bg-white rounded-t-3xl p-5 h-1/2 shadow-lg">
                        <View className="flex-row justify-between items-center mb-5">
                            <Text className="text-xl font-bold text-gray-800">Select Category</Text>
                            <TouchableOpacity
                                onPress={() => setShowCategoryModal(false)}
                                className="bg-gray-100 p-2 rounded-full"
                            >
                                <MaterialIcons name="close" size={24} color="#666" />
                            </TouchableOpacity>
                        </View>

                        {categoriesLoading ? (
                            <View className="flex-1 justify-center items-center">
                                <ActivityIndicator size="large" color="#A31621" />
                                <Text className="mt-4 text-gray-600">Loading categories...</Text>
                            </View>
                        ) : categories.length === 0 ? (
                            <View className="flex-1 justify-center items-center">
                                <MaterialIcons name="category" size={48} color="#ccc" />
                                <Text className="text-gray-400 mt-2">No categories found</Text>
                            </View>
                        ) : (
                            <ScrollView className="px-2">
                                {categories.map((category) => (
                                    <TouchableOpacity
                                        key={category._id}
                                        className={`py-4 px-3 mb-2 rounded-xl border border-gray-100 ${
                                            productData.category === category._id ? 'bg-madder/10 border-madder' : ''
                                        }`}
                                        onPress={() => {
                                            setProductData({
                                                ...productData,
                                                category: category._id,
                                                categoryName: category.name
                                            });
                                            setShowCategoryModal(false);
                                        }}
                                    >
                                        <View className="flex-row items-center">
                                            {category.image ? (
                                                <Image
                                                    source={{ uri: category.image }}
                                                    className="w-10 h-10 rounded-full mr-3"
                                                    resizeMode="cover"
                                                />
                                            ) : (
                                                <View className="w-10 h-10 rounded-full bg-madder/10 mr-3 items-center justify-center">
                                                    <MaterialIcons name="category" size={20} color="#A31621" />
                                                </View>
                                            )}
                                            <Text className={`text-lg ${productData.category === category._id ? 'text-madder font-bold' : 'text-gray-800'}`}>
                                                {category.name}
                                            </Text>
                                            {productData.category === category._id && (
                                                <MaterialIcons name="check-circle" size={20} color="#A31621" className="ml-auto" />
                                            )}
                                        </View>
                                    </TouchableOpacity>
                                ))}
                                <View className="h-10" />
                            </ScrollView>
                        )}
                    </View>
                </View>
            </Modal>


        </View>
    );
};

export default EditProductScreen;