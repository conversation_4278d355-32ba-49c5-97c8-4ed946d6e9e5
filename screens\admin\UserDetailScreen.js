import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Image,
    FlatList,
    ActivityIndicator,
    Alert,
    RefreshControl,
    PanResponder,
    Dimensions,
    Linking
} from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { getUserById, updateUser, getAllUsers } from '../../utils/api/userApi';
import { getOrdersByUserId } from '../../utils/api/orderApi';

const UserDetailScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { userId } = route.params;

    const [user, setUser] = useState(null);
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [error, setError] = useState(null);
    const [activeTab, setActiveTab] = useState('profile');
    const [orderFilter, setOrderFilter] = useState('all'); // Options: 'all', 'pending', 'out-for-delivery', 'delivered'

    // Reference for ScrollView
    const scrollViewRef = useRef(null);

    // Screen width for swipe calculations
    const screenWidth = Dimensions.get('window').width;

    // Handle swipe between tabs
    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => false,
            onMoveShouldSetPanResponder: (_, gestureState) => {
                return Math.abs(gestureState.dx) > 50 && Math.abs(gestureState.dy) < 100;
            },
            onPanResponderRelease: (_, gestureState) => {
                const tabOptions = ['profile', 'orders'];
                const currentIndex = tabOptions.indexOf(activeTab);

                // Swipe right to left (next tab)
                if (gestureState.dx < -50 && currentIndex < 1) {
                    setActiveTab('orders');
                    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
                }

                // Swipe left to right (previous tab)
                if (gestureState.dx > 50 && currentIndex > 0) {
                    setActiveTab('profile');
                    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
                }
            }
        })
    ).current;

    // Normalize order status
    const normalizeOrderStatus = useCallback((status) => {
        if (!status) return 'pending';

        const statusLower = status.toLowerCase();

        // Map various status formats to our standard ones
        if (statusLower.includes('pending') || statusLower.includes('placed') || statusLower === 'new') {
            return 'pending';
        } else if (statusLower.includes('transit') || statusLower.includes('out') || statusLower.includes('shipping') || statusLower.includes('delivery')) {
            return 'out-for-delivery';
        } else if (statusLower.includes('deliver') || statusLower.includes('complete')) {
            return 'delivered';
        } else if (statusLower.includes('cancel')) {
            return 'cancelled';
        }

        return 'pending'; // Default fallback
    }, []);

    // Normalize order data to ensure consistent field names
    const normalizeOrderData = useCallback((order) => {
        // Calculate a consistent total amount
        const totalAmount =
            order.totalAmount ||
            order.total ||
            order.discount_price ||
            order.discountPrice ||
            order.finalTotal ||
            order.total_amount ||
            order.originalAmount ||
            (order.items && order.items.reduce((sum, item) => sum + ((item.price || item.discount_price || 0) * (item.quantity || 1)), 0)) ||
            0;

        // Normalize the order object
        return {
            ...order,
            normalizedTotal: totalAmount,
            normalizedStatus: normalizeOrderStatus(order.status)
        };
    }, [normalizeOrderStatus]);

    // Fetch user data from API - optimized for faster loading
    const fetchUserData = useCallback(async (showFullLoading = true) => {
        try {
            if (showFullLoading) {
                setLoading(true);
            }
            setError(null);

            console.log(`Fetching data for user: ${userId}`);

            // Fetch user data and orders in parallel for faster loading
            const [userResponse, ordersResponse] = await Promise.all([
                getUserById(userId),
                getOrdersByUserId(userId)
            ]);

            // Process user data
            if (userResponse && userResponse.user) {
                console.log('User found successfully');
                setUser(userResponse.user);
            } else {
                setError('User not found. The user may have been deleted or you may not have permission to view it.');
                return;
            }

            // Process orders data
            let processedOrders = [];

            // Check if ordersResponse has orders property
            if (ordersResponse && ordersResponse.orders && Array.isArray(ordersResponse.orders)) {
                console.log(`Found ${ordersResponse.orders.length} orders for user`);
                processedOrders = ordersResponse.orders;
            }
            // Check if ordersResponse is an array directly
            else if (ordersResponse && Array.isArray(ordersResponse)) {
                console.log(`Found ${ordersResponse.length} orders for user`);
                processedOrders = ordersResponse;
            }
            // If response has no orders property but has data
            else if (ordersResponse && typeof ordersResponse === 'object') {
                console.log('Received order data in unexpected format, attempting to parse');
                Object.keys(ordersResponse).forEach(key => {
                    if (Array.isArray(ordersResponse[key])) {
                        processedOrders.push(...ordersResponse[key]);
                    } else if (typeof ordersResponse[key] === 'object' && ordersResponse[key] !== null) {
                        processedOrders.push(ordersResponse[key]);
                    }
                });
                console.log(`Extracted ${processedOrders.length} orders from response`);
            }
            else {
                console.log('No orders found for user');
                processedOrders = [];
            }

            // Normalize order data efficiently
            const normalizedOrders = processedOrders.map(order => {
                const totalAmount =
                    order.totalAmount ||
                    order.total ||
                    order.discount_price ||
                    order.discountPrice ||
                    order.finalTotal ||
                    order.total_amount ||
                    order.originalAmount ||
                    (order.items && order.items.reduce((sum, item) => sum + ((item.price || item.discount_price || 0) * (item.quantity || 1)), 0)) ||
                    0;

                const normalizedStatus = (() => {
                    if (!order.status) return 'pending';
                    const statusLower = order.status.toLowerCase();
                    if (statusLower.includes('pending') || statusLower.includes('placed') || statusLower === 'new') {
                        return 'pending';
                    } else if (statusLower.includes('transit') || statusLower.includes('out') || statusLower.includes('shipping') || statusLower.includes('delivery')) {
                        return 'out-for-delivery';
                    } else if (statusLower.includes('deliver') || statusLower.includes('complete')) {
                        return 'delivered';
                    } else if (statusLower.includes('cancel')) {
                        return 'cancelled';
                    }
                    return 'pending';
                })();

                return {
                    ...order,
                    normalizedTotal: totalAmount,
                    normalizedStatus: normalizedStatus
                };
            });
            console.log('Setting normalized orders:', normalizedOrders.length);
            setOrders(normalizedOrders);

        } catch (error) {
            console.error('Error fetching user data:', error);
            setError('Failed to load user data. Please check your connection and try again.');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [userId]);

    // Initial fetch on mount
    useEffect(() => {
        fetchUserData();
    }, [fetchUserData]);

    // Refresh data when screen comes into focus
    useFocusEffect(
        useCallback(() => {
            fetchUserData(false);
            return () => {};
        }, [fetchUserData])
    );

    // Handle pull-to-refresh
    const onRefresh = useCallback(() => {
        setRefreshing(true);
        fetchUserData(false);
    }, [fetchUserData]);

    if (loading) {
        return (
            <View className="flex-1 bg-snow justify-center items-center">
                <ActivityIndicator size="large" color="#A31621" />
                <Text className="text-gray-600 mt-4">Loading user data...</Text>
            </View>
        );
    }

    if (error || !user) {
        return (
            <View className="flex-1 bg-snow justify-center items-center p-4">
                <MaterialIcons name="error-outline" size={64} color="#A31621" />
                <Text className="text-lg text-gray-700 mt-4 text-center">{error || "User not found"}</Text>
                <TouchableOpacity
                    className="mt-6 bg-madder px-6 py-3 rounded-full"
                    onPress={() => navigation.goBack()}
                >
                    <Text className="text-white font-medium">Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }



    // Filter orders based on selected filter
    const filteredOrders = orders.filter(order => {
        if (orderFilter === 'all') return true;
        return order.normalizedStatus === orderFilter;
    });

    // Calculate user statistics - simplified
    const totalOrders = orders.length;

    // Format date for display
    const formatDate = (dateString) => {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    // Removed user status toggle functionality

    // Render profile tab content
    const renderProfileTab = () => (
        <View>
            {/* User Basic Info Card */}
            <View className="bg-white rounded-xl overflow-hidden mb-6 shadow-sm">
                {/* Header with background */}
                <View className="bg-madder p-5">
                    <View className="flex-row items-center">
                        <View className="w-20 h-20 rounded-full bg-white items-center justify-center mr-4 shadow-sm">
                            <Text className="text-3xl font-bold text-madder">
                                {user.name ? user.name.charAt(0).toUpperCase() : '?'}
                            </Text>
                        </View>
                        <View className="flex-1">
                            <Text className="text-2xl font-bold text-white">{user.name || 'Unknown'}</Text>
                            <View className="flex-row items-center mt-1">
                                <MaterialIcons name="phone" size={16} color="white" />
                                <Text className="text-white ml-1">{user.phone || user.number || 'No phone'}</Text>
                            </View>
                            {user.email && (
                                <View className="flex-row items-center mt-1">
                                    <MaterialIcons name="email" size={16} color="white" />
                                    <Text className="text-white ml-1" numberOfLines={1}>{user.email}</Text>
                                </View>
                            )}
                        </View>
                    </View>
                </View>

                {/* Action Buttons */}
                <View className="border-b border-gray-100">
                    <TouchableOpacity
                        className="py-4 flex-row justify-center items-center"
                        onPress={() => {
                            if (user.phone || user.number) {
                                Alert.alert(
                                    "Contact User",
                                    `Call ${user.name || 'this user'} at ${user.phone || user.number}?`,
                                    [
                                        {
                                            text: "Cancel",
                                            style: "cancel"
                                        },
                                        {
                                            text: "Call",
                                            style: "default",
                                            onPress: () => Linking.openURL(`tel:${user.phone || user.number}`)
                                        }
                                    ],
                                    { cancelable: true }
                                );
                            } else {
                                Alert.alert(
                                    "No Phone Number",
                                    "This user doesn't have a phone number provided.",
                                    [{ text: "OK", style: "default" }],
                                    { cancelable: true }
                                );
                            }
                        }}
                    >
                        <MaterialIcons name="phone" size={18} color="#A31621" />
                        <Text className="text-madder font-medium ml-2">Call</Text>
                    </TouchableOpacity>
                </View>

                {/* User Details */}
                <View className="p-5">
                    <Text className="text-lg font-bold text-madder mb-4">Account Information</Text>
                    <View className="flex-row justify-between">
                        <View className="flex-1 pr-2">
                            <Text className="text-xs text-gray-500 uppercase">Joined On</Text>
                            <Text className="text-gray-800 font-medium">{formatDate(user.createdAt)}</Text>
                        </View>
                        <View className="flex-1 pl-2">
                            <Text className="text-xs text-gray-500 uppercase">Loyalty Coins</Text>
                            <View className="flex-row items-center">
                                <Text className="text-gray-800 font-medium">{user.coins || 0}</Text>
                                <View className="bg-madder/10 px-2 py-0.5 rounded-full ml-2">
                                    <Text className="text-xs font-medium text-madder">coins</Text>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </View>

            {/* User Order Summary Card */}
            <View className="bg-white rounded-xl overflow-hidden mb-6 shadow-sm">
                <View className="bg-madder p-4">
                    <View className="flex-row items-center justify-between">
                        <Text className="text-lg font-bold text-white">Order Summary</Text>
                    </View>
                </View>

                {/* Order Summary */}
                <View className="p-5">
                    <View className="flex-row justify-between items-center">
                        <View className="flex-row items-center">
                            <MaterialIcons name="receipt" size={20} color="#A31621" />
                            <Text className="text-madder font-medium ml-2">Total Orders</Text>
                        </View>
                        <Text className="text-xl font-bold text-gray-800">
                            {orders.length}
                        </Text>
                    </View>
                </View>
            </View>


        </View>
    );


    // Render orders tab content
    const renderOrdersTab = () => {
        console.log('Rendering orders tab with:', {
            totalOrders: orders.length,
            filteredOrders: filteredOrders.length,
            orderFilter: orderFilter,
            firstOrder: orders[0]
        });

        return (
            <View>
                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                    <View className="flex-row items-center justify-between mb-4">
                        <Text className="text-lg font-bold text-gray-800">Recent Orders</Text>
                        <View className="bg-madder/10 px-3 py-1 rounded-full">
                            <Text className="text-madder font-medium">{orders.length} total</Text>
                        </View>
                    </View>

                <View className="flex-row justify-between mb-4 bg-gray-100 p-1 rounded-xl">
                    <TouchableOpacity
                        className={`flex-1 py-2 rounded-xl ${orderFilter === 'all' ? 'bg-madder' : 'bg-transparent'}`}
                        onPress={() => setOrderFilter('all')}
                    >
                        <Text className={`text-center font-medium ${orderFilter === 'all' ? 'text-white' : 'text-gray-700'}`}>
                            All
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`flex-1 py-2 rounded-xl ${orderFilter === 'pending' ? 'bg-madder' : 'bg-transparent'}`}
                        onPress={() => setOrderFilter('pending')}
                    >
                        <Text className={`text-center font-medium ${orderFilter === 'pending' ? 'text-white' : 'text-gray-700'}`}>
                            Pending
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`flex-1 py-2 rounded-xl ${orderFilter === 'out-for-delivery' ? 'bg-madder' : 'bg-transparent'}`}
                        onPress={() => setOrderFilter('out-for-delivery')}
                    >
                        <Text className={`text-center font-medium ${orderFilter === 'out-for-delivery' ? 'text-white' : 'text-gray-700'}`}>
                            Out for Delivery
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`flex-1 py-2 rounded-xl ${orderFilter === 'delivered' ? 'bg-madder' : 'bg-transparent'}`}
                        onPress={() => setOrderFilter('delivered')}
                    >
                        <Text className={`text-center font-medium ${orderFilter === 'delivered' ? 'text-white' : 'text-gray-700'}`}>
                            Delivered
                        </Text>
                    </TouchableOpacity>
                </View>

                {filteredOrders.length > 0 ? (
                    filteredOrders
                        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                        .map(order => {
                            const statusColors = {
                                'pending': { bg: 'bg-yellow-100', text: 'text-yellow-700', icon: 'schedule' },
                                'out-for-delivery': { bg: 'bg-blue-100', text: 'text-blue-700', icon: 'local-shipping' },
                                'delivered': { bg: 'bg-green-100', text: 'text-green-700', icon: 'check-circle' },
                                'cancelled': { bg: 'bg-red-100', text: 'text-red-700', icon: 'cancel' }
                            };

                            const statusColor = statusColors[order.normalizedStatus] || statusColors.pending;
                            const statusIcon = statusColors[order.normalizedStatus]?.icon || 'help';

                            return (
                                <TouchableOpacity
                                    key={order.id || order._id}
                                    className="bg-gray-50 rounded-xl p-4 mb-3"
                                    onPress={() => navigation.navigate('AdminOrdersScreen', { orderId: order.id || order._id })}
                                    activeOpacity={0.7}
                                >
                                    <View className="flex-row justify-between items-start mb-3">
                                        <View className="flex-row items-center">
                                            <View className={`w-10 h-10 rounded-full ${statusColor.bg} items-center justify-center mr-3`}>
                                                <MaterialIcons name={statusIcon} size={20} color={statusColor.text.replace('text-', '')} />
                                            </View>
                                            <View>
                                                <Text className="font-bold text-gray-800">Order #{order.orderNumber || order.order_number || (order.id || order._id).substring(0, 8)}</Text>
                                                <Text className="text-gray-500 text-sm">{formatDate(order.createdAt || order.created_at || new Date())}</Text>
                                            </View>
                                        </View>
                                        <View className={`${statusColor.bg} px-3 py-1 rounded-full`}>
                                            <Text className={`${statusColor.text} text-xs font-medium`}>
                                                {order.normalizedStatus === 'pending' ? 'Pending' :
                                                    order.normalizedStatus === 'out-for-delivery' ? 'Out for Delivery' :
                                                        order.normalizedStatus === 'delivered' ? 'Delivered' : 'Cancelled'}
                                            </Text>
                                        </View>
                                    </View>

                                    <View className="bg-white p-3 rounded-lg">
                                        <View className="flex-row justify-between mb-2">
                                            <View className="flex-row items-center">
                                                <MaterialIcons name="shopping-basket" size={16} color="#A31621" />
                                                <Text className="text-gray-700 ml-2">{order.items ? order.items.length : 0} items</Text>
                                            </View>
                                            <Text className="font-bold text-madder">₹{order.normalizedTotal.toFixed(0)}</Text>
                                        </View>

                                        <View className="flex-row justify-between">
                                            <View className="flex-row items-center">
                                                <MaterialIcons name="payment" size={16} color="#A31621" />
                                                <Text className="text-gray-700 ml-2">
                                                    {['COD', 'cod'].includes(order.paymentMethod) ? 'Cash on Delivery' :
                                                     ['UPI', 'upi'].includes(order.paymentMethod) ? 'UPI Payment' :
                                                     order.paymentMethod || 'Cash on Delivery'}
                                                </Text>
                                            </View>

                                            <View className="flex-row items-center">
                                                <Text className="text-madder text-sm font-medium mr-1">View Details</Text>
                                                <MaterialIcons name="chevron-right" size={16} color="#A31621" />
                                            </View>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            );
                        })
                ) : (
                    <View className="bg-gray-50 rounded-xl p-8 items-center justify-center">
                        <MaterialIcons name="receipt-long" size={48} color="#A31621" opacity={0.2} />
                        <Text className="text-gray-700 mt-4 font-medium">No orders found</Text>
                        <Text className="text-gray-500 text-center mt-1">
                            {orderFilter !== 'all'
                                ? `No ${orderFilter} orders found for this user`
                                : "This user hasn't placed any orders yet"}
                        </Text>
                        {orderFilter !== 'all' && (
                            <TouchableOpacity
                                className="mt-4 bg-madder px-4 py-2 rounded-lg"
                                onPress={() => setOrderFilter('all')}
                            >
                                <Text className="text-white font-medium">View All Orders</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                )}
            </View>
        </View>
        );
    };

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-4 pt-16 rounded-b-3xl pb-4">
                <View className="flex-row items-center">
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        className="mr-4 bg-white/20 p-2 rounded-full"
                    >
                        <MaterialIcons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-2xl text-white font-bold">User Details</Text>
                </View>
            </View>

            {/* Tab Navigation */}
            <View className="flex-row bg-white shadow-sm">
                <TouchableOpacity
                    className={`flex-1 py-4 ${activeTab === 'profile' ? 'border-b-2 border-madder' : ''}`}
                    onPress={() => {
                        setActiveTab('profile');
                        scrollViewRef.current?.scrollTo({ y: 0, animated: true });
                    }}
                >
                    <View className="flex-row items-center justify-center">
                        <MaterialIcons
                            name="person"
                            size={18}
                            color={activeTab === 'profile' ? '#A31621' : '#6B7280'}
                        />
                        <Text className={`ml-1 font-medium ${activeTab === 'profile' ? 'text-madder' : 'text-gray-600'}`}>
                            Profile
                        </Text>
                    </View>
                </TouchableOpacity>

                <TouchableOpacity
                    className={`flex-1 py-4 ${activeTab === 'orders' ? 'border-b-2 border-madder' : ''}`}
                    onPress={() => {
                        setActiveTab('orders');
                        scrollViewRef.current?.scrollTo({ y: 0, animated: true });
                    }}
                >
                    <View className="flex-row items-center justify-center">
                        <MaterialIcons
                            name="receipt-long"
                            size={18}
                            color={activeTab === 'orders' ? '#A31621' : '#6B7280'}
                        />
                        <Text className={`ml-1 font-medium ${activeTab === 'orders' ? 'text-madder' : 'text-gray-600'}`}>
                            Orders
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>

            <ScrollView
                ref={scrollViewRef}
                className="flex-1 p-4"
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                {...panResponder.panHandlers}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={["#A31621"]}
                        tintColor="#A31621"
                    />
                }
            >
                {activeTab === 'profile' && renderProfileTab()}
                {activeTab === 'orders' && renderOrdersTab()}
            </ScrollView>
        </View>
    );
};

export default UserDetailScreen;
