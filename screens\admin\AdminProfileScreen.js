import React, { useState, useRef, useCallback } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert, Modal, Animated } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';

const AdminProfileScreen = () => {
    const navigation = useNavigation();
    const { logout: authLogout } = useAuth();
    const [showLogoutModal, setShowLogoutModal] = useState(false);

    // Animation refs for logout modal
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.8)).current;

    const handleLogout = useCallback(() => {
        setShowLogoutModal(true);

        // Animate the modal appearance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 8,
                tension: 40,
                useNativeDriver: true,
            })
        ]).start();
    }, [fadeAnim, scaleAnim]);

    const confirmLogout = useCallback(async () => {
        try {
            // Call the auth logout function
            await authLogout();
            console.log('Auth logout successful for admin');

            // Reset navigation to login screen
            navigation.reset({
                index: 0,
                routes: [{ name: 'PreLoginScreen' }],
            });
        } catch (error) {
            console.error('Error during logout:', error);
            // Still navigate to login screen even if there's an error
            navigation.reset({
                index: 0,
                routes: [{ name: 'PreLoginScreen' }],
            });
        }
    }, [authLogout, navigation]);

    const cancelLogout = useCallback(() => {
        // Animate the modal disappearance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start(() => {
            setShowLogoutModal(false);
        });
    }, [fadeAnim, scaleAnim]);

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder pt-12 pb-6 px-4 rounded-b-3xl">
                <View className="flex-row items-center">
                    <Text className="text-xl text-white font-bold">Admin Profile</Text>
                </View>
            </View>

            <ScrollView className="p-4" contentContainerStyle={{ paddingBottom: 100 }}>
                <View className="bg-white rounded-xl p-4 mb-4">
                    <View className="items-center">
                        <View className="w-20 h-20 rounded-full bg-madder/10 items-center justify-center mb-3">
                            <MaterialIcons name="person" size={40} color="#A31621" />
                        </View>
                        <Text className="text-xl font-bold">Admin</Text>
                        <Text className="text-gray-600">8825549901</Text>
                    </View>
                </View>

                <View className="bg-white rounded-xl p-4 mb-4">
                    <Text className="text-lg font-bold mb-3">Management</Text>



                    <TouchableOpacity
                        className="flex-row items-center py-3"
                        onPress={() => navigation.navigate('DeliveryPartnerManagementScreen')}
                    >
                        <MaterialIcons name="delivery-dining" size={24} color="#A31621" />
                        <Text className="ml-3 text-gray-800 font-medium">Manage Delivery Partners</Text>
                        <MaterialIcons name="chevron-right" size={24} color="#A31621" style={{ marginLeft: 'auto' }} />
                    </TouchableOpacity>
                </View>

                <TouchableOpacity
                    className="bg-white rounded-xl p-4 flex-row items-center justify-center mb-4"
                    onPress={handleLogout}
                >
                    <MaterialIcons name="logout" size={24} color="#A31621" />
                    <Text className="ml-3 text-madder font-medium">Logout</Text>
                </TouchableOpacity>
            </ScrollView>

            {/* Logout Modal */}
            <Modal
                visible={showLogoutModal}
                transparent={true}
                animationType="none"
                onRequestClose={cancelLogout}
            >
                <Animated.View
                    className="flex-1 justify-center items-center bg-black/30"
                    style={{ opacity: fadeAnim }}
                >
                    <Animated.View
                        className="bg-white rounded-2xl p-6 m-4 items-center"
                        style={{
                            transform: [{ scale: scaleAnim }],
                            width: '80%',
                            maxWidth: 320
                        }}
                    >
                        <View className="w-16 h-16 bg-madder/10 rounded-full items-center justify-center mb-4">
                            <MaterialIcons name="logout" size={32} color="#A31621" />
                        </View>
                        <Text className="text-xl font-bold text-gray-800 mb-2">Logout</Text>
                        <Text className="text-gray-600 text-center mb-6">Are you sure you want to logout from your account?</Text>

                        <View className="flex-row w-full">
                            <TouchableOpacity
                                className="flex-1 bg-gray-200 py-3 rounded-xl mr-2 items-center"
                                onPress={cancelLogout}
                            >
                                <Text className="font-medium text-gray-700">Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                className="flex-1 bg-madder py-3 rounded-xl ml-2 items-center"
                                onPress={confirmLogout}
                            >
                                <Text className="font-medium text-white">Logout</Text>
                            </TouchableOpacity>
                        </View>
                    </Animated.View>
                </Animated.View>
            </Modal>
        </View>
    );
};

export default AdminProfileScreen;