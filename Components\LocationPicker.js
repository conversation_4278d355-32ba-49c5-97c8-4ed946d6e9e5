import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ActivityIndicator,
    Alert,
    Dimensions,
    Platform,
    TextInput,
    KeyboardAvoidingView,
    Animated
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { isWithinDeliveryZone, isValidPincode } from '../utils/locationUtils';
import { useLocation } from '../context/LocationContext';

// Dynamically import MapView to handle potential errors
let MapView, Marker;
try {
    const Maps = require('react-native-maps');
    MapView = Maps.default;
    Marker = Maps.Marker;
} catch (error) {
    console.error('Error loading react-native-maps:', error);
    // Create placeholder components if maps can't be loaded
    MapView = ({ children, ...props }) => (
        <View style={[props.style, { justifyContent: 'center', alignItems: 'center' }]}>
            <Text>Map not available</Text>
        </View>
    );
    Marker = ({ children }) => <View>{children}</View>;
}

const { width, height } = Dimensions.get('window');
const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.01;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

const LocationPicker = ({
    onLocationSelected,
    initialLocation = null,
    onValidationChange = () => {},
    showPlacesAutocomplete = true
}) => {
    const [location, setLocation] = useState(initialLocation || null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [isWithinZone, setIsWithinZone] = useState(true);
    const [searchText, setSearchText] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [hasShownDragHint, setHasShownDragHint] = useState(false);
    const mapRef = useRef(null);
    const markerRef = useRef(null);
    const searchInputRef = useRef(null);
    const hasShownDeliveryZoneWarning = useRef(false);

    // Animation values for the pulsating effect
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const pulseOpacity = useRef(new Animated.Value(0.7)).current;

    useEffect(() => {
        const initializeLocation = async () => {
            if (initialLocation) {
                setLocation(initialLocation);
                return;
            }

            // Try to get user's current location directly
            await getCurrentLocationOnMount();
        };

        initializeLocation();
    }, [initialLocation]);

    // Get current location on component mount
    const getCurrentLocationOnMount = async () => {
        try {
            setLoading(true);
            setError(null);

            // Check if location permission is already granted
            const { status: existingStatus } = await Location.getForegroundPermissionsAsync();

            let permissionStatus = existingStatus;

            // If permission not granted, request it
            if (existingStatus !== 'granted') {
                const { status } = await Location.requestForegroundPermissionsAsync();
                permissionStatus = status;
            }

            if (permissionStatus !== 'granted') {
                // If permission denied, fall back to default location
                console.log('Location permission denied, using default location');
                const defaultLocation = {
                    latitude: 12.8997028,
                    longitude: 79.136073
                };
                setLocation(defaultLocation);
                setLoading(false);
                return;
            }

            // Get current position
            const position = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.High,
                maximumAge: 10000
            });

            const newLocation = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude
            };

            setLocation(newLocation);
            validateLocation(newLocation);

            // Animate to user's location
            if (mapRef.current) {
                mapRef.current.animateToRegion({
                    ...newLocation,
                    latitudeDelta: LATITUDE_DELTA,
                    longitudeDelta: LONGITUDE_DELTA
                }, 1000);
            }

        } catch (err) {
            console.error('Error getting current location on mount:', err);
            // Fall back to default location on error
            const defaultLocation = {
                latitude: 12.8997028,
                longitude: 79.136073
            };
            setLocation(defaultLocation);
            setError('Could not get your location, using default area');
        } finally {
            setLoading(false);
        }
    };

    // Start the pulsating animation when component mounts
    useEffect(() => {
        const startPulseAnimation = () => {
            // Reset values
            pulseAnim.setValue(1);
            pulseOpacity.setValue(0.7);

            // Create a sequence of animations
            Animated.parallel([
                Animated.sequence([
                    Animated.timing(pulseAnim, {
                        toValue: 1.3,
                        duration: 800,
                        useNativeDriver: true
                    }),
                    Animated.timing(pulseAnim, {
                        toValue: 1,
                        duration: 800,
                        useNativeDriver: true
                    })
                ]),
                Animated.sequence([
                    Animated.timing(pulseOpacity, {
                        toValue: 0.2,
                        duration: 800,
                        useNativeDriver: true
                    }),
                    Animated.timing(pulseOpacity, {
                        toValue: 0.7,
                        duration: 800,
                        useNativeDriver: true
                    })
                ])
            ]).start(() => {
                // Repeat the animation
                startPulseAnimation();
            });
        };

        startPulseAnimation();

        // Clean up animation when component unmounts
        return () => {
            pulseAnim.stopAnimation();
            pulseOpacity.stopAnimation();
        };
    }, []);

    useEffect(() => {
        // Validate location whenever it changes
        if (location) {
            validateLocation(location);
        }
    }, [location]);

    const validateLocation = (loc) => {
        const withinZone = isWithinDeliveryZone(loc);
        setIsWithinZone(withinZone);
        onValidationChange(withinZone);

        // Only show the alert once per session, not every time a location is validated
        if (!withinZone && !hasShownDeliveryZoneWarning.current) {
            hasShownDeliveryZoneWarning.current = true;
            Alert.alert(
                "Outside Delivery Zone",
                "The selected location appears to be outside our delivery zone. You can still save this address, but we may not be able to deliver to this location.",
                [{ text: "OK" }],
                { cancelable: true }
            );
        }
    };

    // Use the location context
    const { requestPermission, getLocation: getContextLocation } = useLocation();

    const getCurrentLocation = async () => {
        try {
            setLoading(true);
            setError(null);

            // Use the location context to get the current location
            const newLocation = await getContextLocation();

            if (!newLocation) {
                setError('Could not get your location');
                setLoading(false);

                // Show a more user-friendly error message
                Alert.alert(
                    "Location Error",
                    "We couldn't get your current location. Please make sure location services are enabled and try again, or manually select a location on the map.",
                    [{ text: "OK" }],
                    { cancelable: true }
                );
                return;
            }

            setLocation(newLocation);

            // Don't automatically call onLocationSelected - wait for the Set Location button

            // Animate map to the location with closer zoom for better visibility
            if (mapRef.current) {
                mapRef.current.animateToRegion({
                    ...newLocation,
                    latitudeDelta: 0.005, // Closer zoom level for better visibility
                    longitudeDelta: 0.005 * ASPECT_RATIO
                }, 1000);
            }

            // Get address from coordinates and show feedback
            const address = await getAddressFromCoordinates(newLocation);
            if (address) {
                const addressText = [
                    address.name,
                    address.street,
                    address.city,
                    address.region
                ].filter(Boolean).join(', ');

                // Show a success message with the address
                Alert.alert(
                    "Location Found",
                    `Your current location: ${addressText}\n\nYou can drag the marker to adjust it if needed.`,
                    [{ text: "OK" }],
                    { cancelable: true }
                );
            }

        } catch (err) {
            console.error('Error getting current location:', err);
            setError('Could not get your location');

            // Show a more user-friendly error message
            Alert.alert(
                "Location Error",
                "We couldn't get your current location. Please make sure location services are enabled and try again, or manually select a location on the map.",
                [{ text: "OK" }],
                { cancelable: true }
            );
        } finally {
            setLoading(false);
        }
    };

    const getAddressFromCoordinates = async (coords) => {
        try {
            const result = await Location.reverseGeocodeAsync({
                latitude: coords.latitude,
                longitude: coords.longitude
            });

            if (result && result.length > 0) {
                const address = result[0];
                // Log the address for debugging
                console.log('Address:', address);
                return address;
            }
            return null;
        } catch (error) {
            console.error('Error getting address:', error);
            return null;
        }
    };

    // Search for locations based on text input
    const searchLocations = async (text) => {
        if (!text || text.trim().length < 3) {
            setSearchResults([]);
            return;
        }

        setIsSearching(true);

        try {
            // Special handling for pincodes (6-digit numbers)
            const isPincode = /^\d{6}$/.test(text.trim());

            // Hardcoded locations for common pincodes to ensure they work
            const hardcodedLocations = {
                '632001': {
                    name: 'Vellore',
                    latitude: 12.9165,
                    longitude: 79.1325,
                    address: {
                        city: 'Vellore',
                        region: 'Tamil Nadu',
                        country: 'India',
                        postalCode: '632001'
                    }
                },
                '600001': {
                    name: 'Chennai',
                    latitude: 13.0827,
                    longitude: 80.2707,
                    address: {
                        city: 'Chennai',
                        region: 'Tamil Nadu',
                        country: 'India',
                        postalCode: '600001'
                    }
                }
            };

            // Check if we have a hardcoded location for this pincode
            if (isPincode && hardcodedLocations[text.trim()]) {
                const location = hardcodedLocations[text.trim()];
                setSearchResults([{
                    latitude: location.latitude,
                    longitude: location.longitude,
                    address: location.address,
                    searchText: text,
                    isPincodeSearch: true,
                    name: location.name
                }]);
                setIsSearching(false);
                return;
            }

            // If it's a pincode, add "India" to improve geocoding accuracy
            const searchText = isPincode ? `${text.trim()}, India` : text;

            // Use Geocoding API to search for locations
            const result = await Location.geocodeAsync(searchText);

            if (result && result.length > 0) {
                // Get more details for each result
                const detailedResults = await Promise.all(
                    result.slice(0, 5).map(async (item) => {
                        const address = await Location.reverseGeocodeAsync({
                            latitude: item.latitude,
                            longitude: item.longitude
                        });

                        return {
                            ...item,
                            address: address[0] || {},
                            // Add original search text for reference
                            searchText: text
                        };
                    })
                );

                setSearchResults(detailedResults);
            } else {
                // If no results, try some common city names
                const commonCities = [
                    "Vellore, Tamil Nadu, India",
                    "Chennai, Tamil Nadu, India",
                    "Delhi, India",
                    "Mumbai, India",
                    "Bangalore, India"
                ];

                // Try to find a city that matches part of the search text
                const matchingCity = commonCities.find(city =>
                    city.toLowerCase().includes(text.toLowerCase())
                );

                if (matchingCity) {
                    const cityResult = await Location.geocodeAsync(matchingCity);

                    if (cityResult && cityResult.length > 0) {
                        const address = await Location.reverseGeocodeAsync({
                            latitude: cityResult[0].latitude,
                            longitude: cityResult[0].longitude
                        });

                        setSearchResults([{
                            ...cityResult[0],
                            address: address[0] || {},
                            searchText: text,
                            name: matchingCity.split(',')[0]
                        }]);
                    } else {
                        setSearchResults([]);
                    }
                } else {
                    setSearchResults([]);
                }
            }
        } catch (error) {
            console.error('Error searching locations:', error);
            setSearchResults([]);

            // Fallback to hardcoded locations if search fails
            if (text.toLowerCase().includes('vellore') || text.trim() === '632001') {
                setSearchResults([{
                    latitude: 12.9165,
                    longitude: 79.1325,
                    address: {
                        city: 'Vellore',
                        region: 'Tamil Nadu',
                        country: 'India',
                        postalCode: '632001'
                    },
                    searchText: text,
                    isPincodeSearch: true,
                    name: 'Vellore'
                }]);
            }
        } finally {
            setIsSearching(false);
        }
    };

    // Handle selecting a search result
    const handleSelectSearchResult = (result) => {
        const newLocation = {
            latitude: result.latitude,
            longitude: result.longitude
        };

        setLocation(newLocation);
        // Don't automatically call onLocationSelected - wait for the Set Location button
        setSearchResults([]);
        setSearchText('');

        // Animate map to the location
        if (mapRef.current) {
            mapRef.current.animateToRegion({
                ...newLocation,
                latitudeDelta: LATITUDE_DELTA,
                longitudeDelta: LONGITUDE_DELTA
            }, 1000);
        }

        // Show a toast or alert to prompt user to confirm location
        Alert.alert(
            "Location Selected",
            "Tap 'Set Location' button at the bottom to confirm this location.",
            [{ text: "OK" }]
        );
    };

    const handleMapPress = (event) => {
        const newLocation = event.nativeEvent.coordinate;
        setLocation(newLocation);
        // Don't automatically call onLocationSelected - wait for the Set Location button
    };

    const handleMarkerDragEnd = (event) => {
        const newLocation = event.nativeEvent.coordinate;
        setLocation(newLocation);
        onLocationSelected(newLocation);

        // Get address for the new location
        getAddressFromCoordinates(newLocation)
            .then(address => {
                if (address) {
                    // Show a toast or alert with the new address
                    const addressText = [
                        address.name,
                        address.street,
                        address.city,
                        address.region
                    ].filter(Boolean).join(', ');

                    Alert.alert(
                        "Location Updated",
                        `New location: ${addressText}`,
                        [{ text: "OK" }],
                        { cancelable: true }
                    );
                }
            })
            .catch(error => {
                console.error('Error getting address after drag:', error);
            });
    };

    return (
        <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
            {showPlacesAutocomplete && (
                <View style={styles.searchContainer}>
                    <View style={styles.searchInputContainer}>
                        <MaterialIcons name="search" size={20} color="#A31621" style={styles.searchIcon} />
                        <TextInput
                            ref={searchInputRef}
                            style={styles.searchInput}
                            placeholder="Search for Vellore, Chennai or pincode 632001"
                            value={searchText}
                            onChangeText={(text) => {
                                setSearchText(text);
                                if (text.length >= 3) {
                                    searchLocations(text);
                                } else {
                                    setSearchResults([]);
                                }
                            }}
                            returnKeyType="search"
                            onSubmitEditing={() => searchLocations(searchText)}
                            autoCapitalize="none"
                            keyboardType="default"
                        />
                        {searchText.length > 0 && (
                            <TouchableOpacity
                                style={styles.clearButton}
                                onPress={() => {
                                    setSearchText('');
                                    setSearchResults([]);
                                }}
                            >
                                <MaterialIcons name="clear" size={20} color="#A31621" />
                            </TouchableOpacity>
                        )}
                    </View>
                    <Text style={styles.searchHint}>
                        Try searching for "Vellore", "632001", or "Chennai"
                    </Text>

                    {/* Search Results */}
                    {searchResults.length > 0 && (
                        <View style={styles.searchResultsContainer}>
                            {isSearching ? (
                                <ActivityIndicator style={styles.searchingIndicator} size="small" color="#A31621" />
                            ) : (
                                searchResults.map((result, index) => {
                                    const address = result.address;

                                    // Create a display name with available address components
                                    let displayComponents = [
                                        address.name,
                                        address.street,
                                        address.city,
                                        address.region
                                    ].filter(Boolean);

                                    // If this was a pincode search, make sure to show it prominently
                                    if (result.isPincodeSearch || (result.searchText && /^\d{6}$/.test(result.searchText.trim()))) {
                                        // Add pincode to the display name if it's not already there
                                        if (address.postalCode !== result.searchText) {
                                            displayComponents.unshift(result.searchText);
                                        }
                                    }

                                    const displayName = displayComponents.join(', ');

                                    // Determine if this location is in a delivery zone
                                    const inDeliveryZone = isWithinDeliveryZone({
                                        latitude: result.latitude,
                                        longitude: result.longitude
                                    });

                                    return (
                                        <TouchableOpacity
                                            key={`result-${index}`}
                                            style={[
                                                styles.searchResultItem,
                                                inDeliveryZone ? styles.searchResultItemValid : styles.searchResultItemInvalid
                                            ]}
                                            onPress={() => handleSelectSearchResult(result)}
                                        >
                                            <MaterialIcons
                                                name="location-on"
                                                size={20}
                                                color={inDeliveryZone ? "#A31621" : "#888"}
                                            />
                                            <View style={styles.searchResultTextContainer}>
                                                <Text style={styles.searchResultText} numberOfLines={1}>
                                                    {displayName || 'Unknown location'}
                                                </Text>
                                                <View style={styles.searchResultDetails}>
                                                    {address.postalCode && (
                                                        <Text style={styles.searchResultPincode}>
                                                            {address.postalCode}
                                                        </Text>
                                                    )}
                                                    <Text style={styles.searchResultSubtext} numberOfLines={1}>
                                                        {address.country || ''}
                                                        {inDeliveryZone
                                                            ? ' • Delivery Available'
                                                            : ' • Outside Delivery Zone'}
                                                    </Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    );
                                })
                            )}
                        </View>
                    )}
                </View>
            )}

            <View style={styles.mapContainer}>
                <MapView
                    ref={mapRef}
                    style={styles.map}
                    initialRegion={location ? {
                        ...location,
                        latitudeDelta: LATITUDE_DELTA,
                        longitudeDelta: LONGITUDE_DELTA
                    } : {
                        // Default to Vellore location
                        latitude: 12.8997028,
                        longitude: 79.136073,
                        latitudeDelta: 0.01,
                        longitudeDelta: 0.01
                    }}
                    onPress={handleMapPress}
                    showsUserLocation={true}
                    showsMyLocationButton={false}
                    showsCompass={false}
                    showsScale={false}
                    rotateEnabled={true}
                    loadingEnabled={false}
                    minZoomLevel={5}
                    maxZoomLevel={19}
                    moveOnMarkerPress={false}
                >
                    {location && (
                        <Marker
                            ref={markerRef}
                            coordinate={location}
                            draggable={false}
                            tracksViewChanges={false}
                        >
                            <View style={[
                                styles.markerContainer,
                                !isWithinZone && styles.markerOutsideZone
                            ]}>
                                {/* Shadow for better visibility */}
                                <View style={styles.markerShadow}>
                                    <MaterialIcons
                                        name="person-pin"
                                        size={48}
                                        color="#000"
                                    />
                                </View>

                                {/* Main marker icon - person shape */}
                                <View style={styles.markerIconContainer}>
                                    <MaterialIcons
                                        name="person-pin"
                                        size={48}
                                        color="#A31621"
                                    />
                                </View>

                                {/* Pulsating circle for emphasis */}
                                <Animated.View
                                    style={[
                                        styles.markerPulse,
                                        {
                                            transform: [{ scale: pulseAnim }],
                                            opacity: pulseOpacity
                                        }
                                    ]}
                                />
                            </View>
                        </Marker>
                    )}
                </MapView>

                {loading && (
                    <View style={styles.loadingOverlay}>
                        <ActivityIndicator size="large" color="#A31621" />
                    </View>
                )}

                {error && (
                    <View style={styles.errorContainer}>
                        <Text style={styles.errorText}>{error}</Text>
                    </View>
                )}

                <TouchableOpacity
                    style={styles.currentLocationButton}
                    onPress={getCurrentLocation}
                >
                    <MaterialIcons name="my-location" size={24} color="#A31621" />
                </TouchableOpacity>

                {/* Set Location Button */}
                <TouchableOpacity
                    style={styles.setLocationButton}
                    onPress={() => {
                        if (location) {
                            Alert.alert(
                                "Confirm Location",
                                "Do you want to set this as your location?",
                                [
                                    {
                                        text: "Cancel",
                                        style: "cancel"
                                    },
                                    {
                                        text: "Set Location",
                                        onPress: () => {
                                            onLocationSelected(location);
                                            Alert.alert(
                                                "Location Set",
                                                "Your location has been set successfully.",
                                                [{ text: "OK" }]
                                            );
                                        }
                                    }
                                ]
                            );
                        } else {
                            Alert.alert(
                                "No Location Selected",
                                "Please select a location first.",
                                [{ text: "OK" }]
                            );
                        }
                    }}
                >
                    <MaterialIcons name="check-circle" size={20} color="white" />
                    <Text style={styles.setLocationButtonText}>Set Location</Text>
                </TouchableOpacity>

                {!isWithinZone && (
                    <View style={styles.warningBanner}>
                        <MaterialIcons name="warning" size={20} color="#fff" />
                        <View style={styles.warningTextContainer}>
                            <Text style={styles.warningText}>Outside delivery zone</Text>
                            <Text style={styles.warningSubtext}>You can still select this location</Text>
                        </View>
                    </View>
                )}

                <View style={styles.mapAttributionContainer}>
                    {/* Empty view to replace "Powered by Google" */}
                </View>
            </View>
        </KeyboardAvoidingView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    },
    searchContainer: {
        padding: 10,
        zIndex: 10,
        elevation: 10
    },
    searchInputContainer: {
        backgroundColor: 'white',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#ddd',
        marginBottom: 5,
        marginRight: 2, // Add margin for shadow visibility
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        shadowColor: '#A31621', // Use madder color for shadow
        shadowOffset: { width: 3, height: 2 }, // Shadow only on right and bottom
        shadowOpacity: 0.15, // Subtle shadow
        shadowRadius: 4, // Soft shadow edge
        elevation: 4 // For Android
    },
    searchIcon: {
        marginRight: 8
    },
    searchInput: {
        flex: 1,
        height: 45,
        fontSize: 16,
        color: '#5d5d5d'
    },
    clearButton: {
        padding: 5
    },
    searchHint: {
        fontSize: 12,
        color: '#666',
        marginBottom: 8,
        textAlign: 'center',
        fontStyle: 'italic'
    },
    searchResultsContainer: {
        backgroundColor: 'white',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#ddd',
        marginBottom: 10,
        maxHeight: 200,
        zIndex: 20,
        elevation: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.2,
        shadowRadius: 4
    },
    searchingIndicator: {
        padding: 15
    },
    searchResultItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0'
    },
    searchResultItemValid: {
        backgroundColor: 'rgba(163, 22, 33, 0.05)'
    },
    searchResultItemInvalid: {
        backgroundColor: 'rgba(0, 0, 0, 0.03)'
    },
    searchResultTextContainer: {
        flex: 1,
        marginLeft: 10
    },
    searchResultText: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500'
    },
    searchResultDetails: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 2
    },
    searchResultPincode: {
        fontSize: 12,
        color: '#A31621',
        fontWeight: 'bold',
        marginRight: 5,
        backgroundColor: 'rgba(163, 22, 33, 0.1)',
        paddingHorizontal: 4,
        paddingVertical: 1,
        borderRadius: 3
    },
    searchResultSubtext: {
        fontSize: 12,
        color: '#666'
    },
    mapContainer: {
        flex: 1,
        position: 'relative'
    },
    map: {
        ...StyleSheet.absoluteFillObject
    },
    markerContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        width: 60,
        height: 60
    },
    markerOutsideZone: {
        opacity: 0.9
    },
    markerShadow: {
        position: 'absolute',
        top: 0,
        left: 6,
        opacity: 0.3,
        transform: [{scale: 1.05}]
    },
    markerIconContainer: {
        position: 'absolute',
        top: 0,
        left: 6
    },
    markerPulse: {
        position: 'absolute',
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: 'rgba(163, 22, 33, 0.5)',
        borderWidth: 2,
        borderColor: 'rgba(163, 22, 33, 0.2)',
        top: 8,
        left: 15,
        zIndex: -1
    },
    markerDragHint: {
        backgroundColor: 'rgba(0,0,0,0.7)',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        marginTop: 40,
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.3)'
    },
    markerDragHintText: {
        color: 'white',
        fontSize: 10,
        fontWeight: 'bold'
    },
    loadingOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(255,255,255,0.7)',
        alignItems: 'center',
        justifyContent: 'center'
    },
    errorContainer: {
        position: 'absolute',
        top: '50%',
        left: 20,
        right: 20,
        backgroundColor: 'rgba(255,0,0,0.7)',
        padding: 10,
        borderRadius: 8,
        alignItems: 'center'
    },
    errorText: {
        color: 'white',
        textAlign: 'center'
    },
    currentLocationButton: {
        position: 'absolute',
        bottom: 80,
        right: 20,
        backgroundColor: 'white',
        width: 50,
        height: 50,
        borderRadius: 25,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5
    },
    setLocationButton: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        backgroundColor: '#A31621',
        height: 50,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5
    },
    setLocationButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
        marginLeft: 10
    },
    warningBanner: {
        position: 'absolute',
        top: 10,
        left: 10,
        right: 10,
        backgroundColor: '#ff6b6b',
        padding: 8,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        elevation: 3
    },
    warningTextContainer: {
        flex: 1,
        marginLeft: 5
    },
    warningText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14
    },
    warningSubtext: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12
    },
    mapAttributionContainer: {
        position: 'absolute',
        bottom: 0,
        right: 0,
        backgroundColor: 'transparent',
        width: 100,
        height: 20
    }
});

export default LocationPicker;
