import React, { createContext, useState, useContext, useEffect } from 'react';
import { getDeliveryPartnerProfile, updateAvailability, getDeliveryPartnerOrders } from '../utils/api/deliveryApi';
import { getUserData, getAuthToken } from '../utils/authStorage';
import { USER_TYPES } from '../config/constants';
import { refreshAccessToken } from '../utils/tokenRefresh';
import axios from 'axios';
import { API_URL } from '../config/constants';

// Create the context
const DeliveryPartnerContext = createContext();

// Create a provider component
export const DeliveryPartnerProvider = ({ children }) => {
    // Initialize with empty array, will be populated from API
    const [deliveryPartners, setDeliveryPartners] = useState([]);
    // Current logged in delivery partner
    const [currentDeliveryPartner, setCurrentDeliveryPartner] = useState(null);
    // Loading state
    const [loading, setLoading] = useState(true);
    // Error state
    const [error, setError] = useState(null);
    // Track when user data changes
    const [userDataVersion, setUserDataVersion] = useState(0);

    // Function to refresh user data - can be called after login
    const refreshUserData = () => {
        console.log('DeliveryPartnerContext: Triggering refresh of user data');
        setUserDataVersion(prev => prev + 1);
    };

    // Fetch delivery partners from API on component mount or when user data changes
    useEffect(() => {
        const fetchDeliveryPartners = async () => {
            try {
                setLoading(true);
                setError(null); // Clear previous errors

                // Get user data to check user type
                const userData = await getUserData();
                const userType = userData?.userType?.toUpperCase();

                console.log('DeliveryPartnerContext: User type:', userType);
                console.log('DeliveryPartnerContext: User data:', userData);

                // Verify token is valid
                const token = await getAuthToken();
                if (!token) {
                    console.log('DeliveryPartnerContext: No auth token available');
                    setLoading(false);
                    return;
                }

                // If user is a delivery partner, fetch only their profile
                if (userType === USER_TYPES.DELIVERY_PARTNER) {
                    console.log('DeliveryPartnerContext: User is a delivery partner, fetching profile');
                    try {
                        // Try to fetch profile
                        let profile;
                        try {
                            profile = await getDeliveryPartnerProfile();
                        } catch (error) {
                            // If 401 error, try to refresh token and retry
                            if (error.response && error.response.status === 401) {
                                console.log('DeliveryPartnerContext: Token expired, refreshing...');
                                try {
                                    await refreshAccessToken();
                                    // Retry with new token
                                    profile = await getDeliveryPartnerProfile();
                                } catch (refreshError) {
                                    console.error('DeliveryPartnerContext: Token refresh failed:', refreshError);
                                    throw refreshError;
                                }
                            } else {
                                throw error;
                            }
                        }

                        console.log('DeliveryPartnerContext: Delivery partner profile fetched:', profile);

                        if (profile) {
                            // Set the current delivery partner
                            setCurrentDeliveryPartner(profile);
                            // Also add to the partners array for consistency
                            setDeliveryPartners([profile]);
                        } else {
                            console.log('DeliveryPartnerContext: No profile returned from API');
                            // Use userData as fallback if profile fetch returns empty
                            if (userData) {
                                console.log('DeliveryPartnerContext: Using userData as fallback for profile');
                                setCurrentDeliveryPartner(userData);
                                setDeliveryPartners([userData]);
                            } else {
                                setCurrentDeliveryPartner(null);
                                setDeliveryPartners([]);
                            }
                        }
                    } catch (profileError) {
                        console.error('DeliveryPartnerContext: Error fetching delivery partner profile:', profileError);
                        // Use userData as fallback if profile fetch fails
                        if (userData) {
                            console.log('DeliveryPartnerContext: Using userData as fallback for profile');
                            setCurrentDeliveryPartner(userData);
                            setDeliveryPartners([userData]);
                        } else {
                            setCurrentDeliveryPartner(null);
                            setDeliveryPartners([]);
                        }
                    }
                }
                // We don't need to handle admin case in delivery partner context
                // For regular users, don't fetch any delivery partner data
                else {
                    console.log('DeliveryPartnerContext: User is not admin or delivery partner, skipping fetch');
                    setDeliveryPartners([]);
                    setCurrentDeliveryPartner(null);
                }
            } catch (error) {
                console.error('DeliveryPartnerContext: Error in delivery partner fetch process:', error);
                setError(error.message || 'An unexpected error occurred');
                // Fallback to empty array if process fails
                setDeliveryPartners([]);
                setCurrentDeliveryPartner(null);
            } finally {
                setLoading(false);
            }
        };

        fetchDeliveryPartners();
    }, [userDataVersion]); // Re-run when userDataVersion changes

    // Function to toggle availability
    const toggleAvailability = async (partnerId) => {
        try {
            if (!partnerId) {
                console.error('DeliveryPartnerContext: No partner ID provided for toggle availability');
                throw new Error('No partner ID provided');
            }

            // Get user data to check user type
            const userData = await getUserData();
            const userType = userData?.userType?.toUpperCase();

            // Always use the string ID directly - the backend will handle it
            console.log(`DeliveryPartnerContext: Toggling availability for partner ${partnerId}`);

            // Get current availability status to toggle it
            const isCurrentlyAvailable = currentDeliveryPartner?.isAvailable !== false;
            console.log(`DeliveryPartnerContext: Current availability status: ${isCurrentlyAvailable}, toggling to: ${!isCurrentlyAvailable}`);

            // Use the updateAvailability function imported at the top of the file

            // Call the API to update availability
            const response = await updateAvailability(!isCurrentlyAvailable);
            console.log('DeliveryPartnerContext: API response:', response);

            // For delivery partners, always update the currentDeliveryPartner state
            // This ensures the UI stays in sync with the backend
            if (userType !== USER_TYPES.ADMIN && currentDeliveryPartner) {
                console.log('DeliveryPartnerContext: Updating currentDeliveryPartner for delivery partner');

                // Always update the current delivery partner with the new availability status
                setCurrentDeliveryPartner(prev => ({
                    ...prev,
                    isAvailable: !isCurrentlyAvailable
                }));

                // Increment the user data version to trigger a refresh
                setUserDataVersion(prev => prev + 1);
            }

            // Process the response based on its format
            if (response && response.deliveryPartner) {
                console.log('DeliveryPartnerContext: Updating with API response data');

                // Update in the list with the response from the API
                setDeliveryPartners(prevPartners =>
                    prevPartners.map(partner =>
                        partner.id === partnerId || partner._id === partnerId
                            ? response.deliveryPartner
                            : partner
                    )
                );

                // If it's the current delivery partner and we're in admin mode, update that too
                if (userType === USER_TYPES.ADMIN && currentDeliveryPartner && (
                    currentDeliveryPartner.id === partnerId ||
                    currentDeliveryPartner._id === partnerId
                )) {
                    console.log('DeliveryPartnerContext: Updating currentDeliveryPartner with API response (admin mode)');
                    setCurrentDeliveryPartner(response.deliveryPartner);
                }

                return response.deliveryPartner;
            } else if (response && typeof response.isAvailable === 'boolean') {
                // Handle case where API returns just the updated partner directly
                console.log('DeliveryPartnerContext: Updating with direct API response');

                // Update in the list
                setDeliveryPartners(prevPartners =>
                    prevPartners.map(partner =>
                        partner.id === partnerId || partner._id === partnerId
                            ? { ...partner, ...response }
                            : partner
                    )
                );

                // If it's the current delivery partner and we're in admin mode, update that too
                if (userType === USER_TYPES.ADMIN && currentDeliveryPartner && (
                    currentDeliveryPartner.id === partnerId ||
                    currentDeliveryPartner._id === partnerId
                )) {
                    console.log('DeliveryPartnerContext: Updating currentDeliveryPartner with direct API response (admin mode)');
                    setCurrentDeliveryPartner(prev => ({
                        ...prev,
                        ...response
                    }));
                }

                return response;
            } else {
                // Fallback to local update if API doesn't return the updated partner
                console.log('DeliveryPartnerContext: Falling back to local state update');

                // Update in the list
                setDeliveryPartners(prevPartners =>
                    prevPartners.map(partner =>
                        partner.id === partnerId || partner._id === partnerId
                            ? { ...partner, isAvailable: !partner.isAvailable }
                            : partner
                    )
                );

                // If it's the current delivery partner and we're in admin mode, update that too
                if (userType === USER_TYPES.ADMIN && currentDeliveryPartner && (
                    currentDeliveryPartner.id === partnerId ||
                    currentDeliveryPartner._id === partnerId
                )) {
                    console.log('DeliveryPartnerContext: Updating currentDeliveryPartner with local state (admin mode)');
                    setCurrentDeliveryPartner(prev => ({
                        ...prev,
                        isAvailable: !prev.isAvailable
                    }));
                }

                // Return the updated partner from local state
                const updatedPartner = deliveryPartners.find(
                    partner => partner.id === partnerId || partner._id === partnerId
                );

                if (updatedPartner) {
                    return {
                        ...updatedPartner,
                        isAvailable: !updatedPartner.isAvailable
                    };
                }

                // If we can't find the partner in the list, return a basic object with the new availability
                return { isAvailable: !isCurrentlyAvailable };
            }
        } catch (error) {
            console.error(`DeliveryPartnerContext: Error toggling availability for partner ${partnerId}:`, error);

            // Log detailed error information
            if (error.response) {
                console.error('DeliveryPartnerContext: Error response:', {
                    status: error.response.status,
                    data: error.response.data
                });
            }

            // For delivery partners, don't update local state on error to avoid inconsistency
            // For admins, we can still update local state as a fallback
            if (userType === USER_TYPES.ADMIN && currentDeliveryPartner && (
                currentDeliveryPartner.id === partnerId ||
                currentDeliveryPartner._id === partnerId
            )) {
                console.log('DeliveryPartnerContext: API failed, updating local state as fallback (admin mode)');
                setCurrentDeliveryPartner(prev => ({
                    ...prev,
                    isAvailable: !prev.isAvailable
                }));
            }

            throw error;
        }
    };

    // This function is not needed for delivery partner side
    const updateDeliveryPartnerProfile = async (partnerId, updatedData) => {
        console.error('DeliveryPartnerContext: updateDeliveryPartnerProfile is not available for delivery partners');
        throw new Error('This function is only available for admin users');
    };

    // These functions are not needed for delivery partner side
    const getPartnerById = async (partnerId) => {
        console.error('DeliveryPartnerContext: getPartnerById is not available for delivery partners');
        throw new Error('This function is only available for admin users');
    };

    const getPartnerByPhone = (phone) => {
        console.error('DeliveryPartnerContext: getPartnerByPhone is not available for delivery partners');
        throw new Error('This function is only available for admin users');
    };

    // Function to fetch orders for a delivery partner
    const fetchDeliveryPartnerOrders = async () => {
        try {
            if (!currentDeliveryPartner) {
                console.log('DeliveryPartnerContext: No delivery partner available for order fetch');

                // Try to get user data as fallback
                try {
                    const userData = await getUserData();
                    if (userData && (userData.userType === 'DELIVERY_PARTNER' || userData.userType === 'delivery_partner')) {
                        console.log('DeliveryPartnerContext: Using user data as fallback for delivery partner');
                        console.log('DeliveryPartnerContext: User data:', JSON.stringify(userData, null, 2));

                        // Set current delivery partner from user data
                        setCurrentDeliveryPartner(userData);
                    } else {
                        console.log('DeliveryPartnerContext: No delivery partner data available');
                        console.log('DeliveryPartnerContext: User data:', JSON.stringify(userData, null, 2));
                        return [];
                    }
                } catch (userDataError) {
                    console.error('DeliveryPartnerContext: Error getting user data:', userDataError);
                    return [];
                }
            }

            // Get the delivery partner ID (could be in _id or id field)
            const deliveryPartnerId = currentDeliveryPartner?._id || currentDeliveryPartner?.id;
            console.log('DeliveryPartnerContext: Fetching orders for delivery partner:', deliveryPartnerId);

            // Use the getDeliveryPartnerOrders function imported at the top of the file

            try {
                // Get orders for the delivery partner
                console.log('DeliveryPartnerContext: Calling getDeliveryPartnerOrders API function');
                const response = await getDeliveryPartnerOrders();
                console.log('DeliveryPartnerContext: API response received:', JSON.stringify(response, null, 2));

                // Validate response
                if (!response) {
                    console.log('DeliveryPartnerContext: No orders returned from API');
                    return [];
                }

                // Handle different response formats
                let orders;
                if (Array.isArray(response)) {
                    console.log('DeliveryPartnerContext: Response is an array with', response.length, 'orders');
                    orders = response;
                } else if (response.orders && Array.isArray(response.orders)) {
                    console.log('DeliveryPartnerContext: Found orders array in response.orders with', response.orders.length, 'orders');
                    orders = response.orders;
                } else if (response.data && Array.isArray(response.data.orders)) {
                    console.log('DeliveryPartnerContext: Found orders array in response.data.orders with', response.data.orders.length, 'orders');
                    orders = response.data.orders;
                } else if (typeof response === 'object' && response._id) {
                    // Single order object
                    console.log('DeliveryPartnerContext: Response is a single order object');
                    orders = [response];
                } else {
                    console.log('DeliveryPartnerContext: Invalid response format:', typeof response);
                    console.log('DeliveryPartnerContext: Response keys:', Object.keys(response));
                    return [];
                }

                console.log('DeliveryPartnerContext: Total orders available:', orders.length);

                // Filter orders for the current delivery partner
                // Check both string and ObjectId representations
                const filteredOrders = orders.filter(order => {
                    try {
                        // Check if the order has a deliveryPartner field
                        if (!order.deliveryPartner) {
                            // Also check for other fields that might contain the delivery partner ID
                            const alternativeFields = [
                                'deliveryPartnerId', 'deliveryPartnerID',
                                'delivery_partner_id', 'assignedTo', 'assignedDeliveryPartner'
                            ];

                            for (const field of alternativeFields) {
                                if (order[field]) {
                                    const fieldValue = order[field];
                                    const fieldValueStr = String(typeof fieldValue === 'object' ?
                                        (fieldValue._id || fieldValue.id) : fieldValue);
                                    const currentIdStr = String(deliveryPartnerId);

                                    if (fieldValueStr === currentIdStr) {
                                        return true;
                                    }
                                }
                            }

                            return false;
                        }

                        // Handle different formats of deliveryPartner field
                        const orderDeliveryPartnerId =
                            typeof order.deliveryPartner === 'object'
                                ? (order.deliveryPartner._id || order.deliveryPartner.id)
                                : order.deliveryPartner;

                        // Convert both to strings for comparison
                        const orderIdStr = String(orderDeliveryPartnerId);
                        const currentIdStr = String(deliveryPartnerId);

                        // Log for debugging
                        console.log(`Comparing order.deliveryPartner (${orderIdStr}) with currentDeliveryPartner (${currentIdStr})`);

                        // Direct string comparison
                        if (orderIdStr === currentIdStr) {
                            return true;
                        }

                        // Special handling for MongoDB ObjectId format
                        // Check if both are MongoDB ObjectIds (24 hex characters)
                        const isOrderIdObjectId = /^[0-9a-f]{24}$/i.test(orderIdStr);
                        const isCurrentIdObjectId = /^[0-9a-f]{24}$/i.test(currentIdStr);

                        if (isOrderIdObjectId && isCurrentIdObjectId) {
                            // For MongoDB ObjectIds, we need to compare them as strings
                            return orderIdStr === currentIdStr;
                        }

                        // Special handling for custom ID format (e.g., DP002)
                        if (typeof currentIdStr === 'string' && currentIdStr.startsWith('DP')) {
                            // Check if the order's deliveryPartner is an object with an id property
                            if (typeof order.deliveryPartner === 'object' &&
                                order.deliveryPartner.id &&
                                String(order.deliveryPartner.id) === currentIdStr) {
                                return true;
                            }

                            // Check other possible fields
                            const fieldsToCheck = [
                                'deliveryPartnerId', 'deliveryPartnerID',
                                'delivery_partner_id', 'assignedTo', 'assignedDeliveryPartner'
                            ];

                            for (const field of fieldsToCheck) {
                                if (order[field] && String(order[field]) === currentIdStr) {
                                    return true;
                                }
                            }
                        }

                        // Check for timestamps in the order that match the delivery partner
                        // This is a fallback for when the IDs don't match but we have timestamps
                        if (order.deliveryPartnerAssignedAt &&
                            order.deliveryStartedAt &&
                            order.deliveredAt) {
                            // If the order has all the delivery timestamps, it's likely assigned to this partner
                            console.log('Order has delivery timestamps, checking if it belongs to this partner');

                            // Check if the order has the user ID in any form
                            if (order.userId && currentDeliveryPartner &&
                                (order.userId === currentDeliveryPartner._id ||
                                 order.userId === currentDeliveryPartner.id)) {
                                return true;
                            }
                        }

                        return false;
                    } catch (error) {
                        console.error('Error filtering order:', error);
                        return false;
                    }
                });

                console.log(`DeliveryPartnerContext: Found ${filteredOrders.length} orders for delivery partner ${deliveryPartnerId}`);

                // Always check for unassigned orders that can be picked up if the delivery partner is available
                if (currentDeliveryPartner?.isAvailable) {
                    console.log('DeliveryPartnerContext: Checking for unassigned orders');

                    // Filter for unassigned orders (no deliveryPartner) with status PLACED or CONFIRMED
                    const unassignedOrders = orders.filter(order =>
                        !order.deliveryPartner &&
                        (order.status === 'PLACED' || order.status === 'CONFIRMED')
                    );

                    console.log(`DeliveryPartnerContext: Found ${unassignedOrders.length} unassigned orders that can be picked up`);

                    // Return both assigned and unassigned orders
                    return [...filteredOrders, ...unassignedOrders];
                }

                // Log the first order for debugging if available
                if (filteredOrders.length > 0) {
                    console.log('DeliveryPartnerContext: First order sample:', JSON.stringify(filteredOrders[0], null, 2));
                }

                return filteredOrders;
            } catch (apiError) {
                console.error('DeliveryPartnerContext: API error fetching orders:', apiError);
                console.error('DeliveryPartnerContext: Error details:', apiError.response?.status, apiError.response?.data);

                // Try the general orders endpoint directly as a last resort
                try {
                    console.log('DeliveryPartnerContext: Trying direct fallback to general orders endpoint');
                    const token = await getAuthToken();

                    const fallbackResponse = await axios.get(`${API_URL}/orders`, {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Accept': 'application/json'
                        },
                        timeout: 10000
                    });

                    console.log('DeliveryPartnerContext: Fallback response received:',
                        Array.isArray(fallbackResponse.data)
                            ? `${fallbackResponse.data.length} orders`
                            : typeof fallbackResponse.data);

                    if (Array.isArray(fallbackResponse.data) && fallbackResponse.data.length > 0) {
                        // Filter orders for the current delivery partner
                        const filteredFallbackOrders = fallbackResponse.data.filter(order => {
                            if (!order.deliveryPartner) return false;

                            const orderDeliveryPartnerId =
                                typeof order.deliveryPartner === 'object'
                                    ? (order.deliveryPartner._id || order.deliveryPartner.id)
                                    : order.deliveryPartner;

                            return String(orderDeliveryPartnerId) === String(deliveryPartnerId);
                        });

                        console.log(`DeliveryPartnerContext: Found ${filteredFallbackOrders.length} orders from fallback for delivery partner ${deliveryPartnerId}`);
                        return filteredFallbackOrders;
                    }
                } catch (fallbackError) {
                    console.error('DeliveryPartnerContext: Fallback attempt failed:', fallbackError);
                }

                return [];
            }
        } catch (error) {
            console.error('DeliveryPartnerContext: Error in order fetch process:', error);
            return [];
        }
    };

    // Value to be provided to consumers - only include what's needed for delivery partner side
    const value = {
        currentDeliveryPartner,
        loading,
        error, // Expose error state
        setError, // Expose setError for manual error handling if needed
        toggleAvailability,
        setCurrentDeliveryPartner,
        refreshUserData, // Add function to refresh user data after login
        fetchDeliveryPartnerOrders // Add function to fetch orders for a delivery partner
    };

    return (
        <DeliveryPartnerContext.Provider value={value}>
            {children}
        </DeliveryPartnerContext.Provider>
    );
};

// Custom hook to use the delivery partner context
export const useDeliveryPartner = () => {
    const context = useContext(DeliveryPartnerContext);
    if (context === undefined) {
        throw new Error('useDeliveryPartner must be used within a DeliveryPartnerProvider');
    }
    return context;
};
