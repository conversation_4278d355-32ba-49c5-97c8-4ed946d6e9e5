import React, { createContext, useContext, useState } from 'react';
import { Alert } from 'react-native';
import Toast from 'react-native-toast-message';

const CartContext = createContext();

export const useCart = () => {
    return useContext(CartContext);
};

export const CartProvider = ({ children }) => {
    const [cartItems, setCartItems] = useState([]);

    // Add to cart modal state
    const [addToCartModal, setAddToCartModal] = useState({
        visible: false,
        productName: '',
        productWeight: '',
        isUpdate: false,
        navigation: null
    });

    const updateCartItemQuantity = (itemId, newQuantity) => {
        if (newQuantity <= 0) {
            removeFromCart(itemId);
            return;
        }

        setCartItems(prevItems =>
            prevItems.map(item => {
                if (item.id === itemId) {
                    // Calculate the per-unit price based on the original totalPrice and quantity
                    const perUnitPrice = item.discount_price || (item.totalPrice / item.quantity);
                    const perUnitOriginalPrice = item.price || (item.originalPrice / item.quantity);

                    return {
                        ...item,
                        quantity: newQuantity,
                        totalPrice: perUnitPrice * newQuantity,
                        originalPrice: perUnitOriginalPrice * newQuantity,
                        savings: item.discount_price ?
                            (perUnitOriginalPrice - perUnitPrice) * newQuantity : 0
                    };
                }
                return item;
            })
        );
    };

    const addToCart = (item, navigation) => {
        // Check if item already exists in cart (same product with same weight)
        const existingItemIndex = cartItems.findIndex(cartItem =>
            cartItem.id.split('-')[0] === item.id.split('-')[0] &&
            cartItem.selectedWeight === item.selectedWeight
        );

        if (existingItemIndex !== -1) {
            // Item exists, update quantity by adding the new quantity
            const existingItem = cartItems[existingItemIndex];
            const newQuantity = existingItem.quantity + item.quantity;

            // Use updateCartItemQuantity for consistency
            updateCartItemQuantity(existingItem.id, newQuantity);

            // Show modal for cart update
            setAddToCartModal({
                visible: true,
                productName: item.name,
                productWeight: item.selectedWeight || '500g',
                isUpdate: true,
                navigation: navigation
            });
        } else {
            // New item, add to cart with the specified quantity and prices
            const newItem = {
                ...item,
                quantity: item.quantity || 1,
                selectedWeight: item.selectedWeight || '500g',
                // Use the totalPrice if provided, otherwise calculate it
                totalPrice: item.totalPrice || (item.discount_price || item.price) * (item.quantity || 1),
                // Use the savings if provided, otherwise calculate it
                savings: item.savings || (item.discount_price ?
                    (item.price - item.discount_price) * (item.quantity || 1) : 0)
            };

            setCartItems(prevItems => [...prevItems, newItem]);

            // Show modal for new item added
            setAddToCartModal({
                visible: true,
                productName: item.name,
                productWeight: newItem.selectedWeight,
                isUpdate: false,
                navigation: navigation
            });
        }
    };

    const calculateItemTotal = (item) => {
        const basePrice = item.discount_price || item.price;
        return basePrice * item.quantity;
    };



    const showCartNotification = (navigation) => {
        Toast.show({
            type: 'info',
            text1: 'View Cart',
            text2: 'Click to view your cart items',
            position: 'bottom',
            bottomOffset: 80, // Add offset to position above bottom navigation
            visibilityTime: 1000,
            onPress: () => {
                navigation.navigate('MainTabs', { screen: 'Cart' });
            },
        });
    };

    const removeFromCart = (itemId) => {
        setCartItems(prevItems => prevItems.filter(item => item.id !== itemId));
        Toast.show({
            type: 'info',
            text1: 'Removed from Cart',
            position: 'bottom',
            bottomOffset: 140, // Position above the add to cart modal
            visibilityTime: 2000,
            props: {
                style: {
                    backgroundColor: '#000',
                    borderRadius: 8,
                },
                text1Style: {
                    color: 'white',
                    fontSize: 14,
                    fontWeight: '600'
                }
            }
        });
    };

    const clearCart = () => {
        setCartItems([]);
    };

    // Modal control functions - modal stays open until user navigates
    const closeAddToCartModal = () => {
        // Only close when user explicitly navigates to cart
        setAddToCartModal(prev => ({ ...prev, visible: false }));
    };

    const proceedToCart = () => {
        if (addToCartModal.navigation) {
            addToCartModal.navigation.navigate('MainTabs', { screen: 'Cart' });
        }
    };

    return (
        <CartContext.Provider value={{
            cartItems,
            addToCart,
            removeFromCart,
            clearCart,
            updateCartItemQuantity,
            addToCartModal,
            closeAddToCartModal,
            proceedToCart
        }}>
            {children}
        </CartContext.Provider>
    );
};