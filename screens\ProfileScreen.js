import React, { useState, useRef } from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView, StatusBar, Animated } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';

// Profile Header Component
const ProfileHeader = ({ userInfo, handleEditProfile, navigation }) => {
    return (
        <View>
            <View className="bg-madder pt-8 pb-2 px-5 flex-row justify-between items-center rounded-b-3xl">
                <Text className="text-xl font-bold text-white">Profile</Text>
                <TouchableOpacity
                    onPress={() => navigation.navigate('Settings')}
                    className="w-10 h-10 rounded-full bg-white items-center justify-center"
                >
                    <MaterialIcons name="settings" size={22} color="#333" />
                </TouchableOpacity>
            </View>

            <View className="bg-madder rounded-b-2xl shadow-sm p-4">
                <View className="flex-row items-center">
                    <View className="w-16 h-16 rounded-full bg-white items-center justify-center mr-4">
                        <Text className="text-madder text-xl font-extrabold">
                            {userInfo.name.charAt(0).toUpperCase()}
                        </Text>
                    </View>
                    <View className="flex-1">
                        <Text className="text-xl font-extrabold">{userInfo.name}</Text>
                        <Text className="text-white text-sm font-medium mt-0.5">{userInfo.phone}</Text>
                    </View>
                    <TouchableOpacity
                        className="bg-madder/10 px-4 py-2 rounded-lg"
                        onPress={handleEditProfile}
                    >
                        <Text className="text-madder font-bold text-sm">Edit</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

// Collapsed Header Component
const CollapsedHeader = ({ userInfo, handleEditProfile, navigation, opacity }) => {
    return (
        <Animated.View
            className="absolute top-0 left-0 right-0 z-20 bg-white shadow-sm rounded-b-3xl"
            style={{
                opacity,
                height: 100,
                paddingTop: 10, // Reduced from 35 to center content
                paddingHorizontal: 16,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
            }}
        >
            <View className="flex-row items-center">
                <View className="w-11 h-11 rounded-full bg-madder/10 items-center justify-center mr-3">
                    <Text className="text-madder font-bold text-base">
                        {userInfo.name.charAt(0).toUpperCase()}
                    </Text>
                </View>
                <View>
                    <Text className="font-extrabold text-gray-800 text-base">{userInfo.name}</Text>
                    <Text className="text-gray-500 text-xs font-medium">{userInfo.phone}</Text>
                </View>
            </View>
            <View className="flex-row">
                <TouchableOpacity
                    onPress={handleEditProfile}
                    className="mr-3 px-4 py-2 rounded-lg bg-madder/10 flex-row items-center justify-center"
                >
                    <Text className="text-madder font-bold text-xs">Edit</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    onPress={() => navigation.navigate('Settings')}
                    className="w-9 h-9 rounded-full bg-gray-100 items-center justify-center"
                >
                    <MaterialIcons name="settings" size={20} color="#333" />
                </TouchableOpacity>
            </View>
        </Animated.View>
    );
};

// Menu Item Component
const MenuItem = ({ item, onPress }) => {
    return (
        <TouchableOpacity
            className="bg-white px-5 py-4 flex-row justify-between items-center mx-4 mb-3 rounded-lg shadow-sm"
            onPress={onPress}
            activeOpacity={0.7}
        >
            <View className="flex-row items-center">
                <View className="w-11 h-11 justify-center items-center bg-madder/10 rounded-full">
                    <MaterialIcons name={item.icon} size={22} color="#A31621" />
                </View>
                <View className="ml-4">
                    <Text className="text-base font-bold text-gray-800">{item.title}</Text>
                    {item.subtitle && (
                        <Text className="text-gray-500 text-xs font-medium mt-1">{item.subtitle}</Text>
                    )}
                </View>
            </View>
            <MaterialIcons name="chevron-right" size={24} color="#999" />
        </TouchableOpacity>
    );
};

// Toast Notification Component
const ToastNotification = ({ visible, message, type, animation, translateY }) => {
    const getBgColor = () => {
        switch (type) {
            case 'success': return 'bg-green-500';
            case 'error': return 'bg-red-500';
            default: return 'bg-green-500';
        }
    };

    const getIcon = () => {
        switch (type) {
            case 'success': return 'check-circle';
            case 'error': return 'error';
            default: return 'check-circle';
        }
    };

    if (!visible) return null;

    return (
        <Animated.View
            className={`absolute bottom-20 left-5 right-5 ${getBgColor()} rounded-lg p-4 flex-row items-center`}
            style={{
                transform: [{ translateY }],
                opacity: animation,
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                elevation: 5,
            }}
        >
            <MaterialIcons name={getIcon()} size={24} color="white" />
            <Text className="text-white font-medium ml-2 flex-1">{message}</Text>
        </Animated.View>
    );
};

// Add this new Banner component after the existing components
const PromoBanner = ({ opacity, translateY }) => {
    return (
        <Animated.View
            style={{
                opacity,
                transform: [{ translateY }],
                height: 120, // Increased height for better visibility
                marginHorizontal: 16,
                marginTop: 15,
                marginBottom: 15,
                borderRadius: 20, // More rounded corners
                overflow: 'hidden',
                backgroundColor: 'white',
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.15,
                shadowRadius: 8,
                elevation: 5, // Enhanced shadow
            }}
        >
            <Image
                source={require('../assets/banner1.png')}
                style={{ width: '100%', height: '100%' }}
                resizeMode="cover"
            />
        </Animated.View>
    );
};

// In the ProfileScreen component, add these animation values
const ProfileScreen = ({ navigation }) => {
    // Get user data from AsyncStorage
    const [userInfo, setUserInfo] = useState({
        name: 'New User',
        phone: '9xxxxxxxx',
    });

    // Get user data from AuthContext
    const { isLoggedIn } = useAuth();

    // Load user data from AsyncStorage
    React.useEffect(() => {
        const loadUserData = async () => {
            try {
                const { getUserData } = require('../utils/authStorage');
                const userData = await getUserData();

                if (userData) {
                    console.log('User data loaded:', userData);
                    setUserInfo({
                        name: userData.name || 'New User',
                        phone: userData.phoneNumber || userData.number || '9xxxxxxxx',
                    });
                }
            } catch (error) {
                console.error('Error loading user data:', error);
            }
        };

        loadUserData();
    }, [isLoggedIn]);

    // Create animated values for toast notification
    const toastAnimation = useRef(new Animated.Value(0)).current;
    const [toastVisible, setToastVisible] = useState(false);
    const [toastMessage, setToastMessage] = useState('');
    const [toastType, setToastType] = useState('success');

    // Create animated scroll value for smooth animations
    const scrollY = useRef(new Animated.Value(0)).current;

    // Animation values for collapsible header
    const headerHeight = 160; // Increased from 180
    const collapsedHeight = 80; // Increased from 90

    // Calculate animations based on scroll position
    const headerTranslate = scrollY.interpolate({
        inputRange: [0, headerHeight - collapsedHeight],
        outputRange: [0, -(headerHeight - collapsedHeight)],
        extrapolate: 'clamp'
    });

    const profileInfoOpacity = scrollY.interpolate({
        inputRange: [0, 40, 80],  // Adjusted for smoother transition
        outputRange: [1, 0.5, 0],
        extrapolate: 'clamp'
    });

    const collapsedHeaderOpacity = scrollY.interpolate({
        inputRange: [0, 60, 100],  // Adjusted to match new header heights
        outputRange: [0, 0.7, 1],
        extrapolate: 'clamp'
    });

    const menuItems = [
        { id: 1, title: 'Orders', subtitle: 'Check Your order status', icon: 'receipt-long', onPress: () => navigation.navigate('Checkout') },
        { id: 2, title: 'Address', subtitle: '0 saved address', icon: 'location-on', onPress: () => navigation.navigate('DeliveryAddressScreen') },
        { id: 3, title: 'Coins', subtitle: 'Check your coins', icon: 'monetization-on' },
        { id: 4, title: 'Contact us', icon: 'headset-mic' },
        { id: 5, title: 'FAQ', icon: 'help' },
        { id: 6, title: 'Language', icon: 'language' },
        { id: 7, title: 'Logout', icon: 'logout', onPress: handleLogout }
    ];

    // Show toast notification
    const showToast = (message, type = 'success') => {
        setToastMessage(message);
        setToastType(type);
        setToastVisible(true);

        Animated.sequence([
            // Animate in
            Animated.timing(toastAnimation, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            // Hold
            Animated.delay(2000),
            // Animate out
            Animated.timing(toastAnimation, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            })
        ]).start(() => {
            setToastVisible(false);
        });
    };

    // Get the logout function from AuthContext
    const { logout } = useAuth();

    async function handleLogout() {
        try {
            console.log('Logging out...');

            // Show toast notification
            showToast('Logging out...', 'success');

            // Call the logout function from AuthContext
            const success = await logout();

            if (success) {
                console.log('Logout successful');

                // Show success toast
                showToast('Logged out successfully!', 'error');

                // Create a timeout to navigate after showing the toast
                setTimeout(() => {
                    // Reset navigation stack to Auth screen
                    navigation.reset({
                        index: 0,
                        routes: [{ name: 'Auth' }],
                    });
                }, 2000);
            } else {
                console.log('Logout failed');
                showToast('Logout failed. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Error during logout:', error);
            showToast('Logout failed. Please try again.', 'error');
        }
    }

    const handleEditProfile = () => {
        navigation.navigate('EditProfileScreen', {
            userInfo,
            onUpdate: (updatedInfo) => {
                setUserInfo(updatedInfo);
                showToast('Profile updated successfully!', 'success');
            }
        });
    };

    // Toast animation styles
    const toastTranslateY = toastAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [100, 0],
    });

    // Add banner animation values
    const bannerOpacity = scrollY.interpolate({
        inputRange: [0, 40, 80],
        outputRange: [1, 0.5, 0],
        extrapolate: 'clamp'
    });

    const bannerTranslate = scrollY.interpolate({
        inputRange: [0, 80],
        outputRange: [0, -30],
        extrapolate: 'clamp'
    });

    return (
        <View className="flex-1 bg-gray-100">
            <StatusBar backgroundColor="#fff" barStyle="dark-content" />

            {/* Collapsed Header Component */}
            <CollapsedHeader
                userInfo={userInfo}
                handleEditProfile={handleEditProfile}
                navigation={navigation}
                opacity={collapsedHeaderOpacity}
            />

            {/* Main Header Component */}
            <Animated.View
                className="absolute top-0 left-0 right-0 z-10 bg-white shadow-md rounded-b-3xl"
                style={{
                    transform: [{ translateY: headerTranslate }],
                    opacity: profileInfoOpacity,
                }}
            >
                <ProfileHeader
                    userInfo={userInfo}
                    handleEditProfile={handleEditProfile}
                    navigation={navigation}
                />
            </Animated.View>

            <Animated.ScrollView
                className="flex-1"
                showsVerticalScrollIndicator={false}
                scrollEventThrottle={16}
                onScroll={Animated.event(
                    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                    { useNativeDriver: true }
                )}
                contentContainerStyle={{
                    paddingTop: headerHeight + 15, // Reduced from 30 to create more even spacing
                    paddingBottom: 20
                }}
            >
                {/* Promo Banner Component - with additional top margin */}
                <PromoBanner
                    opacity={bannerOpacity}
                    translateY={bannerTranslate}
                />

                {/* Menu Items using MenuItem Component */}
                {menuItems.map((item) => (
                    <MenuItem
                        key={item.id}
                        item={item}
                        onPress={() => item.onPress ? item.onPress() : console.log(`Pressed: ${item.title}`)}
                    />
                ))}
            </Animated.ScrollView>

            {/* Toast Notification Component */}
            <ToastNotification
                visible={toastVisible}
                message={toastMessage}
                type={toastType}
                animation={toastAnimation}
                translateY={toastTranslateY}
            />
        </View>
    );
};

export default ProfileScreen;
