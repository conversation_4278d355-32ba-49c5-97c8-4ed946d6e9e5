import axios from 'axios';
import { API_URL, USER_TYPES } from '../../config/constants';
import { getAuthToken, getUserData } from '../authStorage';

// Get dashboard analytics
export const getDashboardAnalytics = async () => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('AnalyticsAPI: User type:', userType);

        // Only admins should be able to fetch dashboard analytics
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch dashboard analytics');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.get(`${API_URL}/admin/analytics/dashboard`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching dashboard analytics:', error);
        throw error;
    }
};

// Get sales analytics
export const getSalesAnalytics = async (period = 'monthly') => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('User type from userData:', userType);

        // Only admins should be able to fetch sales analytics
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch sales analytics');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.get(`${API_URL}/admin/analytics/sales?period=${period}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Error fetching sales analytics for period ${period}:`, error);
        throw error;
    }
};

// Get user analytics
export const getUserAnalytics = async () => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('User type from userData:', userType);

        // Only admins should be able to fetch user analytics
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch user analytics');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.get(`${API_URL}/admin/analytics/users`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching user analytics:', error);
        throw error;
    }
};

// Get product analytics
export const getProductAnalytics = async () => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('User type from userData:', userType);

        // Only admins should be able to fetch product analytics
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch product analytics');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.get(`${API_URL}/admin/analytics/products`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching product analytics:', error);
        throw error;
    }
};

// Get delivery partner analytics
export const getDeliveryPartnerAnalytics = async () => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('AnalyticsAPI: User type for delivery partner analytics:', userType);

        // Only admins should be able to fetch delivery partner analytics
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can fetch delivery partner analytics');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.get(`${API_URL}/admin/analytics/delivery-partners`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching delivery partner analytics:', error);
        throw error;
    }
};
