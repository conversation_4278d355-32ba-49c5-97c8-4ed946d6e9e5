import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert, Linking, RefreshControl, ActivityIndicator } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from '@react-navigation/native';
import axios from 'axios';
import { API_URL } from '../../config/constants';

const HelpSupportScreen = () => {
    const navigation = useNavigation();
    const [activeTab, setActiveTab] = useState('faq');
    const [faqs, setFaqs] = useState([]);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);

    // Fetch FAQs on component mount
    useEffect(() => {
        fetchFaqs();
    }, []);

    // Fetch FAQs - use default FAQs immediately for better UX
    const fetchFaqs = async () => {
        try {
            // Set default FAQs immediately to avoid loading state
            setFaqs(defaultFaqs);
            setLoading(false);
            setRefreshing(false);

            // Optionally try to fetch from API in background (silent)
            // This won't affect the user experience if it fails
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000);

            const response = await axios.get(`${API_URL}/faqs/delivery-partner`, {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            // Only update if we get valid data from API
            if (response.data && Array.isArray(response.data) && response.data.length > 0) {
                setFaqs(response.data);
            } else if (response.data && response.data.faqs && Array.isArray(response.data.faqs) && response.data.faqs.length > 0) {
                setFaqs(response.data.faqs);
            }
        } catch (error) {
            // Silently handle API errors - default FAQs are already loaded
            // No console.error to avoid cluttering logs with expected 404s
            console.log('Using default FAQs (API endpoint not available)');
        }
    };



    const handleCall = () => {
        Linking.openURL('tel:+918825549901');
    };

    const handleEmail = () => {
        Linking.openURL('mailto:<EMAIL>');
    };

    // Default FAQs to use if API fails
    const defaultFaqs = [
        {
            question: "How do I update my vehicle information?",
            answer: "You can update your vehicle information by going to your profile, tapping on 'Edit Profile', and updating the vehicle details in the form."
        },
        {
            question: "How are my earnings calculated?",
            answer: "Your earnings are calculated based on the distance traveled, time taken, and the base delivery fee. You receive 10% of the order value as commission."
        },
        {
            question: "When will I receive my payments?",
            answer: "Payments are processed weekly. All earnings accumulated until Sunday midnight will be transferred to your bank account by Tuesday."
        },
        {
            question: "What should I do if a customer is not available?",
            answer: "If a customer is not available, try calling them. If they don't respond, wait for 5 minutes, then call support for further instructions."
        },
        {
            question: "How do I report an issue with an order?",
            answer: "You can report issues by contacting our support team through the 'Contact Us' tab in the Help & Support section."
        }
    ];

    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-6 pt-16 pb-6 flex-row items-center rounded-b-3xl">
                <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                    <MaterialIcons name="arrow-back" size={24} color="white" />
                </TouchableOpacity>
                <Text className="text-xl text-white font-bold">Help & Support</Text>
            </View>

            <View className="flex-row bg-white mx-4 mt-4 rounded-xl overflow-hidden">
                <TouchableOpacity
                    className={`flex-1 py-3 px-2 ${activeTab === 'faq' ? 'bg-madder' : 'bg-white'}`}
                    onPress={() => setActiveTab('faq')}
                >
                    <Text
                        className={`text-center font-medium ${activeTab === 'faq' ? 'text-white' : 'text-gray-600'}`}
                    >
                        FAQs
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    className={`flex-1 py-3 px-2 ${activeTab === 'contact' ? 'bg-madder' : 'bg-white'}`}
                    onPress={() => setActiveTab('contact')}
                >
                    <Text
                        className={`text-center font-medium ${activeTab === 'contact' ? 'text-white' : 'text-gray-600'}`}
                    >
                        Contact Us
                    </Text>
                </TouchableOpacity>
            </View>

            <ScrollView
                className="p-4"
                contentContainerStyle={{ paddingBottom: 100 }}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={fetchFaqs}
                        colors={['#A31621']}
                        tintColor="#A31621"
                    />
                }
            >
                {activeTab === 'faq' ? (
                    <View>
                        {loading && faqs.length === 0 ? (
                            <View className="items-center justify-center py-10">
                                <ActivityIndicator size="large" color="#A31621" />
                                <Text className="text-gray-500 mt-4">Loading FAQs...</Text>
                            </View>
                        ) : (
                            faqs.map((faq, index) => (
                                <View
                                    key={index}
                                    className="bg-white rounded-xl p-4 mb-4 shadow-sm"
                                >
                                    <Text className="text-gray-800 font-bold mb-2">{faq.question}</Text>
                                    <Text className="text-gray-600">{faq.answer}</Text>
                                </View>
                            ))
                        )}
                        {!loading && faqs.length === 0 && (
                            <View className="items-center justify-center py-10">
                                <MaterialIcons name="help-outline" size={48} color="#ccc" />
                                <Text className="text-gray-400 mt-2 text-lg">No FAQs available</Text>
                                <TouchableOpacity
                                    className="mt-4 bg-madder py-2 px-4 rounded-lg"
                                    onPress={fetchFaqs}
                                >
                                    <Text className="text-white">Refresh</Text>
                                </TouchableOpacity>
                            </View>
                        )}
                    </View>
                ) : (
                    <View>
                        <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                            <Text className="text-lg font-bold mb-3">Contact Information</Text>

                            <TouchableOpacity
                                className="flex-row items-center py-3 border-b border-gray-100"
                                onPress={handleCall}
                            >
                                <MaterialIcons name="call" size={20} color="#16A34A" />
                                <Text className="ml-3 text-gray-700">+91 8825549901</Text>
                                <Text className="ml-2 text-gray-500 text-xs">(Mon-Sat, 9AM-6PM)</Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                className="flex-row items-center py-3"
                                onPress={handleEmail}
                            >
                                <MaterialIcons name="email" size={20} color="#A31621" />
                                <Text className="ml-3 text-gray-700"><EMAIL></Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                )}
            </ScrollView>
        </View>
    );
};

export default HelpSupportScreen;
