import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, TouchableOpacity, TextInput, Image, Modal, Alert, ActivityIndicator, RefreshControl, FlatList } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { useAdmin } from '../../context/AdminContext';
import { getAllProducts, getProductById, deleteProduct as apiDeleteProduct } from '../../utils/api/productApi';

const AdminProductsScreen = ({ navigation }) => {
    const { products: contextProducts, loading: contextLoading } = useAdmin();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState(['all']);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [error, setError] = useState(null);

    // Fetch products from API
    const fetchProducts = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            console.log('Fetching products from API...');
            const response = await getAllProducts();

            if (response && response.products && response.products.length > 0) {
                console.log(`Fetched ${response.products.length} products from API`);

                // Process products and extract categories
                const fetchedProducts = response.products;
                setProducts(fetchedProducts);

                // Extract unique categories
                const uniqueCategories = ['all'];
                fetchedProducts.forEach(product => {
                    if (product.category && typeof product.category === 'string' && !uniqueCategories.includes(product.category.toLowerCase())) {
                        uniqueCategories.push(product.category.toLowerCase());
                    }
                });

                setCategories(uniqueCategories);
            } else if (contextProducts && contextProducts.length > 0) {
                // Fallback to context products
                console.log('Using products from context');
                setProducts(contextProducts);

                // Extract unique categories from context products
                const uniqueCategories = ['all'];
                contextProducts.forEach(product => {
                    if (product.category && typeof product.category === 'string' && !uniqueCategories.includes(product.category.toLowerCase())) {
                        uniqueCategories.push(product.category.toLowerCase());
                    }
                });

                setCategories(uniqueCategories);
            } else {
                console.log('No products found');
                setProducts([]);
            }
        } catch (err) {
            console.error('Error fetching products:', err);
            setError('Failed to load products. Please try again.');

            // Fallback to context products if API fails
            if (contextProducts && contextProducts.length > 0) {
                setProducts(contextProducts);
            }
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [contextProducts]);

    // Initial fetch
    useEffect(() => {
        fetchProducts();

        // Cleanup function to prevent memory leaks
        return () => {
            setProducts([]);
            setLoading(false);
            setRefreshing(false);
        };
    }, [fetchProducts]);

    // Pull to refresh handler
    const onRefresh = useCallback(() => {
        setRefreshing(true);
        fetchProducts();
    }, [fetchProducts]);

    // Filter products based on search and category with memoization for performance
    const filteredProducts = React.useMemo(() => {
        if (!products || products.length === 0) return [];

        return products.filter(product => {
            // Handle null/undefined product name with better error handling
            const matchesSearch = !searchQuery ||
                (product?.name && typeof product.name === 'string' &&
                 product.name.toLowerCase().includes(searchQuery.toLowerCase()));

            // Handle category filtering with better support for different data formats
            let matchesCategory = selectedCategory === 'all';

            if (!matchesCategory && product) {
                // Handle string category
                if (product.category && typeof product.category === 'string') {
                    matchesCategory = product.category.toLowerCase() === selectedCategory;
                }
                // Handle object category with name property
                else if (product.category && typeof product.category === 'object' && product.category.name) {
                    matchesCategory = product.category.name.toLowerCase() === selectedCategory;
                }
                // Handle categoryName property
                else if (product.categoryName && typeof product.categoryName === 'string') {
                    matchesCategory = product.categoryName.toLowerCase() === selectedCategory;
                }
            }

            return matchesSearch && matchesCategory;
        });
    }, [products, searchQuery, selectedCategory]);

    const handleDeleteProduct = useCallback(async (productId) => {
        if (!productId) {
            Alert.alert("Error", "Invalid product ID");
            return;
        }

        Alert.alert(
            "Delete Product",
            "Are you sure you want to delete this product? This action cannot be undone.",
            [
                { text: "Cancel", style: "cancel" },
                {
                    text: "Delete",
                    style: "destructive",
                    onPress: async () => {
                        try {
                            setLoading(true);
                            // Call API to delete product
                            await apiDeleteProduct(productId);

                            // Update local state
                            const updatedProducts = products.filter(p =>
                                p.id !== productId && p._id !== productId
                            );
                            setProducts(updatedProducts);

                            Alert.alert("Success", "Product deleted successfully");
                        } catch (error) {
                            console.error('Error deleting product:', error);
                            Alert.alert("Error", "Failed to delete product. Please try again.");
                        } finally {
                            setLoading(false);
                        }
                    }
                }
            ]
        );
    }, [products]);

    const handleEditProduct = async (product) => {
        try {
            setLoading(true);

            // Validate product object
            if (!product) {
                throw new Error('Invalid product data');
            }

            // Get the latest product data from the API
            const productId = product._id || product.id;

            if (!productId) {
                console.error('Product has no valid ID:', product);
                Alert.alert('Error', 'This product has no valid ID. Please refresh the product list.');
                return;
            }

            console.log(`Fetching product details for ID: ${productId}`);

            const response = await getProductById(productId);

            if (response && response.product) {
                console.log('Successfully fetched product details:', response.product.name);
                // Navigate to edit screen with the latest product data
                navigation.navigate('EditProduct', { product: response.product });
            } else {
                console.warn('API returned success but no product data, using local data');
                // Fallback to using the product data we already have
                navigation.navigate('EditProduct', { product });
            }
        } catch (error) {
            console.error('Error fetching product details:', error);

            // Show error message to user
            Alert.alert(
                'Error Fetching Product',
                `Could not fetch the latest product data: ${error.message}. Would you like to continue with the existing data?`,
                [
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    },
                    {
                        text: 'Continue',
                        onPress: () => {
                            // If there's an error, still navigate but with the existing product data
                            navigation.navigate('EditProduct', { product });
                        }
                    }
                ]
            );
        } finally {
            setLoading(false);
        }
    };

    // Render product item
    const renderProductItem = ({ item: product }) => {
        // Handle missing product data safely
        const productId = product._id || product.id || 'unknown';
        const productName = product.name || 'Unnamed Product';
        const productPrice = product.price || 0;
        const discountPrice = product.discount_price || (product.discountPercentage > 0 ?
            (productPrice * (1 - (product.discountPercentage / 100))).toFixed(0) : productPrice);
        const categoryName = typeof product.category === 'string' ? product.category :
            (product.category?.name || 'Uncategorized');

        return (
            <TouchableOpacity
                className="bg-white rounded-xl mb-4 overflow-hidden mx-0 shadow-sm"
                style={{
                    shadowColor: "#000",
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.1,
                    shadowRadius: 3,
                    elevation: 3,
                    width: '100%'
                }}
                onPress={() => handleEditProduct(product)}
                activeOpacity={0.7}
            >
                {/* Product Header with Image and Basic Info */}
                <View className="p-4">
                    <View className="flex-row">
                        <Image
                            source={{ uri: product.image || 'https://via.placeholder.com/150' }}
                            className="w-24 h-24 rounded-lg"
                            resizeMode="cover"
                            onError={(e) => console.log('Image loading error:', e.nativeEvent.error)}
                        />

                        <View className="flex-1 pl-4 justify-center">
                            <Text className="text-lg font-bold text-gray-800" numberOfLines={2}>
                                {productName}
                            </Text>

                            <View className="flex-row items-baseline mt-2">
                                {product.discountPercentage > 0 ? (
                                    <>
                                        <Text className="text-madder font-bold text-2xl">₹{discountPrice}</Text>
                                        <Text className="text-gray-500 line-through ml-2">₹{productPrice}</Text>
                                    </>
                                ) : (
                                    <Text className="text-madder font-bold text-2xl">₹{productPrice}</Text>
                                )}
                            </View>

                            {product.discountPercentage > 0 && (
                                <View className="bg-green-100 px-2 py-1 rounded mt-2 self-start">
                                    <Text className="text-green-700 text-xs font-medium">{product.discountPercentage}% OFF</Text>
                                </View>
                            )}

                            {product.weight && (
                                <Text className="text-gray-500 mt-2">
                                    {product.weight}
                                </Text>
                            )}
                        </View>
                    </View>
                </View>

                {/* Product Details and Actions */}
                <View className="px-3 pb-3">
                    <View className="flex-row items-center justify-between mt-1 pt-2 border-t border-gray-100">
                        <View className="flex-row items-center">
                            <View className={`h-3 w-3 rounded-full ${product.isAvailable !== false ? 'bg-green-500' : 'bg-red-500'} mr-2`} />
                            <Text className={`${product.isAvailable !== false ? 'text-green-600' : 'text-red-600'} font-medium`}>
                                {product.isAvailable !== false ? 'In Stock' : 'Out of Stock'}
                            </Text>
                        </View>

                        <Text className="text-gray-500 capitalize">
                            {categoryName.toLowerCase()}
                        </Text>
                    </View>
                </View>

                {/* Action Buttons */}
                <View className="bg-gray-50 px-3 py-3 flex-row justify-end items-center">
                    <View className="flex-row">
                        <TouchableOpacity
                            onPress={() => handleEditProduct(product)}
                            className="bg-snow px-4 py-2 rounded-lg mr-3 flex-row items-center shadow-sm"
                            style={{
                                shadowColor: "#000",
                                shadowOffset: { width: 0, height: 1 },
                                shadowOpacity: 0.1,
                                shadowRadius: 1,
                                elevation: 1
                            }}
                        >
                            <MaterialIcons name="edit" size={18} color="#666" />
                            <Text className="text-gray-700 font-medium ml-1">Edit</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            onPress={() => handleDeleteProduct(productId)}
                            className="bg-madder px-4 py-2 rounded-lg flex-row items-center shadow-sm"
                            style={{
                                shadowColor: "#A31621",
                                shadowOffset: { width: 0, height: 1 },
                                shadowOpacity: 0.2,
                                shadowRadius: 1,
                                elevation: 1
                            }}
                        >
                            <MaterialIcons name="delete" size={18} color="#FCF7F8" />
                            <Text className="text-snow font-medium ml-1">Delete</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };

    // Predefined category filters
    const predefinedCategories = ['all', 'chicken', 'mutton', 'fish', 'egg', 'combo'];

    // Filter categories to show predefined ones first, then others
    const displayCategories = [...new Set([
        ...predefinedCategories.filter(c => categories.includes(c)),
        ...categories.filter(c => !predefinedCategories.includes(c))
    ])];

    // Category icons mapping
    const categoryIcons = {
        'all': 'apps',
        'chicken': 'restaurant',
        'mutton': 'restaurant-menu',
        'fish': 'set-meal',
        'egg': 'egg',
        'seafood': 'water',
        'combo': 'shopping-basket',
        'prawns': 'water',
        'crab': 'water'
    };

    return (
        <View className="flex-1 bg-snow">
            {/* Header */}
            <View className="bg-madder px-6 pt-10 rounded-b-3xl pb-4">
                <View className="flex-row justify-between items-center mb-3">
                    <View>
                        <Text className="text-xl text-white font-bold">Products</Text>
                        <Text className="text-white/80 text-xs mt-1">Manage your product inventory</Text>
                    </View>
                    <TouchableOpacity
                        className="bg-white w-10 h-10 rounded-full items-center justify-center shadow-md"
                        onPress={() => navigation.navigate('AddProduct')}
                        style={{
                            shadowColor: "#000",
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.2,
                            shadowRadius: 3,
                            elevation: 3
                        }}
                    >
                        <MaterialIcons name="add" size={22} color="#A31621" />
                    </TouchableOpacity>
                </View>

                {/* Search Bar in Header */}
                <View
                    className="bg-snow rounded-xl flex-row items-center px-4 mr-1 mb-1"
                    style={{
                        shadowColor: '#A31621',
                        shadowOffset: { width: 3, height: 2 },
                        shadowOpacity: 0.15,
                        shadowRadius: 4,
                        elevation: 4,
                    }}
                >
                    <MaterialIcons name="search" size={24} color="#A31621" />
                    <TextInput
                        placeholder="Search products by name..."
                        className="flex-1 p-3"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor="#9CA3AF"
                        style={{ color: "#333" }}
                    />
                    {searchQuery.length > 0 && (
                        <TouchableOpacity
                            onPress={() => setSearchQuery('')}
                            className="bg-gray-100 p-1 rounded-full"
                        >
                            <MaterialIcons name="close" size={20} color="#A31621" />
                        </TouchableOpacity>
                    )}
                </View>
            </View>

            <View className="flex-1 px-2">
                {/* Category Filter Buttons - Horizontal with only names */}
                <View className="mt-4 mb-2">
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        className="py-2"
                    >
                        {['all', 'chicken', 'mutton', 'fish', 'egg', 'combo'].map(category => (
                            <TouchableOpacity
                                key={category}
                                onPress={() => setSelectedCategory(category)}
                                className={`mr-3 px-6 py-3 rounded-xl ${
                                    selectedCategory === category
                                        ? 'bg-madder shadow-md'
                                        : 'bg-white border border-gray-200'
                                }`}
                                style={selectedCategory === category ? {
                                    shadowColor: "#A31621",
                                    shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.2,
                                    shadowRadius: 3,
                                    elevation: 3
                                } : {}}
                            >
                                <Text className={`font-medium ${
                                    selectedCategory === category
                                        ? 'text-white'
                                        : 'text-gray-700'
                                }`}>
                                    {category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1)}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </ScrollView>
                </View>

                {/* Products Count */}
                <View className="flex-row justify-between items-center mb-2 px-1">
                    <Text className="text-gray-500 text-sm">
                        {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'} found
                    </Text>
                    {selectedCategory !== 'all' && (
                        <TouchableOpacity
                            className="flex-row items-center"
                            onPress={() => setSelectedCategory('all')}
                        >
                            <Text className="text-madder text-sm mr-1">Clear Filter</Text>
                            <MaterialIcons name="clear" size={16} color="#A31621" />
                        </TouchableOpacity>
                    )}
                </View>

                {/* Products List */}
                {loading ? (
                    <View className="flex-1 justify-center items-center">
                        <ActivityIndicator size="large" color="#A31621" />
                        <Text className="text-gray-500 mt-4">Loading products...</Text>
                    </View>
                ) : filteredProducts.length === 0 ? (
                    <View className="flex-1 justify-center items-center bg-white rounded-xl p-8 mx-2">
                        <MaterialIcons name="inventory" size={64} color="#A31621" opacity={0.2} />
                        <Text className="text-gray-700 text-lg font-bold mt-4">No products found</Text>
                        <Text className="text-gray-500 text-center mt-2">
                            {searchQuery
                                ? "Try a different search term or clear the filter"
                                : selectedCategory !== 'all'
                                    ? `No products in the "${selectedCategory}" category`
                                    : "Add your first product to get started"
                            }
                        </Text>

                        {!searchQuery && selectedCategory === 'all' && (
                            <TouchableOpacity
                                className="bg-madder px-6 py-3 rounded-xl flex-row items-center mt-6"
                                onPress={() => navigation.navigate('AddProduct')}
                            >
                                <MaterialIcons name="add" size={20} color="white" />
                                <Text className="text-white font-bold ml-2">Add Product</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                ) : (
                    <FlatList
                        data={filteredProducts}
                        renderItem={renderProductItem}
                        keyExtractor={item => item._id || item.id}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{
                            paddingBottom: 100,
                            paddingHorizontal: 8
                        }}
                        refreshControl={
                            <RefreshControl
                                refreshing={refreshing}
                                onRefresh={onRefresh}
                                colors={["#A31621"]}
                                tintColor="#A31621"
                            />
                        }
                        initialNumToRender={5}
                        maxToRenderPerBatch={10}
                        windowSize={10}
                    />
                )}
            </View>
        </View>
    );
};

export default AdminProductsScreen;