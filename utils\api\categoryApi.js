import axios from 'axios';
import { API_URL, USER_TYPES } from '../../config/constants';
import { getAuthToken, getUserData } from '../authStorage';

// Cache for categories data
let categoriesCache = {
    data: null,
    timestamp: 0
};

// Function to clear categories cache
export const clearCategoriesCache = () => {
    categoriesCache = { data: null, timestamp: 0 };
    console.log('Categories cache cleared');
};

// Get all categories with caching
export const getAllCategories = async () => {
    try {
        // Check cache first (10 minutes cache - categories change less frequently)
        const now = new Date().getTime();
        const cacheAge = now - categoriesCache.timestamp;

        if (categoriesCache.data && cacheAge < 600000) { // 10 minutes
            console.log('Using cached categories data, age:', cacheAge, 'ms');
            return categoriesCache.data;
        }

        const response = await axios.get(`${API_URL}/categories`, {
            timeout: 10000 // 10 second timeout
        });

        console.log('Categories API response status:', response.status);
        console.log('Categories found:', response.data.length);

        // Update cache
        categoriesCache = {
            data: response.data,
            timestamp: now
        };

        return response.data;
    } catch (error) {
        console.error('Error fetching categories:', error);
        console.error('Error details:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url
        });

        // Return cached data if available on error
        if (categoriesCache.data) {
            console.log('API error, using cached categories data as fallback');
            return categoriesCache.data;
        }

        throw error;
    }
};

// Get category by ID
export const getCategoryById = async (categoryId) => {
    try {
        const response = await axios.get(`${API_URL}/categories/${categoryId}`, {
            timeout: 10000 // 10 second timeout
        });

        return response.data;
    } catch (error) {
        console.error(`Error fetching category ${categoryId}:`, error);
        throw error;
    }
};

// Create category (admin only)
export const createCategory = async (categoryData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('CategoryAPI: User type for createCategory:', userType);

        // Only admins should be able to create categories
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can create categories');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.post(`${API_URL}/categories`, categoryData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        return response.data;
    } catch (error) {
        console.error('Error creating category:', error);
        throw error;
    }
};

// Update category (admin only)
export const updateCategory = async (categoryId, categoryData) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('CategoryAPI: User type for updateCategory:', userType);

        // Only admins should be able to update categories
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can update categories');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.put(`${API_URL}/categories/${categoryId}`, categoryData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        return response.data;
    } catch (error) {
        console.error(`Error updating category ${categoryId}:`, error);
        throw error;
    }
};

// Delete category (admin only)
export const deleteCategory = async (categoryId) => {
    try {
        const token = await getAuthToken();

        // Get user data to check user type
        const userData = await getUserData();
        const userType = userData?.userType?.toUpperCase();

        console.log('CategoryAPI: User type for deleteCategory:', userType);

        // Only admins should be able to delete categories
        if (userType !== USER_TYPES.ADMIN) {
            console.error('Unauthorized: Only admins can delete categories');
            throw new Error('Unauthorized: Admin access required');
        }

        const response = await axios.delete(`${API_URL}/categories/${categoryId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });

        return response.data;
    } catch (error) {
        console.error(`Error deleting category ${categoryId}:`, error);
        throw error;
    }
};
