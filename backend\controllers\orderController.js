const Order = require('../models/Order');
const User = require('../models/User');
const DeliveryPartner = require('../models/DeliveryPartner');
const mongoose = require('mongoose'); // Import mongoose for transactions
const { syncOrderWithDeliveryPartner } = require('./deliveryController');
const { processOrderInvoice } = require('../services/invoiceService');

/**
 * Get all orders
 * @route GET /api/orders
 * @access Private
 */
const getAllOrders = async (req, res) => {
    try {
        // Different queries based on user type
        let orders;

        if (req.userType === 'ADMIN') {
            // <PERSON><PERSON> can see all orders
            orders = await Order.find()
                .populate('userId', 'name number')
                .populate('deliveryPartner', 'name phoneNumber')
                .sort({ createdAt: -1 });
        } else if (req.userType === 'DELIVERY_PARTNER') {
            // Delivery partners can only see their assigned orders
            orders = await Order.find({ deliveryPartner: req.user._id })
                .populate('userId', 'name number')
                .sort({ createdAt: -1 });
        } else {
            // Regular users can only see their own orders
            orders = await Order.find({ userId: req.user._id })
                .populate('deliveryPartner', 'name phoneNumber')
                .sort({ createdAt: -1 });
        }

        res.status(200).json(orders);
    } catch (error) {
        console.error('Error fetching orders:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get order by ID
 * @route GET /api/orders/:id
 * @access Private
 */
const getOrderById = async (req, res) => {
    try {
        const order = await Order.findById(req.params.id)
            .populate('userId', 'name number email')
            .populate('deliveryPartner', 'name phoneNumber');

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Check if user has permission to view this order
        if (req.userType !== 'ADMIN' &&
            req.userType !== 'DELIVERY_PARTNER' &&
            order.userId._id.toString() !== req.user._id.toString()) {
            return res.status(403).json({ message: 'Not authorized to view this order' });
        }

        // If delivery partner, check if they are assigned to this order
        if (req.userType === 'DELIVERY_PARTNER' &&
            (!order.deliveryPartner || order.deliveryPartner._id.toString() !== req.user._id.toString())) {
            return res.status(403).json({ message: 'Not authorized to view this order' });
        }

        res.status(200).json(order);
    } catch (error) {
        console.error('Error fetching order:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Create new order
 * @route POST /api/orders
 * @access Private
 */
const createOrder = async (req, res) => {
    // Start a Mongoose session for transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        console.log('Creating new order with data:', JSON.stringify(req.body, null, 2));
        const {
            items,
            total,
            deliveryAddress,
            deliveryCoordinates, // Extract deliveryCoordinates directly from request body
            orderNumber,
            paymentMethod,
            couponDiscount, // Discount from coupon
            appliedCoupon,
            // coinsEarned, // We will calculate this based on final amount after coin discount
            coinsToApply, // Number of coins user wants to apply
            expectedDelivery,
            originalAmount: clientOriginalAmount, // Sum of item prices from client
            totalAmount: clientFinalTotalAmount    // Final amount client expects to pay
        } = req.body;

        // Validate essential inputs
        if (!items || !Array.isArray(items) || items.length === 0) {
            await session.abortTransaction();
            session.endSession();
            return res.status(400).json({ message: 'Please add at least one item to the order' });
        }

        if (clientOriginalAmount === undefined || clientOriginalAmount < 0) {
            await session.abortTransaction();
            session.endSession();
            return res.status(400).json({ message: 'Invalid original amount for items' });
        }

        if (!deliveryAddress) {
            return res.status(400).json({ message: 'Delivery address is required' });
        }

        // Process delivery address
        let processedDeliveryAddress = deliveryAddress;
        // Use deliveryCoordinates from request body if available
        let extractedCoordinates = deliveryCoordinates || null;

        // Log the received delivery address and coordinates
        console.log('Received delivery address in createOrder:', {
            type: typeof deliveryAddress,
            isObject: typeof deliveryAddress === 'object',
            isString: typeof deliveryAddress === 'string',
            hasCoordinatesObject: !!(deliveryAddress.coordinates),
            coordinatesObject: deliveryAddress.coordinates,
            hasDirectCoordinates: !!(deliveryAddress.latitude && deliveryAddress.longitude),
            latitude: deliveryAddress.latitude,
            longitude: deliveryAddress.longitude,
            fullAddress: deliveryAddress.fullAddress,
            rawData: JSON.stringify(deliveryAddress)
        });

        // Log the received delivery coordinates
        console.log('Received delivery coordinates in createOrder:', {
            hasCoordinates: !!deliveryCoordinates,
            coordinates: deliveryCoordinates
        });

        // Ensure deliveryAddress has the required structure
        if (typeof deliveryAddress === 'object') {
            // Make sure we have at least some address information
            if (!deliveryAddress.fullAddress &&
                !(deliveryAddress.doorNo || deliveryAddress.streetName || deliveryAddress.area)) {
                await session.abortTransaction();
                session.endSession();
                return res.status(400).json({ message: 'Delivery address is incomplete' });
            }

            // Extract coordinates if not already provided in the request body
            if (!extractedCoordinates) {
                // Check for coordinates in different possible formats
                if (deliveryAddress.coordinates && deliveryAddress.coordinates.latitude && deliveryAddress.coordinates.longitude) {
                    // If coordinates are in a nested coordinates object
                    extractedCoordinates = {
                        latitude: parseFloat(deliveryAddress.coordinates.latitude),
                        longitude: parseFloat(deliveryAddress.coordinates.longitude)
                    };
                    console.log('Using coordinates from nested coordinates object:', extractedCoordinates);
                } else if (deliveryAddress.latitude && deliveryAddress.longitude) {
                    // If coordinates are direct properties
                    extractedCoordinates = {
                        latitude: parseFloat(deliveryAddress.latitude),
                        longitude: parseFloat(deliveryAddress.longitude)
                    };
                    console.log('Using coordinates from direct properties:', extractedCoordinates);
                } else {
                    console.log('WARNING: No coordinates found in delivery address object!');
                }
            } else {
                console.log('Using coordinates provided directly in request body:', extractedCoordinates);
            }

            // If fullAddress is missing but we have other fields, construct it
            if (!deliveryAddress.fullAddress && (deliveryAddress.doorNo || deliveryAddress.streetName)) {
                processedDeliveryAddress = {
                    ...deliveryAddress,
                    fullAddress: `${deliveryAddress.doorNo || ''}, ${deliveryAddress.streetName || ''}, ${deliveryAddress.area || ''}, ${deliveryAddress.district || ''}, ${deliveryAddress.pincode || ''}`.trim()
                };
            }
        } else if (typeof deliveryAddress === 'string') {
            // If it's a string, use it as is
            processedDeliveryAddress = deliveryAddress;

            // Try to find coordinates from user's saved addresses if not already found
            if (!extractedCoordinates) {
                try {
                    const user = await User.findById(req.user._id);
                    if (user && user.addresses && user.addresses.length > 0) {
                        // Look for an address that matches the string
                        const matchingAddress = user.addresses.find(addr =>
                            addr.fullAddress === deliveryAddress ||
                            `${addr.doorNo}, ${addr.streetName}, ${addr.area}, ${addr.district}, ${addr.pincode}` === deliveryAddress
                        );

                        if (matchingAddress) {
                            // Check for coordinates in different possible formats
                            if (matchingAddress.coordinates && matchingAddress.coordinates.latitude && matchingAddress.coordinates.longitude) {
                                // If coordinates are in a nested coordinates object
                                extractedCoordinates = {
                                    latitude: parseFloat(matchingAddress.coordinates.latitude),
                                    longitude: parseFloat(matchingAddress.coordinates.longitude)
                                };
                                console.log('Using coordinates from matching address nested coordinates:', extractedCoordinates);
                            } else if (matchingAddress.latitude && matchingAddress.longitude) {
                                // If coordinates are direct properties
                                extractedCoordinates = {
                                    latitude: parseFloat(matchingAddress.latitude),
                                    longitude: parseFloat(matchingAddress.longitude)
                                };
                                console.log('Using coordinates from matching address direct properties:', extractedCoordinates);
                            }
                        }
                    }

                    // If no matching address found, check the main address
                    if (!extractedCoordinates && user.address) {
                        if (user.address.coordinates && user.address.coordinates.latitude && user.address.coordinates.longitude) {
                            extractedCoordinates = {
                                latitude: parseFloat(user.address.coordinates.latitude),
                                longitude: parseFloat(user.address.coordinates.longitude)
                            };
                            console.log('Using coordinates from user main address nested coordinates:', extractedCoordinates);
                        } else if (user.address.latitude && user.address.longitude) {
                            extractedCoordinates = {
                                latitude: parseFloat(user.address.latitude),
                                longitude: parseFloat(user.address.longitude)
                            };
                            console.log('Using coordinates from user main address direct properties:', extractedCoordinates);
                        }
                    }
                } catch (error) {
                    console.error('Error finding coordinates for address:', error);
                    // Continue without coordinates if there's an error
                }
            }
        } else {
            await session.abortTransaction();
            session.endSession();
            return res.status(400).json({ message: 'Delivery address format is invalid' });
        }

        // Process items to ensure they have the correct format
        const processedItems = items.map(item => ({
            productId: item.productId,
            name: item.name,
            quantity: item.quantity,
            price: item.price || item.discount_price,
            image: item.image
        }));

        // Extract additional fields from request body
        const { expectedDeliveryInfo, originalAmount } = req.body; // Removed coinsDiscount

        // --- Server-side calculation of costs before coin discount ---
        const serverSubtotal = clientOriginalAmount; // This is sum of item prices
        const serverDeliveryFee = serverSubtotal >= 499 ? 0 : 49; // Simplified standard delivery fee logic
        // Removed COD fee as per requirement
        const serverAmountBeforeCouponsAndCoins = serverSubtotal + serverDeliveryFee;
        const serverAmountAfterCouponDiscount = serverAmountBeforeCouponsAndCoins - (couponDiscount || 0);

        // --- Coupon Usage Validation ---
        if (appliedCoupon && couponDiscount > 0) {
            // Check if user has already used this coupon
            const user = await User.findById(req.user._id).session(session);
            if (user && user.usedCoupons && user.usedCoupons.some(usedCoupon => usedCoupon.couponCode === appliedCoupon.code)) {
                await session.abortTransaction();
                return res.status(400).json({
                    message: 'This coupon has already been used by you.',
                    error: 'COUPON_ALREADY_USED'
                });
            }

            // Validate that user is not using both coupon and coins
            if (coinsToApply > 0) {
                await session.abortTransaction();
                return res.status(400).json({
                    message: 'You cannot use both coupons and coins in the same order.',
                    error: 'COUPON_COIN_CONFLICT'
                });
            }
        }

        // --- Coin Application Logic (Backend validation and application) ---
        let actualCoinsUsed = 0;
        let actualCoinsDiscount = 0;
        const user = await User.findById(req.user._id).session(session);
        const newCoinsHistoryEntries = [];

        if (user && coinsToApply > 0) {
            // Validate coin usage restrictions
            if (appliedCoupon && couponDiscount > 0) {
                await session.abortTransaction();
                return res.status(400).json({
                    message: 'You cannot use both coins and coupons in the same order.',
                    error: 'COIN_COUPON_CONFLICT'
                });
            }

            // Check minimum order amount for coins (₹100)
            if (serverSubtotal < 100) {
                await session.abortTransaction();
                return res.status(400).json({
                    message: 'Minimum order amount of ₹100 required to use coins.',
                    error: 'MINIMUM_ORDER_AMOUNT'
                });
            }

            // Check maximum coins per order (100)
            if (coinsToApply > 100) {
                await session.abortTransaction();
                return res.status(400).json({
                    message: 'Maximum 100 coins can be used per order.',
                    error: 'MAX_COINS_EXCEEDED'
                });
            }

            const now = new Date();
            const availableCoinBatches = user.coinsHistory
                .filter(coin => coin.type === 'EARNED' && coin.amount > 0 && (!coin.expiry || new Date(coin.expiry) > now))
                .sort((a, b) => new Date(a.expiry) - new Date(b.expiry));

            let calculatedAvailableCoins = availableCoinBatches.reduce((sum, batch) => sum + batch.amount, 0);

            console.log(`User ${user._id} wants to apply ${coinsToApply} coins. Server calculated available: ${calculatedAvailableCoins}`);

            if (calculatedAvailableCoins > 0) {
                // Enhanced coin application logic with profit protection
                // 1. Ensure minimum profit margin (at least 20% of original amount should remain)
                const minimumProfitAmount = Math.max(20, serverSubtotal * 0.2); // At least ₹20 or 20% of subtotal
                const maxDiscountFromCoins = Math.max(0, serverAmountAfterCouponDiscount - minimumProfitAmount);

                // 2. Apply dynamic coin limits based on order value
                let maxCoinsAllowed = 100; // Default maximum
                if (serverSubtotal >= 500) {
                    maxCoinsAllowed = Math.min(100, Math.floor(serverSubtotal * 0.5)); // Up to 50% of subtotal for large orders
                } else if (serverSubtotal >= 200) {
                    maxCoinsAllowed = Math.min(50, Math.floor(serverSubtotal * 0.4)); // Up to 40% for medium orders
                } else {
                    maxCoinsAllowed = Math.min(30, Math.floor(serverSubtotal * 0.3)); // Up to 30% for small orders
                }

                // 3. Calculate final applicable coins
                const applicableCoinsToUse = Math.min(
                    coinsToApply,
                    calculatedAvailableCoins,
                    maxDiscountFromCoins,
                    maxCoinsAllowed
                );

                if (applicableCoinsToUse > 0) {
                    actualCoinsUsed = applicableCoinsToUse;
                    actualCoinsDiscount = actualCoinsUsed; // 1 coin = 1 Rs discount

                    console.log(`Server applying ${actualCoinsUsed} coins as discount.`);
                    console.log(`Limits: Requested=${coinsToApply}, Available=${calculatedAvailableCoins}, MaxDiscount=${maxDiscountFromCoins}, MaxAllowed=${maxCoinsAllowed}`);

                    newCoinsHistoryEntries.push({
                        amount: -actualCoinsUsed,
                        type: 'USED',
                        date: now,
                        description: `Used for Order #TEMP_ORDER_ID`
                    });
                } else {
                    console.log(`No coins applied. Requested: ${coinsToApply}, Available: ${calculatedAvailableCoins}, MaxDiscount: ${maxDiscountFromCoins}, MaxAllowed: ${maxCoinsAllowed}`);
                }
            } else {
                console.log(`User ${user._id} has no available coins to apply based on server calculation.`);
            }
        }
        // --- End Coin Application Logic ---

        // Calculate final total amount: amount after coupons, minus actual coin discount. Ensure it's not negative.
        const finalTotalAmount = Math.max(0, serverAmountAfterCouponDiscount - actualCoinsDiscount);

        // Calculate coins earned based on the final amount paid (after all discounts)
        // Ensure coinsEarned is not negative if finalTotalAmount is 0
        const coinsEarned = finalTotalAmount > 0 ? Math.floor(finalTotalAmount / 100) * 10 : 0;


        // Log the coordinates for debugging
        console.log('Extracted coordinates before creating order:', extractedCoordinates);
        console.log('Processed delivery address:', typeof processedDeliveryAddress === 'object' ?
            JSON.stringify(processedDeliveryAddress) : processedDeliveryAddress);

        // If we have coordinates in the processed address but not in extractedCoordinates, use those
        if (!extractedCoordinates && typeof processedDeliveryAddress === 'object') {
            if (processedDeliveryAddress.coordinates &&
                processedDeliveryAddress.coordinates.latitude &&
                processedDeliveryAddress.coordinates.longitude) {
                extractedCoordinates = {
                    latitude: parseFloat(processedDeliveryAddress.coordinates.latitude),
                    longitude: parseFloat(processedDeliveryAddress.coordinates.longitude)
                };
                console.log('Using coordinates from processed address nested object:', extractedCoordinates);
            } else if (processedDeliveryAddress.latitude && processedDeliveryAddress.longitude) {
                extractedCoordinates = {
                    latitude: parseFloat(processedDeliveryAddress.latitude),
                    longitude: parseFloat(processedDeliveryAddress.longitude)
                };
                console.log('Using coordinates from processed address direct properties:', extractedCoordinates);
            }
        }

        // If we still don't have coordinates, try to find them from the user's address
        if (!extractedCoordinates) {
            try {
                console.log('No coordinates found, trying to get them from user address');
                const user = await User.findById(req.user._id);

                if (user && user.address) {
                    // Check for coordinates in the user's main address
                    if (user.address.coordinates &&
                        user.address.coordinates.latitude &&
                        user.address.coordinates.longitude) {
                        extractedCoordinates = {
                            latitude: parseFloat(user.address.coordinates.latitude),
                            longitude: parseFloat(user.address.coordinates.longitude)
                        };
                        console.log('Using coordinates from user main address nested object:', extractedCoordinates);
                    } else if (user.address.latitude && user.address.longitude) {
                        extractedCoordinates = {
                            latitude: parseFloat(user.address.latitude),
                            longitude: parseFloat(user.address.longitude)
                        };
                        console.log('Using coordinates from user main address direct properties:', extractedCoordinates);
                    }
                }

                // If still no coordinates, check the user's addresses array
                if (!extractedCoordinates && user.addresses && user.addresses.length > 0) {
                    // Try to find an address that matches the delivery address
                    let matchingAddress;

                    if (typeof processedDeliveryAddress === 'object' && processedDeliveryAddress.fullAddress) {
                        matchingAddress = user.addresses.find(addr => addr.fullAddress === processedDeliveryAddress.fullAddress);
                    } else if (typeof processedDeliveryAddress === 'string') {
                        matchingAddress = user.addresses.find(addr => addr.fullAddress === processedDeliveryAddress);
                    }

                    if (matchingAddress) {
                        if (matchingAddress.coordinates &&
                            matchingAddress.coordinates.latitude &&
                            matchingAddress.coordinates.longitude) {
                            extractedCoordinates = {
                                latitude: parseFloat(matchingAddress.coordinates.latitude),
                                longitude: parseFloat(matchingAddress.coordinates.longitude)
                            };
                            console.log('Using coordinates from matching address in addresses array:', extractedCoordinates);
                        } else if (matchingAddress.latitude && matchingAddress.longitude) {
                            extractedCoordinates = {
                                latitude: parseFloat(matchingAddress.latitude),
                                longitude: parseFloat(matchingAddress.longitude)
                            };
                            console.log('Using direct coordinates from matching address in addresses array:', extractedCoordinates);
                        }
                    }
                }
            } catch (error) {
                console.error('Error finding coordinates from user address:', error);
            }
        }

        // Set the final deliveryCoordinates to use in the order
        const finalDeliveryCoordinates = extractedCoordinates;

        // Ensure we have valid coordinates
        if (finalDeliveryCoordinates) {
            // Make sure coordinates are numbers, not strings
            if (typeof finalDeliveryCoordinates.latitude === 'string') {
                finalDeliveryCoordinates.latitude = parseFloat(finalDeliveryCoordinates.latitude);
            }
            if (typeof finalDeliveryCoordinates.longitude === 'string') {
                finalDeliveryCoordinates.longitude = parseFloat(finalDeliveryCoordinates.longitude);
            }

            // Validate coordinates
            if (isNaN(finalDeliveryCoordinates.latitude) || isNaN(finalDeliveryCoordinates.longitude)) {
                console.error('Invalid coordinates detected:', finalDeliveryCoordinates);
                finalDeliveryCoordinates = null;
            } else {
                console.log('Valid coordinates confirmed:', finalDeliveryCoordinates);
            }
        } else {
            console.log('No coordinates available for this order');
        }

        // Create new order instance
        const newOrder = new Order({
            userId: req.user._id,
            items: processedItems,
            totalAmount: finalTotalAmount,
            originalAmount: clientOriginalAmount, // This is the sum of item prices before any discounts/fees
            deliveryAddress: processedDeliveryAddress,
            deliveryCoordinates: finalDeliveryCoordinates, // Add delivery coordinates
            status: 'PLACED',
            orderNumber,
            paymentMethod,
            couponDiscount: couponDiscount || 0,
            coinsDiscount: actualCoinsDiscount, // Store the actual discount applied
            appliedCoupon: appliedCoupon || null,
            coinsEarned: coinsEarned, // Store coins earned based on final amount
            expectedDelivery: expectedDelivery ? new Date(expectedDelivery) : undefined,
            expectedDeliveryInfo: expectedDeliveryInfo || undefined // Store display info
        });

        // Log the final order data for debugging
        console.log('New order created with delivery coordinates:', newOrder.deliveryCoordinates);

        // Force set the deliveryCoordinates if they're not being saved properly
        if (finalDeliveryCoordinates && (!newOrder.deliveryCoordinates ||
            !newOrder.deliveryCoordinates.latitude ||
            !newOrder.deliveryCoordinates.longitude)) {
            console.log('Forcing deliveryCoordinates to be set on the order');
            newOrder.set('deliveryCoordinates', finalDeliveryCoordinates);
        }

        // Save the order within the transaction
        const savedOrder = await newOrder.save({ session });

        // Check if the coordinates were saved properly
        console.log('Saved order delivery coordinates:', savedOrder.deliveryCoordinates);

        // If coordinates weren't saved properly, try to update them directly
        if (finalDeliveryCoordinates && (!savedOrder.deliveryCoordinates ||
            !savedOrder.deliveryCoordinates.latitude ||
            !savedOrder.deliveryCoordinates.longitude)) {
            console.log('Coordinates not saved properly, attempting direct update');

            try {
                // Update the order directly
                const updatedOrder = await Order.findByIdAndUpdate(
                    savedOrder._id,
                    { deliveryCoordinates: finalDeliveryCoordinates },
                    { session, new: true }
                );

                // Log the updated order
                console.log('Order updated with coordinates:', updatedOrder.deliveryCoordinates);
            } catch (error) {
                console.error('Error updating order with coordinates:', error);
            }
        }

        // Now update the 'USED' coin history entries with the actual orderId and orderNumber
        newCoinsHistoryEntries.forEach(entry => {
            entry.orderId = savedOrder._id;
            entry.orderNumber = savedOrder.orderNumber || savedOrder._id.toString();
            entry.description = `Used for Order #${entry.orderNumber}`;
        });

        // Add order to user's order history and update coins
        if (user) {
            // Add 'EARNED' coin entry if any coins were earned
            if (coinsEarned > 0) {
                const expiryDate = new Date();
                expiryDate.setFullYear(expiryDate.getFullYear() + 1);
                newCoinsHistoryEntries.push({
                    amount: coinsEarned,
                    type: 'EARNED',
                    orderId: savedOrder._id,
                    orderNumber: savedOrder.orderNumber || savedOrder._id.toString(),
                    date: new Date(),
                    expiry: expiryDate,
                    description: `Earned from Order #${savedOrder.orderNumber || savedOrder._id}`
                });
            }

            // Prepare update operations for the user document
            const userUpdateOps = {
                $push: {
                    orderHistory: { orderId: savedOrder._id, date: new Date() },
                    coinsHistory: { $each: newCoinsHistoryEntries } // Add all new history entries
                },
                $inc: { coins: coinsEarned - actualCoinsUsed } // Adjust simple counter
            };

            // Add coupon usage tracking if coupon was applied
            if (appliedCoupon && couponDiscount > 0) {
                if (!userUpdateOps.$push.usedCoupons) {
                    userUpdateOps.$push.usedCoupons = [];
                }
                userUpdateOps.$push.usedCoupons = {
                    couponCode: appliedCoupon.code || appliedCoupon,
                    orderId: savedOrder._id,
                    orderNumber: savedOrder.orderNumber || savedOrder._id.toString(),
                    usedAt: new Date(),
                    discountAmount: couponDiscount
                };
            }

            // Update user within the transaction
            await User.findByIdAndUpdate(req.user._id, userUpdateOps, { session });

            console.log(`User ${req.user._id}: Earned ${coinsEarned}, Used ${actualCoinsUsed}. Net change: ${coinsEarned - actualCoinsUsed}`);
            console.log(`Updated user's coin history for order ${savedOrder._id}`);
        }

        // Commit the transaction
        await session.commitTransaction();
        session.endSession();

        // Get the io instance from the request
        const io = req.app.get('io');

        // Emit real-time update to admin room
        if (io) {
            io.to('admin_room').emit('new_order', savedOrder);
        }

        // Send push notifications
        try {
            const { sendNewOrderNotification } = require('../utils/pushNotificationUtils');
            const User = require('../models/User');
            const DeliveryPartner = require('../models/DeliveryPartner');

            // Get admin users to send notifications
            const adminUsers = await User.find({ userType: 'ADMIN' }).select('expoPushToken pushToken name');

            // Send notification to all admin users
            for (const admin of adminUsers) {
                const pushToken = admin.expoPushToken || admin.pushToken;
                if (pushToken) {
                    await sendNewOrderNotification(
                        pushToken,
                        savedOrder.orderNumber,
                        req.user.name || 'Customer',
                        savedOrder.totalAmount,
                        'ADMIN'
                    );
                }
            }

            // Get all delivery partners to send notifications
            const deliveryPartners = await DeliveryPartner.find({ isActive: true }).select('expoPushToken pushToken name');

            // Send notification to all delivery partners
            for (const partner of deliveryPartners) {
                const pushToken = partner.expoPushToken || partner.pushToken;
                if (pushToken) {
                    await sendNewOrderNotification(
                        pushToken,
                        savedOrder.orderNumber,
                        req.user.name || 'Customer',
                        savedOrder.totalAmount,
                        'DELIVERY_PARTNER'
                    );
                }
            }
        } catch (notificationError) {
            console.error('Error sending new order push notifications:', notificationError);
            // Don't fail the order creation if notifications fail
        }

        // We'll send the invoice email when the order is delivered, not at creation time
        console.log(`Order ${savedOrder.orderId} created successfully. Invoice will be sent when order is delivered.`);

        res.status(201).json(savedOrder);

    } catch (error) {
        // If an error occurred, abort the transaction
        await session.abortTransaction();
        session.endSession();
        console.error('Error creating order:', error);
        res.status(500).json({ message: 'Server error during order creation' });
    }
};

/**
 * Update order status
 * @route PUT /api/orders/:id/status
 * @access Private
 */
const updateOrderStatus = async (req, res) => {
    // Start a Mongoose session for transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const { status } = req.body;

        if (!status) {
            await session.abortTransaction();
            session.endSession();
            return res.status(400).json({ message: 'Status is required' });
        }

        // Validate status
        const validStatuses = ['PLACED', 'CONFIRMED', 'PREPARING', 'OUT_FOR_DELIVERY', 'DELIVERED', 'CANCELLED'];
        if (!validStatuses.includes(status)) {
            await session.abortTransaction();
            session.endSession();
            return res.status(400).json({ message: 'Invalid status' });
        }

        // Find order within the session
        const order = await Order.findById(req.params.id).session(session);

        if (!order) {
            await session.abortTransaction();
            session.endSession();
            return res.status(404).json({ message: 'Order not found' });
        }

        // Check permissions based on user type and status change
        if (req.userType === 'USER') {
            // Users can only cancel their own orders
            if (status !== 'CANCELLED') {
                await session.abortTransaction();
                session.endSession();
                return res.status(403).json({ message: 'Not authorized to update order status' });
            }

            if (order.userId.toString() !== req.user._id.toString()) {
                await session.abortTransaction();
                session.endSession();
                return res.status(403).json({ message: 'Not authorized to update this order' });
            }

            // Cannot cancel if already delivered or out for delivery
            if (order.status === 'DELIVERED' || order.status === 'OUT_FOR_DELIVERY') {
                await session.abortTransaction();
                session.endSession();
                return res.status(400).json({ message: 'Cannot cancel order that is already out for delivery or delivered' });
            }
        } else if (req.userType === 'DELIVERY_PARTNER') {
            // Delivery partners can only update to OUT_FOR_DELIVERY or DELIVERED
            if (status !== 'OUT_FOR_DELIVERY' && status !== 'DELIVERED') {
                await session.abortTransaction();
                session.endSession();
                return res.status(403).json({ message: 'Not authorized to update to this status' });
            }

            // Check if delivery partner is assigned to this order
            if (!order.deliveryPartner || order.deliveryPartner.toString() !== req.user._id.toString()) {
                await session.abortTransaction();
                session.endSession();
                return res.status(403).json({ message: 'Not authorized to update this order' });
            }
        }
        // Admins can update to any status (no transaction abort needed here)

        // Update order status
        order.status = status;

        // Record status change timestamps
        if (status === 'OUT_FOR_DELIVERY') {
            order.deliveryStartedAt = new Date();
        }

        // If delivered, set delivery date
        if (status === 'DELIVERED') {
            order.deliveredAt = new Date();

            // Update delivery partner stats if applicable (within transaction)
            if (order.deliveryPartner) {
                await DeliveryPartner.findByIdAndUpdate(order.deliveryPartner, {
                    $inc: {
                        totalDeliveries: 1,
                        completedOrders: 1,
                        earnings: Math.ceil(order.totalAmount * 0.1) // 10% of order amount as earnings
                    }
                }, { session });
            }
        }

        // If cancelled, handle coin refund and update delivery partner stats
        if (status === 'CANCELLED') {
            // Update delivery partner stats if applicable (within transaction)
            if (order.deliveryPartner) {
                await DeliveryPartner.findByIdAndUpdate(order.deliveryPartner, {
                    $inc: { cancelledOrders: 1 }
                }, { session });
            }

            // Handle coin adjustments for cancelled orders
            const user = await User.findById(order.userId).session(session);
            if (user) {
                const coinHistoryEntries = [];
                let coinBalanceAdjustment = 0;

                // 1. Remove/Refund earned coins from this order (if any)
                if (order.coinsEarned > 0) {
                    console.log(`Order ${order._id} cancelled - removing ${order.coinsEarned} earned coins from user ${order.userId}`);

                    // Find the EARNED entry for this order in the user's coin history
                    const earnedEntry = user.coinsHistory.find(
                        entry => entry.type === 'EARNED' &&
                        entry.orderId &&
                        entry.orderId.toString() === order._id.toString()
                    );

                    const coinsToRemove = earnedEntry ? earnedEntry.amount : order.coinsEarned;

                    if (coinsToRemove > 0) {
                        // Create coin history entry for removing earned coins
                        coinHistoryEntries.push({
                            amount: -coinsToRemove, // Negative amount to indicate removal
                            type: 'REFUNDED',
                            orderId: order._id,
                            orderNumber: order.orderNumber || order._id.toString(),
                            date: new Date(),
                            description: `Removed earned coins due to cancellation of Order #${order.orderNumber || order._id}`
                        });

                        coinBalanceAdjustment -= coinsToRemove;
                        console.log(`Will remove ${coinsToRemove} earned coins from user ${order.userId}`);
                    }
                }

                // 2. Refund used coins (if any were used for discount)
                if (order.coinsDiscount > 0) {
                    console.log(`Order ${order._id} had ${order.coinsDiscount} coins used for discount - refunding these coins`);

                    // Find the USED entry for this order in the user's coin history
                    const usedEntry = user.coinsHistory.find(
                        entry => entry.type === 'USED' &&
                        entry.orderId &&
                        entry.orderId.toString() === order._id.toString()
                    );

                    if (usedEntry) {
                        // Create a history entry to refund the used coins
                        coinHistoryEntries.push({
                            amount: order.coinsDiscount, // Positive amount to refund
                            type: 'REFUNDED',
                            orderId: order._id,
                            orderNumber: order.orderNumber || order._id.toString(),
                            date: new Date(),
                            description: `Refunded used coins due to cancellation of Order #${order.orderNumber || order._id}`
                        });

                        // Add the used coins back to the balance
                        coinBalanceAdjustment += order.coinsDiscount;
                        console.log(`Refunding used coins (${order.coinsDiscount}) back to user balance`);
                    }
                }

                // Calculate new coin balance
                const newCoinBalance = user.coins + coinBalanceAdjustment;

                // Update user document within the transaction
                if (coinHistoryEntries.length > 0) {
                    await User.findByIdAndUpdate(order.userId, {
                        $set: { coins: newCoinBalance },
                        $push: { coinsHistory: { $each: coinHistoryEntries } }
                    }, { session });

                    console.log(`Updated user ${order.userId} coin balance from ${user.coins} to ${newCoinBalance} due to order cancellation`);
                    console.log(`Added ${coinHistoryEntries.length} coin history entries for cancelled order ${order._id}`);
                }
            }
            // NOTE: As per new requirement, both earned and used coins are handled during cancellation.
        }

        // Save the updated order within the transaction
        const updatedOrder = await order.save({ session });

        // Commit the transaction before syncing and emitting events
        await session.commitTransaction();
        session.endSession();

        // Sync the order status with the delivery partner's orders array if a delivery partner is assigned
        // This happens outside the transaction as it might involve external calls or complex logic
        if (updatedOrder.deliveryPartner) {
            try {
                await syncOrderWithDeliveryPartner(updatedOrder._id, updatedOrder.deliveryPartner);
            } catch (syncError) {
                console.error(`Error syncing order ${updatedOrder._id} with delivery partner ${updatedOrder.deliveryPartner}:`, syncError);
                // Continue even if sync fails, as the main status update succeeded
            }
        }

        // Send invoice email if order is delivered
        if (status === 'DELIVERED') {
            try {
                // Get the user associated with this order
                const order = await Order.findById(updatedOrder._id).populate('userId', 'name number email');

                if (order && order.userId && order.userId.email) {
                    console.log(`Sending invoice email for delivered order ${order.orderId} to ${order.userId.email}`);

                    // Process invoice asynchronously - don't wait for it to complete
                    processOrderInvoice(order._id)
                        .then(result => {
                            if (result.success) {
                                console.log(`Invoice email sent successfully for order ${order.orderId}`);
                            } else {
                                console.error(`Failed to send invoice email for order ${order.orderId}: ${result.error}`);
                            }
                        })
                        .catch(error => {
                            console.error(`Error sending invoice email for order ${order.orderId}:`, error);
                        });
                } else {
                    console.log(`User for order ${updatedOrder._id} does not have an email address. Skipping invoice email.`);
                }
            } catch (emailError) {
                // Log error but don't fail the order status update
                console.error('Error sending invoice email:', emailError);
            }
        }

        // Get the io instance from the request
        const io = req.app.get('io');

        // Emit real-time update
        if (io) {
            // Notify admin room
            io.to('admin_room').emit('order_status_changed', {
                orderId: updatedOrder._id,
                status: updatedOrder.status
            });

            // Notify user
            io.to(`USER_${updatedOrder.userId}`).emit('order_status_changed', {
                orderId: updatedOrder._id,
                status: updatedOrder.status
            });

            // Notify delivery partner if assigned
            if (updatedOrder.deliveryPartner) {
                io.to(`DELIVERY_PARTNER_${updatedOrder.deliveryPartner}`).emit('order_status_changed', {
                    orderId: updatedOrder._id,
                    status: updatedOrder.status
                });
            }
        }

        // Send push notifications for specific status changes
        try {
            const { sendOrderStatusNotification } = require('../utils/pushNotificationUtils');
            const User = require('../models/User');

            // Send notifications for OUT_FOR_DELIVERY and DELIVERED status to users
            if (status === 'OUT_FOR_DELIVERY' || status === 'DELIVERED') {
                // Notify only the customer
                const customer = await User.findById(updatedOrder.userId).select('expoPushToken pushToken name');
                if (customer) {
                    const pushToken = customer.expoPushToken || customer.pushToken;
                    if (pushToken) {
                        console.log(`Sending ${status} notification to user:`, customer.name);
                        await sendOrderStatusNotification(
                            pushToken,
                            updatedOrder.orderNumber,
                            status,
                            'USER'
                        );
                    } else {
                        console.log('No push token found for user:', customer.name);
                    }
                } else {
                    console.log('Customer not found for order:', updatedOrder.orderNumber);
                }
            }
        } catch (notificationError) {
            console.error('Error sending order status push notifications:', notificationError);
            // Don't fail the status update if notifications fail
        }

        res.status(200).json(updatedOrder);

    } catch (error) {
        // If an error occurred, abort the transaction
        await session.abortTransaction();
        session.endSession();
        console.error('Error updating order status:', error);
        res.status(500).json({ message: 'Server error during status update' });
    }
};

/**
 * Assign delivery partner to order
 * @route PUT /api/orders/:id/assign
 * @access Private (Admin only)
 */
const assignDeliveryPartner = async (req, res) => {
    try {
        const { partnerId } = req.body;

        if (!partnerId) {
            return res.status(400).json({ message: 'Delivery partner ID is required' });
        }

        // Check if delivery partner exists
        const deliveryPartner = await DeliveryPartner.findById(partnerId);
        if (!deliveryPartner) {
            return res.status(404).json({ message: 'Delivery partner not found' });
        }

        // Check if delivery partner is available
        if (!deliveryPartner.isActive || !deliveryPartner.isAvailable) {
            return res.status(400).json({ message: 'Delivery partner is not available' });
        }

        const order = await Order.findById(req.params.id);

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Update order with delivery partner and assignment details
        order.deliveryPartner = partnerId;
        order.deliveryPartnerAssignedAt = new Date();
        order.deliveryPartnerAssignedBy = req.user._id; // Admin who assigned the delivery partner

        // If order is in PLACED status, update to CONFIRMED
        if (order.status === 'PLACED') {
            order.status = 'CONFIRMED';
        }

        const updatedOrder = await order.save();

        // Sync the order with the delivery partner's orders array
        await syncOrderWithDeliveryPartner(updatedOrder._id, partnerId);

        // Get the io instance from the request
        const io = req.app.get('io');

        // Emit real-time update
        if (io) {
            // Notify delivery partner
            io.to(`DELIVERY_PARTNER_${partnerId}`).emit('new_delivery_assignment', {
                orderId: updatedOrder._id,
                orderDetails: updatedOrder
            });

            // Notify user
            io.to(`USER_${updatedOrder.userId}`).emit('order_update', {
                type: 'delivery_assigned',
                order: updatedOrder
            });
        }

        res.status(200).json(updatedOrder);
    } catch (error) {
        console.error('Error assigning delivery partner:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

module.exports = {
    getAllOrders,
    getOrderById,
    createOrder,
    updateOrderStatus,
    assignDeliveryPartner
};
