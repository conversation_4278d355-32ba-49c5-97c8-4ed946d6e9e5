{"name": "my-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:production": "eas build --platform android --profile production", "build:preview": "eas build --platform android --profile preview", "build:ios": "eas build --platform ios --profile production", "validate-config": "node scripts/validateConfig.js"}, "dependencies": {"@expo/config-plugins": "~9.0.0", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/geolocation": "^3.4.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.1.1", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo": "~52.0.46", "expo-constants": "~17.0.8", "expo-device": "~7.0.3", "expo-image-picker": "^16.0.6", "expo-linear-gradient": "~14.0.2", "expo-location": "~18.0.10", "expo-notifications": "~0.29.14", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "firebase": "^11.8.1", "lottie-react-native": "7.1.0", "metro": "^0.81.5", "metro-resolver": "^0.81.5", "nativewind": "^2.0.11", "react": "18.3.1", "react-native": "0.76.9", "react-native-chart-kit": "^6.12.0", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "~1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-maps": "1.18.0", "react-native-permissions": "^5.2.5", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-toast-message": "^2.2.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.3.12", "metro-config": "^0.81.5", "tailwindcss": "^3.3.2", "typescript": "^5.3.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["firebase", "react-native-elements", "socket.io-client", "uuid", "metro", "metro-resolver", "react-native-chart-kit"], "listUnknownPackages": false}}}, "private": true}