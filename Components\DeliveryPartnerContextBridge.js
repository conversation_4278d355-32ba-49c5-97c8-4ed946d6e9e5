import React, { useEffect } from 'react';
import { useDeliveryPartner } from '../context/DeliveryPartnerContext';

/**
 * This component creates a bridge between the global refresh function and the DeliveryPartnerContext
 * It should be included once in the app, typically near the root
 */
const DeliveryPartnerContextBridge = () => {
    // Use try-catch to handle potential context errors
    try {
        const { refreshUserData } = useDeliveryPartner();

        useEffect(() => {
            // Set up the global refresh function to call the context's refresh function
            if (typeof global !== 'undefined') {
                global.refreshDeliveryPartnerContext = () => {
                    console.log('DeliveryPartnerContextBridge: Refreshing delivery partner context');
                    if (refreshUserData) {
                        refreshUserData();
                    } else {
                        console.warn('DeliveryPartnerContextBridge: refreshUserData is not available');
                    }
                };
            }

            // Clean up on unmount
            return () => {
                if (typeof global !== 'undefined') {
                    global.refreshDeliveryPartnerContext = undefined;
                }
            };
        }, [refreshUserData]);
    } catch (error) {
        console.error('DeliveryPartnerContextBridge: Error initializing bridge:', error);
    }

    // This component doesn't render anything
    return null;
};

export default DeliveryPartnerContextBridge;
