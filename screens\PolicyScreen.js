import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StatusBar } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";

const PolicyScreen = ({ navigation, route }) => {
    const { type } = route.params;

    const privacyPolicyContent = `Meat Now Privacy Policy

This privacy policy applies to the Meat Now app (hereby referred to as "Application") for mobile devices that was created by <PERSON><PERSON> (hereby referred to as "Service Provider") as a Free service. This service is intended for use "AS IS".

Information Collection and Use
The Application collects information when you download and use it. This information may include information such as:

• Your device's Internet Protocol address (e.g. IP address)
• The pages of the Application that you visit, the time and date of your visit, the time spent on those pages
• The time spent on the Application
• The operating system you use on your mobile device

The Application collects your device's location, which helps the Service Provider determine your approximate geographical location and make use of in below ways:

Geolocation Services: The Service Provider utilizes location data to provide features such as personalized content, relevant recommendations, and location-based services.

Analytics and Improvements: Aggregated and anonymized location data helps the Service Provider to analyze user behavior, identify trends, and improve the overall performance and functionality of the Application.

Third-Party Services: Periodically, the Service Provider may transmit anonymized location data to external services. These services assist them in enhancing the Application and optimizing their offerings.

The Service Provider may use the information you provided to contact you from time to time to provide you with important information, required notices and marketing promotions.

For a better experience, while using the Application, the Service Provider may require you to provide us with certain personally identifiable information, including but not <NAME_EMAIL>, Dinesh, 21, Male. The information that the Service Provider request will be retained by them and used as described in this privacy policy.

Third Party Access
Only aggregated, anonymized data is periodically transmitted to external services to aid the Service Provider in improving the Application and their service. The Service Provider may share your information with third parties in the ways that are described in this privacy statement.

Please note that the Application utilizes third-party services that have their own Privacy Policy about handling data. Below are the links to the Privacy Policy of the third-party service providers used by the Application:

• Google Play Services

The Service Provider may disclose User Provided and Automatically Collected Information:
• as required by law, such as to comply with a subpoena, or similar legal process;
• when they believe in good faith that disclosure is necessary to protect their rights, protect your safety or the safety of others, investigate fraud, or respond to a government request;
• with their trusted services providers who work on their behalf, do not have an independent use of the information we disclose to them, and have agreed to adhere to the rules set forth in this privacy statement.

Opt-Out Rights
You can stop all collection of information by the Application easily by uninstalling it. You may use the standard uninstall processes as may be available as part of your mobile device or via the mobile application marketplace or network.

Data Retention Policy
The Service Provider will retain User Provided data for as long as you use the Application and for a reasonable time thereafter. If you'd like them to delete User Provided Data that you have provided via the Application, please contact <NAME_EMAIL> and they will respond in a reasonable time.

Children
The Service Provider does not use the Application to knowingly solicit data from or market to children under the age of 13.

The Application does not address anyone under the age of 13. The Service Provider does not knowingly collect personally identifiable information from children under 13 years of age. In the case the Service Provider discover that a child under 13 has provided personal information, the Service Provider will immediately delete this from their servers. If you are a parent or guardian and you are aware that your child has provided us with personal information, please contact the Service Provider (<EMAIL>) so that they will be able to take the necessary actions.

Security
The Service Provider is concerned about safeguarding the confidentiality of your information. The Service Provider provides physical, electronic, and procedural safeguards to protect information the Service Provider processes and maintains.

Changes
This Privacy Policy may be updated from time to time for any reason. The Service Provider will notify you of any changes to the Privacy Policy by updating this page with the new Privacy Policy. You are advised to consult this Privacy Policy regularly for any changes, as continued use is deemed approval of all changes.

This privacy policy is effective as of 2025-05-20

Your Consent
By using the Application, you are consenting to the processing of your information as set forth in this Privacy Policy now and as amended by us.

Contact Us
If you have any questions regarding privacy while using the Application, or have questions about the practices, please contact the Service Provider via <NAME_EMAIL>.

This privacy policy page was generated by App Privacy Policy Generator`;

    const termsAndConditionsContent = `Terms and Conditions – Meat Now

Welcome to Meat Now! These terms and conditions ("Terms") govern your use of the Meat Now mobile application ("Service") operated by the Meat Now Team ("we", "us", or "our").

By accessing or using our Service, you agree to be bound by these Terms. If you disagree with any part of these terms, please refrain from using the Service.

1. ACCEPTANCE OF TERMS
By downloading, installing, or using the Meat Now app, you acknowledge that you have read, understood, and agree to be bound by these Terms and our Privacy Policy.

2. DESCRIPTION OF SERVICE
Meat Now is a food delivery application that enables users to:
• Browse and order fresh meat and related products
• Schedule delivery slots
• Make payments using multiple payment methods
• Track order status and delivery
• Manage user profiles and preferences

3. USER ACCOUNTS
• You must provide accurate and complete information when creating an account
• You are responsible for maintaining the confidentiality of your account credentials
• Only one account per phone number is permitted

4. ORDERING AND DELIVERY
• All orders are subject to availability and confirmation
• Deliveries are available only in designated service areas
• Delivery fees may apply based on order value and location
• We reserve the right to accept, refuse, or cancel orders at our discretion

5. PAYMENT TERMS
• Payments can be made via Cash on Delivery (COD) or UPI
• All prices listed are inclusive of applicable taxes
• Offers and promotions are subject to their own terms
• Refunds are processed in accordance with our refund policy

6. CANCELLATION AND REFUND POLICY
• Orders can be cancelled only before preparation begins
• Cancellations after preparation may not be eligible for a refund
• Refunds for eligible cancellations will be processed within 5–7 business days
• If products are delivered damaged or incorrect, replacements or refunds will be provided

7. USER CONDUCT
You agree not to:
• Use the Service for any illegal or unauthorized purpose
• Provide false, misleading, or outdated information
• Disrupt or interfere with the Service or servers
• Attempt unauthorized access to our systems

8. INTELLECTUAL PROPERTY
• All trademarks, logos, content, and intellectual property belong to the Meat Now team
• You may not reproduce, distribute, or create derivative works without explicit permission
• User-generated content remains your property, but by submitting it, you grant us a license to use it

9. LIMITATION OF LIABILITY
• The Service is provided "as is" without warranties of any kind
• We are not responsible for indirect, incidental, or consequential damages
• Our maximum liability is limited to the amount paid for the disputed order

10. PRIVACY AND DATA PROTECTION
• We are committed to protecting your privacy
• Your data is collected and used in accordance with our Privacy Policy
• We implement reasonable safeguards to protect your personal data

11. MODIFICATIONS TO TERMS
• We reserve the right to change these Terms at any time
• Any updates will take effect immediately upon posting
• Continued use of the Service constitutes your acceptance of the revised Terms

12. TERMINATION
• We may suspend or terminate your account if you violate these Terms
• If you wish to delete your account, you must submit a request using the Account Deletion Request Form
• Upon verifying your request, your account will be deleted, and you will receive a confirmation
• Termination does not affect any obligations related to previously placed orders

13. GOVERNING LAW
These Terms are governed by the laws of India. Any disputes will be subject to the jurisdiction of the courts of Tamil Nadu.

14. CONTACT INFORMATION
For questions, concerns, or feedback regarding these Terms, please contact us:
📞 Customer Support: Available via the app support section

15. SEVERABILITY
If any part of these Terms is deemed unenforceable, the remaining sections shall remain valid and in effect.

By using Meat Now, you acknowledge that you have read, understood, and agreed to all of the above Terms and Conditions.`;

    const content = type === 'privacy' ? privacyPolicyContent : termsAndConditionsContent;
    const title = type === 'privacy' ? 'Privacy Policy' : 'Terms and Conditions';

    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" />

            {/* Header with radius */}
            <View className="bg-madder pt-12 pb-6 px-4 rounded-b-3xl">
                <View className="flex-row items-center">
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        className="mr-4 p-2 -ml-2"
                    >
                        <MaterialIcons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold flex-1">{title}</Text>
                </View>
            </View>

            {/* Content */}
            <ScrollView
                className="flex-1 px-4 py-6"
                showsVerticalScrollIndicator={false}
            >
                <View className="bg-white rounded-xl p-6 shadow-sm">
                    <Text className="text-gray-800 text-base leading-7 text-justify">
                        {content}
                    </Text>
                </View>

                {/* Bottom spacing */}
                <View className="h-6" />
            </ScrollView>
        </View>
    );
};

export default PolicyScreen;
