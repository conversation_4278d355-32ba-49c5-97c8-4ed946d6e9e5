const { withAndroidManifest, withAndroidColors, withAndroidStrings } = require('@expo/config-plugins');

const withNotificationPermissions = (config) => {
  // Add Android manifest permissions
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;

    // Ensure POST_NOTIFICATIONS permission is added
    const permissions = androidManifest.manifest['uses-permission'] || [];

    const hasPostNotifications = permissions.some(
      (permission) => permission.$['android:name'] === 'android.permission.POST_NOTIFICATIONS'
    );

    if (!hasPostNotifications) {
      permissions.push({
        $: {
          'android:name': 'android.permission.POST_NOTIFICATIONS',
        },
      });
    }

    // Ensure other required permissions
    const requiredPermissions = [
      'android.permission.VIBRATE',
      'android.permission.RECEIVE_BOOT_COMPLETED',
      'android.permission.WAKE_LOCK',
      'android.permission.INTERNET'
    ];

    requiredPermissions.forEach((permissionName) => {
      const hasPermission = permissions.some(
        (permission) => permission.$['android:name'] === permissionName
      );

      if (!hasPermission) {
        permissions.push({
          $: {
            'android:name': permissionName,
          },
        });
      }
    });

    androidManifest.manifest['uses-permission'] = permissions;

    // Ensure notification metadata is present
    const application = androidManifest.manifest.application[0];
    const metaData = application['meta-data'] || [];

    // Add notification channel metadata
    const hasChannelMeta = metaData.some(
      (meta) => meta.$['android:name'] === 'com.google.firebase.messaging.default_notification_channel_id'
    );

    if (!hasChannelMeta) {
      metaData.push({
        $: {
          'android:name': 'com.google.firebase.messaging.default_notification_channel_id',
          'android:value': 'default'
        }
      });
    }

    application['meta-data'] = metaData;

    return config;
  });

  // Add notification colors
  config = withAndroidColors(config, (config) => {
    config.modResults.resources.color = config.modResults.resources.color || [];

    const hasNotificationColor = config.modResults.resources.color.some(
      (color) => color.$.name === 'notification_icon_color'
    );

    if (!hasNotificationColor) {
      config.modResults.resources.color.push({
        $: { name: 'notification_icon_color' },
        _: '#A31621'
      });
    }

    return config;
  });

  return config;
};

module.exports = withNotificationPermissions;
