import React, { useState, useCallback } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, Alert, Image, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useUser } from '../context/UserContext';
import { getUserCoins } from '../utils/api/userProfileApi';

const CoinsScreen = () => {
    const navigation = useNavigation();
    const { currentUser } = useUser();

    // State for coin history and loading
    const [coinHistory, setCoinHistory] = useState([]);
    const [loading, setLoading] = useState(true);
    const [totalCoins, setTotalCoins] = useState(0);
    const [usedCoins, setUsedCoins] = useState(0);

    // Fetch coin data directly from API when screen is focused
    useFocusEffect(
        useCallback(() => {
            const fetchData = async () => {
                try {
                    setLoading(true);
                    console.log('Fetching coin data for coins screen...');

                    // Fetch coin data directly from API with cache-busting timestamp
                    const timestamp = new Date().getTime();
                    const coinData = await getUserCoins(timestamp);

                    // Set coin totals from API response
                    setTotalCoins(coinData.totalCoins || 0);
                    setUsedCoins(coinData.usedCoins || 0);

                    // Process coin history from API
                    if (coinData.coinsHistory && Array.isArray(coinData.coinsHistory)) {
                        console.log('Raw coin history from API:', coinData.coinsHistory);

                        // Map the API coin history to our UI format
                        const history = coinData.coinsHistory.map(coin => {
                            // Determine the type based on the transaction type and amount
                            let type = 'earned';
                            if (coin.type === 'USED') type = 'redeemed';
                            if (coin.type === 'EXPIRED') type = 'expired';
                            if (coin.type === 'REFUNDED') {
                                // Distinguish between refunded used coins vs removed earned coins
                                if (coin.amount > 0 || (coin.description && coin.description.includes('Refunded used coins'))) {
                                    type = 'refunded'; // Used coins being refunded (positive)
                                } else {
                                    type = 'removed'; // Earned coins being removed (negative)
                                }
                            }

                            console.log(`Processing coin: ${coin.type} -> ${type}, amount: ${coin.amount}`);

                            return {
                                id: `${type}-${coin._id || coin.orderId || Math.random().toString(36).substring(7)}`,
                                type: type,
                                amount: Math.abs(coin.amount), // Use absolute value for display
                                originalAmount: coin.amount, // Keep original amount for sign determination
                                description: coin.description || `${type.charAt(0).toUpperCase() + type.slice(1)} coins`,
                                date: coin.date || new Date().toISOString(),
                                expiry: coin.expiry,
                                orderNumber: coin.orderNumber,
                                orderId: coin.orderId
                            };
                        }).sort((a, b) => new Date(b.date) - new Date(a.date)); // Sort by date, newest first

                        setCoinHistory(history);
                        console.log('Coin history processed from API:', history.length, 'entries');
                        console.log('Processed history:', history);
                    } else {
                        console.log('No coin history available from API');
                        setCoinHistory([]);
                    }

                    console.log('Coin data fetched successfully');
                } catch (error) {
                    console.error('Error fetching data for coins screen:', error);
                    // Set default values on error
                    setTotalCoins(currentUser?.coins || 0);
                    setUsedCoins(currentUser?.usedCoins || 0);
                    setCoinHistory([]);
                } finally {
                    setLoading(false);
                }
            };

            fetchData();

            return () => {
                // Cleanup function when screen loses focus
            };
        }, [])
    );

    // We're already fetching coin data in useFocusEffect, so we don't need a separate useEffect

    const renderCoinHistoryItem = ({ item }) => {
        // Determine icon and colors based on transaction type
        let iconName = 'add-circle';
        let iconColor = '#16A34A';
        let bgColor = 'bg-green-100';
        let textColor = 'text-green-600';
        let statusText = 'Available';
        let statusBgColor = 'bg-green-100';
        let statusTextColor = 'text-green-800';

        if (item.type === 'redeemed') {
            iconName = 'remove-circle';
            iconColor = '#DC2626';
            bgColor = 'bg-red-100';
            textColor = 'text-red-600';
            statusText = 'Used';
            statusBgColor = 'bg-red-100';
            statusTextColor = 'text-red-800';
        } else if (item.type === 'refunded') {
            iconName = 'replay';
            iconColor = '#9333EA'; // Purple for refunded used coins
            bgColor = 'bg-purple-100';
            textColor = 'text-purple-600';
            statusText = 'Refunded';
            statusBgColor = 'bg-purple-100';
            statusTextColor = 'text-purple-800';
        } else if (item.type === 'removed') {
            iconName = 'remove-circle';
            iconColor = '#DC2626'; // Red for removed earned coins
            bgColor = 'bg-red-100';
            textColor = 'text-red-600';
            statusText = 'Removed';
            statusBgColor = 'bg-red-100';
            statusTextColor = 'text-red-800';
        } else if (item.type === 'expired') {
            iconName = 'timer-off';
            iconColor = '#9CA3AF';
            bgColor = 'bg-gray-100';
            textColor = 'text-gray-600';
            statusText = 'Expired';
            statusBgColor = 'bg-gray-100';
            statusTextColor = 'text-gray-800';
        }

        // Format date
        const formattedDate = new Date(item.date).toLocaleDateString();

        return (
            <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                <View className="flex-row justify-between items-center">
                    <View className="flex-row items-center">
                        <View className={`w-10 h-10 rounded-full ${bgColor} items-center justify-center mr-3`}>
                            <MaterialIcons
                                name={iconName}
                                size={20}
                                color={iconColor}
                            />
                        </View>
                        <View>
                            <Text className="text-gray-800 font-bold">
                                {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                            </Text>
                            <Text className="text-gray-500 text-xs">{formattedDate}</Text>
                        </View>
                    </View>

                    <View>
                        <Text className={`text-lg font-bold ${textColor}`}>
                            {item.type === 'earned' ? '+' :
                             item.type === 'refunded' ? '+' :
                             item.type === 'removed' ? '-' : '-'}{item.amount}
                        </Text>
                    </View>
                </View>

                <Text className="text-gray-600 mt-2 ml-13">{item.description}</Text>

                {/* Show status badges */}
                <View className="flex-row mt-2 ml-13">
                    {/* Status badge */}
                    <View className={`${statusBgColor} px-2 py-0.5 rounded-full mr-2`}>
                        <Text className={`${statusTextColor} text-xs font-medium`}>{statusText}</Text>
                    </View>

                    {/* Expiry date for earned coins */}
                    {item.type === 'earned' && item.expiry && (
                        <View className="bg-amber-50 px-2 py-0.5 rounded-full mr-2">
                            <Text className="text-amber-800 text-xs">
                                Expires: {new Date(item.expiry).toLocaleDateString()}
                            </Text>
                        </View>
                    )}

                    {/* Order number if available */}
                    {item.orderNumber && (
                        <View className="bg-gray-100 px-2 py-0.5 rounded-full">
                            <Text className="text-gray-600 text-xs">Order #{item.orderNumber}</Text>
                        </View>
                    )}
                </View>
            </View>
        );
    };

    return (
        <View className="flex-1 bg-gray-50">
            <View className="bg-madder p-6 pt-16 rounded-b-3xl pb-6 flex-row items-center">
                <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                    <MaterialIcons name="arrow-back" size={24} color="white" />
                </TouchableOpacity>
                <Text className="text-xl text-white font-bold">My Fresh Coins</Text>
            </View>

            <ScrollView
                className="flex-1 p-4"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 20 }}
            >
                {loading ? (
                    // Loading state
                    <View className="flex-1 items-center justify-center">
                        <ActivityIndicator size="large" color="#A31621" />
                        <Text className="text-gray-600 mt-4">Loading your coins...</Text>
                    </View>
                ) : (
                    <>
                        {/* Banner Image */}
                        <View
                            className="rounded-xl overflow-hidden mb-4"
                            style={{
                                shadowColor: '#000',
                                shadowOffset: { width: 2, height: 2 },
                                shadowOpacity: 0.1,
                                shadowRadius: 3,
                                elevation: 3,
                                backgroundColor: 'white'
                            }}
                        >
                            <Image
                                source={require('../assets/banner7.png')}
                                style={{
                                    width: '100%',
                                    height: undefined,
                                    aspectRatio: 1094/157
                                }}
                                resizeMode="contain"
                            />
                        </View>

                        {/* Coin Balance Card */}
                        <View className="bg-white rounded-xl p-6 mb-6 shadow-sm items-center">
                            <View className="w-20 h-20 rounded-full bg-madder/10 items-center justify-center mb-3">
                                <MaterialIcons name="monetization-on" size={40} color="#A31621" />
                            </View>

                            {/* Total Coins Balance - Should be Available + Used */}
                            <Text className="text-3xl font-bold text-gray-800 mb-1">{Math.max(0, totalCoins) + usedCoins}</Text>
                            <Text className="text-gray-500">Total Coin Balance</Text>

                            {/* Coin Breakdown */}
                            <View className="flex-row justify-between w-full mt-4">
                                <View className="items-center flex-1">
                                    <Text className="text-xl font-bold text-green-600">{Math.max(0, totalCoins)}</Text>
                                    <Text className="text-gray-500 text-xs">Available to Use</Text>
                                </View>

                                <View className="items-center flex-1">
                                    <Text className="text-xl font-bold text-amber-600">{usedCoins}</Text>
                                    <Text className="text-gray-500 text-xs">Total Used</Text>
                                </View>
                            </View>



                            {/* Status Messages */}
                            {usedCoins > 0 && (
                                <View className="bg-amber-50 p-3 rounded-lg mt-4 w-full">
                                    <Text className="text-amber-800 text-sm text-center">
                                        You have {usedCoins} coins that have been used for discounts.
                                        Used coins are refunded if orders are cancelled.
                                    </Text>
                                </View>
                            )}

                            <View className="w-full mt-6 pt-4 border-t border-gray-100">
                                <Text className="text-gray-600 text-center font-medium mb-2">
                                    How Coins Work
                                </Text>
                                <Text className="text-gray-600 text-xs text-center mb-1">
                                    • Earn 10 coins for every ₹100 spent
                                </Text>
                                <Text className="text-gray-600 text-xs text-center mb-1">
                                    • 1 coin = ₹1 discount on future orders
                                </Text>
                                <Text className="text-gray-600 text-xs text-center mb-1">
                                    • Used coins refunded, earned coins removed if orders cancelled
                                </Text>
                                <Text className="text-gray-500 text-xs text-center mt-2">
                                    Dynamic limits apply based on order value to maintain quality.
                                </Text>
                            </View>
                        </View>



                        {/* Coin History Section */}
                        <View className="flex-row justify-between items-center mb-4">
                            <Text className="text-lg font-bold text-gray-800">Fresh Coin History</Text>
                            <TouchableOpacity
                                className="bg-madder/10 px-3 py-1 rounded-full"
                                onPress={async () => {
                                    setLoading(true);
                                    try {
                                        // Fetch coin data directly from API with cache-busting timestamp
                                        const timestamp = new Date().getTime();
                                        const coinData = await getUserCoins(timestamp);

                                        // Set coin totals from API response
                                        setTotalCoins(coinData.totalCoins || 0);
                                        setUsedCoins(coinData.usedCoins || 0);

                                        // Process coin history from API
                                        if (coinData.coinsHistory && Array.isArray(coinData.coinsHistory)) {
                                            console.log('Refresh - Raw coin history from API:', coinData.coinsHistory);

                                            // Map the API coin history to our UI format
                                            const history = coinData.coinsHistory.map(coin => {
                                                // Determine the type based on the transaction type and amount
                                                let type = 'earned';
                                                if (coin.type === 'USED') type = 'redeemed';
                                                if (coin.type === 'EXPIRED') type = 'expired';
                                                if (coin.type === 'REFUNDED') {
                                                    // Distinguish between refunded used coins vs removed earned coins
                                                    if (coin.amount > 0 || (coin.description && coin.description.includes('Refunded used coins'))) {
                                                        type = 'refunded'; // Used coins being refunded (positive)
                                                    } else {
                                                        type = 'removed'; // Earned coins being removed (negative)
                                                    }
                                                }

                                                console.log(`Refresh - Processing coin: ${coin.type} -> ${type}, amount: ${coin.amount}`);

                                                return {
                                                    id: `${type}-${coin._id || coin.orderId || Math.random().toString(36).substring(7)}`,
                                                    type: type,
                                                    amount: Math.abs(coin.amount), // Use absolute value for display
                                                    originalAmount: coin.amount, // Keep original amount for sign determination
                                                    description: coin.description || `${type.charAt(0).toUpperCase() + type.slice(1)} coins`,
                                                    date: coin.date || new Date().toISOString(),
                                                    expiry: coin.expiry,
                                                    orderNumber: coin.orderNumber,
                                                    orderId: coin.orderId
                                                };
                                            }).sort((a, b) => new Date(b.date) - new Date(a.date)); // Sort by date, newest first

                                            setCoinHistory(history);
                                            console.log('Refresh - Processed history:', history);
                                        } else {
                                            setCoinHistory([]);
                                        }
                                    } catch (error) {
                                        console.error('Error refreshing data:', error);
                                        Alert.alert('Error', 'Failed to refresh coin data');
                                    } finally {
                                        setLoading(false);
                                    }
                                }}
                            >
                                <Text className="text-madder font-medium">Refresh</Text>
                            </TouchableOpacity>
                        </View>

                        {/* Coin History List */}
                        {coinHistory.length > 0 ? (
                            coinHistory.map((item) => (
                                <View key={item.id}>
                                    {renderCoinHistoryItem({ item })}
                                </View>
                            ))
                        ) : (
                            <View className="items-center justify-center py-10">
                                <View className="w-16 h-16 rounded-full bg-gray-100 items-center justify-center mb-4">
                                    <MaterialIcons name="history" size={30} color="#9CA3AF" />
                                </View>
                                <Text className="text-gray-500 text-center">No coin transactions yet</Text>
                                <Text className="text-gray-400 text-center text-sm mt-2">
                                    Start shopping to earn coins!
                                </Text>
                            </View>
                        )}
                    </>
                )}
            </ScrollView>
        </View>
    );
};

export default CoinsScreen;
