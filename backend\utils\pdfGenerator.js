/**
 * PDF Generator Utility
 *
 * This utility generates PDF files from HTML templates using html-pdf.
 */

const ejs = require('ejs');
const pdf = require('html-pdf');
const path = require('path');
const fs = require('fs');
const emailConfig = require('../config/emailConfig');

/**
 * Generate a PDF invoice from order data
 * @param {Object} orderData - Order data for the invoice
 * @returns {Promise<string>} - Path to the generated PDF file
 */
const generateInvoicePdf = async (orderData) => {
    try {
        // Validate required data
        if (!orderData) {
            console.error('Missing order data for PDF generation');
            throw new Error('Missing order data for PDF generation');
        }

        // Ensure invoices directory exists
        const invoicesDir = path.join(__dirname, '..', emailConfig.invoice.directory);
        if (!fs.existsSync(invoicesDir)) {
            fs.mkdirSync(invoicesDir, { recursive: true });
        }

        // Generate invoice number
        const invoiceNumber = `${emailConfig.invoice.prefix}${Date.now()}`;

        // Log template data for debugging
        console.log('PDF template data:', {
            orderId: orderData.orderId || 'No orderId',
            orderNumber: orderData.orderNumber || 'No orderNumber',
            hasOrderNumber: !!orderData.orderNumber,
            hasItems: Array.isArray(orderData.items) ? `${orderData.items.length} items` : 'No items array'
        });

        // Prepare data for the template
        const templateData = {
            invoiceNumber,
            orderId: orderData.orderId,
            // Ensure orderNumber is always available as a string
            orderNumber: orderData.orderNumber ? orderData.orderNumber.toString() : orderData.orderId,
            orderDate: new Date(orderData.orderPlacedAt).toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }),
            customerName: orderData.customerName,
            customerEmail: orderData.customerEmail,
            customerPhone: orderData.customerPhone,
            deliveryAddress: typeof orderData.deliveryAddress === 'string'
                ? orderData.deliveryAddress
                : formatAddress(orderData.deliveryAddress),
            items: (orderData.items || []).map(item => ({
                ...item,
                // Ensure all required properties are defined and valid
                name: item.name || 'Unknown Item',
                quantity: typeof item.quantity === 'number' ? item.quantity : 1,
                price: typeof item.price === 'number' ? item.price : 0,
                discount_price: typeof item.discount_price === 'number' ? item.discount_price : null
            })),
            originalAmount: orderData.originalAmount || 0,
            totalAmount: orderData.totalAmount || 0,
            couponDiscount: orderData.couponDiscount || 0,
            coinsDiscount: orderData.coinsDiscount || 0,
            coinsEarned: orderData.coinsEarned || 0,
            paymentMethod: orderData.paymentMethod || 'COD',
            deliveryFee: orderData.deliveryFee || 0,
            appliedCoupon: orderData.appliedCoupon || null
        };

        // Path to the invoice template
        const templatePath = path.join(__dirname, '..', 'templates', 'emails', 'invoice-new.ejs');

        // Render HTML from EJS template
        const html = await ejs.renderFile(templatePath, templateData);

        // PDF options
        const options = {
            format: 'A4',
            border: {
                top: '15mm',
                right: '10mm',
                bottom: '15mm',
                left: '10mm'
            },
            footer: {
                height: '10mm',
                contents: {
                    default: '<div style="text-align: center; font-size: 10px; color: #777;">Page {{page}} of {{pages}}</div>'
                }
            },
            // Ensure content fits on one page
            renderDelay: 1000,
            timeout: 30000
        };

        // Generate PDF file path
        const pdfPath = path.join(invoicesDir, `${orderData.orderNumber ? orderData.orderNumber.toString() : orderData.orderId}.pdf`);

        // Generate PDF
        return new Promise((resolve, reject) => {
            try {
                // Verify that the HTML content is valid
                if (!html || typeof html !== 'string' || html.trim().length === 0) {
                    console.error('Invalid HTML content for PDF generation');
                    return reject(new Error('Invalid HTML content for PDF generation'));
                }

                // Verify that the PDF path is valid
                if (!pdfPath || typeof pdfPath !== 'string') {
                    console.error('Invalid PDF path for PDF generation');
                    return reject(new Error('Invalid PDF path for PDF generation'));
                }

                // Create PDF
                pdf.create(html, options).toFile(pdfPath, (err, res) => {
                    if (err) {
                        console.error('Error generating PDF:', err);
                        reject(err);
                    } else {
                        // Verify the PDF was created successfully
                        if (!fs.existsSync(pdfPath)) {
                            console.error(`PDF file was not created at path: ${pdfPath}`);
                            return reject(new Error('PDF file was not created'));
                        }

                        console.log(`PDF invoice generated: ${pdfPath}`);
                        resolve(pdfPath);
                    }
                });
            } catch (error) {
                console.error('Unexpected error in PDF generation:', error);
                reject(error);
            }
        });
    } catch (error) {
        console.error('Error in generateInvoicePdf:', error);
        throw error;
    }
};

/**
 * Format address object into a string
 * @param {Object} address - Address object
 * @returns {string} - Formatted address string
 */
const formatAddress = (address) => {
    if (!address) return 'Address not available';

    // If address has fullAddress property, use it
    if (address.fullAddress) return address.fullAddress;

    // Otherwise, construct from components
    const components = [];

    if (address.doorNo) components.push(address.doorNo);
    if (address.streetName) components.push(address.streetName);
    if (address.area) components.push(address.area);
    if (address.district || address.city) components.push(address.district || address.city);
    if (address.pincode) components.push(address.pincode);

    return components.join(', ');
};

module.exports = {
    generateInvoicePdf
};
