const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid'); // UUID for unique order IDs

const orderSchema = new mongoose.Schema({
    orderId: { type: String, default: uuidv4, unique: true }, // Auto-generate unique order ID
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    items: [
        {
            productId: { type: mongoose.Schema.Types.ObjectId, ref: 'Product', required: true }, // Reference to Product
            name: { type: String, required: true },
            quantity: { type: Number, required: true, min: 1 },
            price: { type: Number, required: true, min: 0 },
            image: { type: String }
        }
    ],
    totalAmount: { type: Number, required: true, min: 0 },
    originalAmount: { type: Number, min: 0 }, // Original amount before discounts
    orderPlacedAt: { type: Date, default: Date.now },
    // Updated to support both string and object formats for backward compatibility
    deliveryAddress: {
        type: mongoose.Schema.Types.Mixed,
        required: true,
        validate: {
            validator: function(value) {
                // Accept either a string or an object with at least fullAddress
                return typeof value === 'string' ||
                      (typeof value === 'object' && value !== null &&
                       (value.fullAddress || (value.doorNo && value.streetName)));
            },
            message: 'Delivery address must be a string or an object with address details'
        }
    },
    // Delivery partner assignment details
    deliveryPartner: { type: mongoose.Schema.Types.ObjectId, ref: 'DeliveryPartner', index: true },
    deliveryPartnerAssignedAt: { type: Date },
    deliveryPartnerAssignedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    deliveryStartedAt: { type: Date }, // When delivery partner started the delivery (OUT_FOR_DELIVERY)
    deliveredAt: { type: Date }, // When order was delivered

    expectedDelivery: {
        type: Date,
        default: () => new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // Defaults to 3 days from order date
    },
    status: {
        type: String,
        enum: ['PLACED', 'CONFIRMED', 'PREPARING', 'OUT_FOR_DELIVERY', 'DELIVERED', 'CANCELLED'],
        default: 'PLACED'
    },
    orderNumber: { type: Number }, // For client-side order number
    paymentMethod: { type: String },
    couponDiscount: { type: Number, default: 0 },
    coinsDiscount: { type: Number, default: 0 }, // Discount from using coins
    appliedCoupon: { type: String, default: null },
    coinsEarned: { type: Number, default: 0 },
    // Store delivery info for display purposes
    expectedDeliveryInfo: { type: mongoose.Schema.Types.Mixed },
    // Location coordinates for delivery
    deliveryCoordinates: {
        latitude: { type: Number, default: null },
        longitude: { type: Number, default: null }
    }
}, { timestamps: true }); // Adds createdAt & updatedAt fields

const Order = mongoose.model('Order', orderSchema);
module.exports = Order;
