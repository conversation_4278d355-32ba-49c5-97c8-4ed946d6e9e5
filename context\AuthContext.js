import React, { createContext, useState, useContext, useEffect } from 'react';
import { isAuthenticated, getUserData, clearAuthData } from '../utils/authStorage';
import { USER_TYPES } from '../config/constants';

// Create the context
const AuthContext = createContext();

// Create a provider component
export const AuthProvider = ({ children }) => {
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [userType, setUserType] = useState(null);
    const [loading, setLoading] = useState(true);

    // Check authentication status on app start
    useEffect(() => {
        const checkAuth = async () => {
            try {
                console.log('Checking authentication status...');

                // Normal authentication check
                console.log('Auth context running in normal mode');

                // Check if user is authenticated (both in dev and production)
                const isAuth = await isAuthenticated();
                console.log('Authentication status:', isAuth);
                setIsLoggedIn(isAuth);

                // Load user data if authenticated
                if (isAuth) {
                    const userData = await getUserData();
                    console.log('User data:', userData);
                    if (userData && userData.userType) {
                        // Normalize user type to uppercase for consistency
                        const normalizedUserType = userData.userType.toUpperCase();
                        console.log('Normalized user type:', normalizedUserType);

                        // Set the user type based on the normalized value
                        if (normalizedUserType === 'ADMIN') {
                            setUserType(USER_TYPES.ADMIN);
                        } else if (normalizedUserType === 'DELIVERY_PARTNER') {
                            setUserType(USER_TYPES.DELIVERY_PARTNER);
                        } else {
                            setUserType(USER_TYPES.USER);
                        }
                    }
                }
            } catch (error) {
                console.error('Error checking authentication:', error);
                // If there's an error, set isLoggedIn to false to be safe
                setIsLoggedIn(false);
            } finally {
                console.log('Authentication check complete');
                setLoading(false);
            }
        };

        checkAuth();
    }, []);

    // Login function
    const login = async (userData) => {
        try {
            console.log('Login function called with userData:', userData);

            if (!userData) {
                console.error('Login failed: No user data provided');
                return false;
            }

            // Set user type if available
            if (userData.userType) {
                // Normalize user type to uppercase for consistency
                const normalizedUserType = userData.userType.toUpperCase();
                console.log('Setting user type to:', userData.userType, '(normalized to:', normalizedUserType, ')');

                // Set the user type based on the normalized value
                if (normalizedUserType === 'ADMIN') {
                    console.log('User is admin, setting user type to ADMIN');
                    setUserType(USER_TYPES.ADMIN);
                } else if (normalizedUserType === 'DELIVERY_PARTNER') {
                    console.log('User is delivery partner, setting user type to DELIVERY_PARTNER');
                    setUserType(USER_TYPES.DELIVERY_PARTNER);

                    // Create a function to refresh the delivery partner context after a short delay
                    // This allows the auth data to be saved before the context tries to use it
                    setTimeout(() => {
                        try {
                            // Use the global event to refresh the delivery partner context
                            if (global.refreshDeliveryPartnerContext) {
                                console.log('Triggering global refresh of delivery partner context');
                                global.refreshDeliveryPartnerContext();
                            } else {
                                console.log('Setting up global refresh function for delivery partner context');
                                // Set up the global function for future use
                                global.refreshDeliveryPartnerContext = () => {
                                    console.log('Global refresh function called');
                                };
                            }
                        } catch (contextError) {
                            console.error('Error refreshing delivery partner context:', contextError);
                        }
                    }, 500);
                } else {
                    console.log('Setting user type to USER');
                    setUserType(USER_TYPES.USER);
                }
            } else if (userData.isAdmin) {
                console.log('User is admin, setting user type to ADMIN');
                setUserType(USER_TYPES.ADMIN);
            } else if (userData.isDeliveryPartner) {
                console.log('User is delivery partner, setting user type to DELIVERY_PARTNER');
                setUserType(USER_TYPES.DELIVERY_PARTNER);

                // Same refresh logic for isDeliveryPartner property
                setTimeout(() => {
                    try {
                        if (global.refreshDeliveryPartnerContext) {
                            console.log('Triggering global refresh of delivery partner context');
                            global.refreshDeliveryPartnerContext();
                        }
                    } catch (contextError) {
                        console.error('Error refreshing delivery partner context:', contextError);
                    }
                }, 500);
            } else {
                console.log('Setting default user type to USER');
                setUserType(USER_TYPES.USER);
            }

            // Set logged in state
            setIsLoggedIn(true);
            console.log('User successfully logged in');

            return true;
        } catch (error) {
            console.error('Error in login function:', error);
            return false;
        }
    };

    // Logout function
    const logout = async () => {
        try {
            console.log('Logging out...');

            // Check if this is a test user first
            const { getUserData } = require('../utils/authStorage');
            const userData = await getUserData();
            const isTestUser = userData?.isTestUser || false;

            // Only call backend API for non-test users
            if (!isTestUser) {
                // First, try to call the backend logout API
                try {
                    const { getAuthToken } = require('../utils/authStorage');
                    const token = await getAuthToken();

                    if (token) {
                        console.log('Calling backend logout API...');
                        // Import axios directly to avoid undefined issues
                        const axios = require('axios').default;
                        const { API_URL } = require('../config/constants');

                        try {
                            await axios.post(`${API_URL}/auth/logout`, {}, {
                                headers: {
                                    Authorization: `Bearer ${token}`
                                }
                            });
                            console.log('Backend logout successful');
                        } catch (axiosError) {
                            console.error('Axios error during logout API call:', axiosError.message);
                            console.log('Continuing with local logout...');
                        }
                    }
                } catch (apiError) {
                    // If the API call fails, just log the error and continue with local logout
                    console.error('Error calling logout API:', apiError);
                    console.log('Continuing with local logout...');
                }
            } else {
                console.log('Test user detected - skipping backend logout API call');
            }

            // Clear local storage
            await clearAuthData();

            // Update state
            setIsLoggedIn(false);
            setUserType(null);

            console.log('Logout complete');
            return true;
        } catch (error) {
            console.error('Error logging out:', error);
            // Still return true to allow UI to proceed with logout
            return true;
        }
    };

    // Value to be provided to consumers
    const value = {
        isLoggedIn,
        userType,
        loading,
        login,
        logout,
        isAdmin: userType === USER_TYPES.ADMIN,
        isDeliveryPartner: userType === USER_TYPES.DELIVERY_PARTNER,
        isUser: userType === USER_TYPES.USER
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

// Custom hook to use the auth context
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
