/**
 * Utility functions for JWT token generation and verification
 */
const jwt = require('jsonwebtoken');

/**
 * Generate JWT access token (no expiration)
 * @param {Object} payload - Data to include in token
 * @returns {string} - Generated JWT token
 */
const generateAccessToken = (payload) => {
    return jwt.sign(
        payload,
        process.env.JWT_SECRET || 'your-secret-key'
        // No expiration time
    );
};

/**
 * Generate JWT refresh token (no expiration)
 * @param {Object} payload - Data to include in token
 * @returns {string} - Generated refresh token
 */
const generateRefreshToken = (payload) => {
    return jwt.sign(
        payload,
        process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key'
        // No expiration time
    );
};

/**
 * Verify JWT token (no expiration check)
 * @param {string} token - JWT token to verify
 * @param {boolean} isRefreshToken - Whether this is a refresh token
 * @returns {Object} - Decoded token payload or null if invalid
 */
const verifyToken = (token, isRefreshToken = false) => {
    try {
        const secret = isRefreshToken
            ? (process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key')
            : (process.env.JWT_SECRET || 'your-secret-key');

        // Ignore expiration by setting ignoreExpiration to true
        return jwt.verify(token, secret, { ignoreExpiration: true });
    } catch (error) {
        // Log error but not for expiration errors
        if (error.name !== 'TokenExpiredError') {
            console.error('JWT verification error:', error.message);
        }
        return null;
    }
};

module.exports = {
    generateAccessToken,
    generateRefreshToken,
    verifyToken
};
