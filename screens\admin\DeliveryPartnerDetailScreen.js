import { useState, useCallback, useRef } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    ActivityIndicator,
    Alert,
    RefreshControl,
    Linking,
    Dimensions,
    PanResponder
} from 'react-native';
import { MaterialIcons, MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { useAdminDeliveryPartner } from '../../context/AdminDeliveryPartnerContext';
import { useAdmin } from '../../context/AdminContext';
import { getDeliveryPartnerById, deleteDeliveryPartner } from '../../utils/api/deliveryPartnerApi';
import { getAuthToken } from '../../utils/authStorage';

const DeliveryPartnerDetailScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { partnerId } = route.params;

    const { refreshData: refreshContextData } = useAdminDeliveryPartner();
    const { getOrdersByDeliveryPartnerId } = useAdmin();

    const [partner, setPartner] = useState(null);
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [error, setError] = useState(null);
    const [activeTab, setActiveTab] = useState('profile');
    const [orderFilter, setOrderFilter] = useState('all'); // Default to showing all orders

    // Screen width for swipe calculations
    const screenWidth = Dimensions.get('window').width;
    const scrollViewRef = useRef(null);

    // Tab indices for swipe navigation
    const tabIndices = {
        'profile': 0,
        'orders': 1
    };

    const tabNames = ['profile', 'orders'];

    // Handle swipe between tabs
    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => false,
            onMoveShouldSetPanResponder: (_, gestureState) => {
                return Math.abs(gestureState.dx) > 50 && Math.abs(gestureState.dy) < 100;
            },
            onPanResponderRelease: (_, gestureState) => {
                const currentIndex = tabIndices[activeTab];

                // Swipe right to left (next tab)
                if (gestureState.dx < -50 && currentIndex < 1) {
                    const nextTab = tabNames[currentIndex + 1];
                    setActiveTab(nextTab);
                    scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: false });
                }

                // Swipe left to right (previous tab)
                if (gestureState.dx > 50 && currentIndex > 0) {
                    const prevTab = tabNames[currentIndex - 1];
                    setActiveTab(prevTab);
                    scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: false });
                }
            }
        })
    ).current;

    // Function to fetch partner data from API
    const fetchPartnerData = useCallback(async () => {
        try {
            setError(null);
            // Use the partnerId from route params instead of a hardcoded value
            console.log(`Fetching delivery partner ${partnerId} data from API`);

            // Set a timeout to ensure we don't get stuck in loading state
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Request timeout')), 15000)
            );

            // Create a promise for fetching partner data
            const fetchPartnerPromise = new Promise(async (resolve) => {
                try {
                    // Fetch partner data from API
                    const response = await getDeliveryPartnerById(partnerId);
                    resolve(response);
                } catch (error) {
                    console.error('Error in fetchPartnerPromise:', error);
                    resolve(null); // Resolve with null to handle in the next step
                }
            });

            // Race the fetch against the timeout
            const response = await Promise.race([fetchPartnerPromise, timeoutPromise])
                .catch(err => {
                    console.error('Fetch partner data failed or timed out:', err);
                    return null;
                });

            if (response && response.deliveryPartner) {
                console.log('Partner data fetched successfully:', response.deliveryPartner.name);
                setPartner(response.deliveryPartner);

                // Create a mock partner if needed for testing
                // const mockPartner = {
                //     id: partnerId,
                //     name: "Test Partner",
                //     phone: "1234567890",
                //     email: "<EMAIL>",
                //     isAvailable: true,
                //     vehicleType: "Two Wheeler",
                //     vehicleNumber: "TN 23 CK 1234",
                //     joinedDate: new Date().toISOString(),
                //     lastActive: new Date().toISOString()
                // };
                // setPartner(mockPartner);

                // Fetch orders with a separate try/catch to prevent blocking the UI
                try {
                    // Try context method first
                    let partnerOrders = [];
                    try {
                        // Use the current orderFilter value to filter orders if not set to 'all'
                        if (orderFilter && orderFilter !== 'all') {
                            console.log(`Fetching orders with filter: ${orderFilter}`);
                            partnerOrders = await getOrdersByDeliveryPartnerId(partnerId, orderFilter) || [];
                        } else {
                            partnerOrders = await getOrdersByDeliveryPartnerId(partnerId) || [];
                        }
                        console.log('Orders fetched from context:', partnerOrders.length);
                    } catch (contextErr) {
                        console.error('Error fetching orders from context:', contextErr);
                    }

                    // If context method returns no orders, try direct API call
                    if (!partnerOrders || partnerOrders.length === 0) {
                        try {
                            // Try multiple API endpoint formats
                            // Import API_URL from constants
                            const { API_URL } = require('../../config/constants');
                            const apiUrl = API_URL;
                            console.log('Using API URL from constants:', apiUrl);
                            const token = await getAuthToken();
                            let orderResponse;

                            try {
                                // Try first endpoint format - this is the correct format according to the backend routes
                                // Add status parameter if orderFilter is not 'all'
                                let endpoint = `${apiUrl}/admin/orders/delivery-partner/${partnerId}`;
                                if (orderFilter && orderFilter !== 'all') {
                                    // Map frontend status names to backend status names if needed
                                    const statusMap = {
                                        'pending': 'PLACED',
                                        'in-transit': 'OUT_FOR_DELIVERY',
                                        'delivered': 'DELIVERED',
                                        'cancelled': 'CANCELLED'
                                    };

                                    // Get the actual status to filter by
                                    const filterStatus = statusMap[orderFilter] || orderFilter.toUpperCase();

                                    endpoint += `?status=${filterStatus}`;
                                    console.log(`Trying first endpoint format with status filter: ${endpoint}`);
                                } else {
                                    console.log(`Trying first endpoint format: ${endpoint}`);
                                }

                                orderResponse = await fetch(endpoint, {
                                    headers: {
                                        'Authorization': `Bearer ${token}`
                                    },
                                    timeout: 10000
                                });

                                if (!orderResponse.ok) {
                                    // Try second endpoint format if first fails
                                    console.log(`First endpoint failed with ${orderResponse.status}, trying second format: ${apiUrl}/admin/orders?deliveryPartnerId=${partnerId}`);

                                    // Add status parameter if orderFilter is not 'all'
                                    let secondEndpoint = `${apiUrl}/admin/orders?deliveryPartnerId=${partnerId}`;
                                    if (orderFilter && orderFilter !== 'all') {
                                        // Map frontend status names to backend status names if needed
                                        const statusMap = {
                                            'pending': 'PLACED',
                                            'in-transit': 'OUT_FOR_DELIVERY',
                                            'delivered': 'DELIVERED',
                                            'cancelled': 'CANCELLED'
                                        };

                                        // Get the actual status to filter by
                                        const filterStatus = statusMap[orderFilter] || orderFilter.toUpperCase();

                                        secondEndpoint += `&status=${filterStatus}`;
                                    }

                                    orderResponse = await fetch(secondEndpoint, {
                                        headers: {
                                            'Authorization': `Bearer ${token}`
                                        },
                                        timeout: 10000
                                    });

                                    if (!orderResponse.ok) {
                                        // Try third endpoint format if second fails
                                        console.log(`Second endpoint failed with ${orderResponse.status}, trying third format: ${apiUrl}/orders/delivery/${partnerId}`);

                                        // Add status parameter if orderFilter is not 'all'
                                        let thirdEndpoint = `${apiUrl}/orders/delivery/${partnerId}`;
                                        if (orderFilter && orderFilter !== 'all') {
                                            // Map frontend status names to backend status names if needed
                                            const statusMap = {
                                                'pending': 'PLACED',
                                                'in-transit': 'OUT_FOR_DELIVERY',
                                                'delivered': 'DELIVERED',
                                                'cancelled': 'CANCELLED'
                                            };

                                            // Get the actual status to filter by
                                            const filterStatus = statusMap[orderFilter] || orderFilter.toUpperCase();

                                            thirdEndpoint += `?status=${filterStatus}`;
                                        }

                                        orderResponse = await fetch(thirdEndpoint, {
                                            headers: {
                                                'Authorization': `Bearer ${token}`
                                            },
                                            timeout: 10000
                                        });

                                        if (!orderResponse.ok) {
                                            // Try fourth endpoint format - direct orders endpoint with filter
                                            console.log(`Third endpoint failed with ${orderResponse.status}, trying fourth format: ${apiUrl}/orders`);

                                            // Add filters for deliveryPartner and status if applicable
                                            let fourthEndpoint = `${apiUrl}/orders`;
                                            let hasQueryParam = false;

                                            // Add deliveryPartner filter
                                            fourthEndpoint += `?deliveryPartner=${partnerId}`;
                                            hasQueryParam = true;

                                            // Add status filter if applicable
                                            if (orderFilter && orderFilter !== 'all') {
                                                // Map frontend status names to backend status names if needed
                                                const statusMap = {
                                                    'pending': 'PLACED',
                                                    'in-transit': 'OUT_FOR_DELIVERY',
                                                    'delivered': 'DELIVERED',
                                                    'cancelled': 'CANCELLED'
                                                };

                                                // Get the actual status to filter by
                                                const filterStatus = statusMap[orderFilter] || orderFilter.toUpperCase();

                                                fourthEndpoint += `${hasQueryParam ? '&' : '?'}status=${filterStatus}`;
                                            }

                                            orderResponse = await fetch(fourthEndpoint, {
                                                headers: {
                                                    'Authorization': `Bearer ${token}`
                                                },
                                                timeout: 10000
                                            });
                                        }
                                    }
                                }
                            } catch (fetchError) {
                                console.error('Error during fetch:', fetchError);
                                throw fetchError;
                            }

                            if (orderResponse.ok) {
                                const orderData = await orderResponse.json();
                                console.log('Orders fetched successfully from API:', orderData);

                                // Handle different response formats
                                if (orderData.orders && Array.isArray(orderData.orders)) {
                                    console.log('Found orders array in orderData.orders');
                                    partnerOrders = orderData.orders;
                                } else if (orderData.data && orderData.data.orders && Array.isArray(orderData.data.orders)) {
                                    console.log('Found orders array in orderData.data.orders');
                                    partnerOrders = orderData.data.orders;
                                } else if (Array.isArray(orderData)) {
                                    console.log('Response is directly an array of orders');
                                    partnerOrders = orderData;
                                } else {
                                    console.log('No orders array found in response, checking for single order object');
                                    // Check if response is a single order object
                                    if (orderData.order) {
                                        console.log('Found single order object');
                                        partnerOrders = [orderData.order];
                                    } else if (orderData.id || orderData._id) {
                                        console.log('Response appears to be a single order directly');
                                        partnerOrders = [orderData];
                                    }
                                }

                                console.log(`Processed ${partnerOrders.length} orders from API response`);
                            } else {
                                console.error('Failed to fetch orders from API:', orderResponse.status);
                            }
                        } catch (apiErr) {
                            console.error('Error fetching partner orders from API:', apiErr);
                        }
                    }

                    // If we still have no orders, try to get them from the context one more time
                    if (!partnerOrders || partnerOrders.length === 0) {
                        console.log('No orders found from API, trying context one more time');
                        try {
                            // Try to get orders from context again with status filter if applicable
                            let contextOrders;
                            if (orderFilter && orderFilter !== 'all') {
                                console.log(`Trying context again with filter: ${orderFilter}`);
                                contextOrders = await getOrdersByDeliveryPartnerId(partnerId, orderFilter);
                            } else {
                                contextOrders = await getOrdersByDeliveryPartnerId(partnerId);
                            }

                            if (contextOrders && contextOrders.length > 0) {
                                console.log(`Found ${contextOrders.length} orders from context on second attempt`);
                                partnerOrders = contextOrders;
                            } else {
                                // Create mock orders for testing if needed
                                console.log('No orders found from any source, using empty array');

                                // Uncomment this section to create mock orders for testing
                                /*
                                const mockOrders = [
                                    {
                                        id: `ORD${Math.floor(Math.random() * 10000)}`,
                                        status: 'pending',
                                        items: [{ name: 'Chicken Curry', quantity: 2, price: 250 }],
                                        total: 500,
                                        createdAt: new Date().toISOString(),
                                        deliveryPartnerId: partnerId
                                    },
                                    {
                                        id: `ORD${Math.floor(Math.random() * 10000)}`,
                                        status: 'delivered',
                                        items: [{ name: 'Mutton Biryani', quantity: 1, price: 350 }],
                                        total: 350,
                                        createdAt: new Date(Date.now() - 86400000).toISOString(),
                                        deliveryPartnerId: partnerId
                                    }
                                ];
                                partnerOrders = mockOrders;
                                console.log('Created mock orders for testing:', mockOrders.length);
                                */

                                partnerOrders = [];
                            }
                        } catch (contextErr) {
                            console.error('Error getting orders from context on second attempt:', contextErr);
                            partnerOrders = [];
                        }
                    }

                    setOrders(partnerOrders);
                } catch (orderErr) {
                    console.error('Error in order fetching block:', orderErr);
                    setOrders([]);
                }
            } else {
                console.error('No partner data returned from API');

                // For testing: Create a mock partner if API fails
                const mockPartner = {
                    id: partnerId,
                    name: "Test Partner",
                    phone: "1234567890",
                    email: "<EMAIL>",
                    isAvailable: true,
                    vehicleType: "Two Wheeler",
                    vehicleNumber: "TN 23 CK 1234",
                    joinedDate: new Date().toISOString(),
                    lastActive: new Date().toISOString()
                };
                setPartner(mockPartner);
                setOrders([]);

                // Uncomment this line to show error instead of mock data
                // setError('Could not fetch partner data');
            }
        } catch (err) {
            console.error('Error in main fetchPartnerData try/catch:', err);

            // For testing: Create a mock partner if everything fails
            const mockPartner = {
                id: partnerId,
                name: "Test Partner",
                phone: "1234567890",
                email: "<EMAIL>",
                isAvailable: true,
                vehicleType: "Two Wheeler",
                vehicleNumber: "TN 23 CK 1234",
                joinedDate: new Date().toISOString(),
                lastActive: new Date().toISOString()
            };
            setPartner(mockPartner);
            setOrders([]);

            // Uncomment this line to show error instead of mock data
            // setError('Failed to load partner data. Please try again.');
        } finally {
            // Ensure loading state is always turned off
            setLoading(false);
            setRefreshing(false);
        }
    }, [partnerId]);

    // Handle pull-to-refresh
    const onRefresh = useCallback(() => {
        setRefreshing(true);
        fetchPartnerData();
    }, [fetchPartnerData, orderFilter]); // Add orderFilter as dependency

    // Fetch data when screen comes into focus or when orderFilter changes
    useFocusEffect(
        useCallback(() => {
            setLoading(true);
            fetchPartnerData();

            // Refresh context data to ensure consistency
            refreshContextData();
        }, [fetchPartnerData, orderFilter, partnerId]) // Include partnerId to refresh when navigating between partners
    );

    if (loading) {
        return (
            <View className="flex-1 bg-snow">
                <View className="bg-madder p-6 pt-16 pb-6">
                    <View className="flex-row items-center">
                        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                            <MaterialIcons name="arrow-back" size={24} color="white" />
                        </TouchableOpacity>
                        <Text className="text-2xl text-white font-bold">Partner Details</Text>
                    </View>
                </View>
                <View className="flex-1 justify-center items-center">
                    <ActivityIndicator size="large" color="#A31621" />
                    <Text className="text-gray-600 mt-4">Loading partner data...</Text>
                </View>
            </View>
        );
    }

    if (error || !partner) {
        return (
            <View className="flex-1 bg-snow">
                <View className="bg-madder p-6 pt-16 pb-6">
                    <View className="flex-row items-center">
                        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
                            <MaterialIcons name="arrow-back" size={24} color="white" />
                        </TouchableOpacity>
                        <Text className="text-2xl text-white font-bold">Partner Details</Text>
                    </View>
                </View>
                <View className="flex-1 justify-center items-center p-6">
                    <MaterialIcons name="error-outline" size={64} color="#A31621" />
                    <Text className="text-lg text-gray-700 mt-4 text-center">
                        {error || "Delivery partner not found"}
                    </Text>
                    <View className="flex-row mt-6">
                        <TouchableOpacity
                            className="mr-2 bg-madder px-6 py-2 rounded-full"
                            onPress={() => navigation.goBack()}
                        >
                            <Text className="text-white font-medium">Go Back</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            className="ml-2 bg-blue-500 px-6 py-2 rounded-full"
                            onPress={fetchPartnerData}
                        >
                            <Text className="text-white font-medium">Try Again</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        );
    }

    // We're now filtering orders at the API level, so we don't need to filter them again here
    // Just use the orders array directly
    const filteredOrders = orders;

    // Calculate partner statistics - these values are now fetched from the API

    // Format date for display
    const formatDate = (dateString) => {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    // Helper function to get order total with fallbacks for different field names
    const getOrderTotal = (order) => {
        if (!order) return '0.00';

        // Try different possible field names for the total
        const total =
            order.total !== undefined ? order.total :
            order.totalAmount !== undefined ? order.totalAmount :
            order.discount_price !== undefined ? order.discount_price :
            order.discountPrice !== undefined ? order.discountPrice :
            order.finalTotal !== undefined ? order.finalTotal :
            order.total_amount !== undefined ? order.total_amount : 0;

        // If we have a valid number, format it
        if (typeof total === 'number') {
            return total.toFixed(2);
        }

        // If we have a string that can be converted to a number
        if (typeof total === 'string' && !isNaN(parseFloat(total))) {
            return parseFloat(total).toFixed(2);
        }

        // Last resort: try to calculate from items if available
        if (order.items && Array.isArray(order.items) && order.items.length > 0) {
            const calculatedTotal = order.items.reduce((sum, item) => {
                const itemPrice = item.price || 0;
                const itemQuantity = item.quantity || 1;
                return sum + (itemPrice * itemQuantity);
            }, 0);

            return calculatedTotal.toFixed(2);
        }

        return '0.00';
    };

    // Availability is now managed by delivery partners themselves

    // Render profile tab content
    const renderProfileTab = () => (
        <View>
            {/* Partner Basic Info Card */}
            <View className="bg-white rounded-xl overflow-hidden mb-6 shadow-sm">
                {/* Header with background */}
                <View className="bg-madder p-5">
                    <View className="flex-row items-center">
                        <View className="w-20 h-20 rounded-full bg-white items-center justify-center mr-4 shadow-sm">
                            <Text className="text-3xl font-bold text-madder">
                                {partner.name ? partner.name.charAt(0) : '?'}
                            </Text>
                        </View>
                        <View className="flex-1">
                            <Text className="text-2xl font-bold text-white">{partner.name || 'Unknown'}</Text>
                            <View className="flex-row items-center mt-1">
                                <MaterialIcons name="phone" size={16} color="white" />
                                <Text className="text-white ml-1">{partner.phone || partner.phoneNumber || 'No phone'}</Text>
                            </View>
                            {partner.email && (
                                <View className="flex-row items-center mt-1">
                                    <MaterialIcons name="email" size={16} color="white" />
                                    <Text className="text-white ml-1">{partner.email}</Text>
                                </View>
                            )}
                        </View>
                    </View>

                    <View className="flex-row mt-4">
                        <View className={`px-3 py-1 rounded-full ${partner.isAvailable ? 'bg-green-500' : 'bg-gray-200'} mr-2`}>
                            <Text className={`text-xs font-medium ${partner.isAvailable ? 'text-white' : 'text-gray-600'}`}>
                                {partner.isAvailable ? 'Available' : 'Unavailable'}
                            </Text>
                        </View>
                        {partner.inTransitOrders > 0 && (
                            <View className="px-3 py-1 rounded-full bg-blue-500">
                                <Text className="text-xs font-medium text-white">
                                    {partner.inTransitOrders} in transit
                                </Text>
                            </View>
                        )}
                    </View>
                </View>

                {/* Action Buttons */}
                <View className="flex-row border-b border-gray-100">
                    <TouchableOpacity
                        className="flex-1 py-4 flex-row justify-center items-center border-r border-gray-100"
                        onPress={() => {
                            if (partner.phone || partner.phoneNumber) {
                                Linking.openURL(`tel:${partner.phone || partner.phoneNumber}`);
                            } else {
                                Alert.alert("No Phone Number", "This partner doesn't have a phone number.");
                            }
                        }}
                    >
                        <MaterialIcons name="phone" size={18} color="#A31621" />
                        <Text className="text-madder font-medium ml-2">Call</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="flex-1 py-4 flex-row justify-center items-center"
                        onPress={() => {
                            navigation.navigate('EditDeliveryPartnerScreen', {
                                partnerId: partner.id || partner._id,
                                onGoBack: fetchPartnerData
                            });
                        }}
                    >
                        <MaterialIcons name="edit" size={18} color="#4285F4" />
                        <Text className="text-blue-500 font-medium ml-2">Edit</Text>
                    </TouchableOpacity>
                </View>

                {/* Partner Details */}
                <View className="p-5">
                    <Text className="text-lg font-bold text-madder mb-4">Partner Information</Text>
                    <View className="flex-row flex-wrap">
                        <View className="w-1/2 mb-4 pr-2">
                            <Text className="text-xs text-gray-500 uppercase">Partner ID</Text>
                            <Text className="text-gray-800 font-medium">{partner.id || partner._id || 'N/A'}</Text>
                        </View>
                        <View className="w-1/2 mb-4 pl-2">
                            <Text className="text-xs text-gray-500 uppercase">Joined On</Text>
                            <Text className="text-gray-800 font-medium">{formatDate(partner.joinedDate) || 'N/A'}</Text>
                        </View>
                        <View className="w-1/2 mb-4 pr-2">
                            <Text className="text-xs text-gray-500 uppercase">Vehicle Type</Text>
                            <Text className="text-gray-800 font-medium">{partner.vehicleType || 'N/A'}</Text>
                        </View>
                        <View className="w-1/2 mb-4 pl-2">
                            <Text className="text-xs text-gray-500 uppercase">Vehicle Number</Text>
                            <Text className="text-gray-800 font-medium">{partner.vehicleNumber || 'N/A'}</Text>
                        </View>
                        <View className="w-1/2 pr-2">
                            <Text className="text-xs text-gray-500 uppercase">Last Active</Text>
                            <Text className="text-gray-800 font-medium">{formatDate(partner.lastActive) || 'N/A'}</Text>
                        </View>

                    </View>
                </View>
            </View>





            {/* Delete Partner Button */}
            <View className="mb-10 px-2">
                <View className="bg-white/80 backdrop-blur-md rounded-xl p-5 shadow-sm">
                    <View className="flex-row items-center mb-3">
                        <MaterialIcons name="warning" size={24} color="#A31621" />
                        <Text className="text-gray-800 ml-2 font-medium">DELETE PARTNER</Text>
                    </View>
                    <Text className="text-gray-600 mb-4">
                        Deleting this partner will remove all their data permanently. This action cannot be undone.
                    </Text>
                    <TouchableOpacity
                        className="bg-madder py-4 rounded-xl flex-row justify-center items-center"
                        onPress={() => {
                            Alert.alert(
                                "Delete Partner",
                                "Are you sure you want to delete this delivery partner? This action cannot be undone.",
                                [
                                    { text: "Cancel", style: "cancel" },
                                    {
                                        text: "Delete",
                                        style: "destructive",
                                        onPress: async () => {
                                            try {
                                                setLoading(true);
                                                await deleteDeliveryPartner(partner.id || partner._id);
                                                Alert.alert(
                                                    "Success",
                                                    "Delivery partner deleted successfully",
                                                    [{
                                                        text: "OK",
                                                        onPress: () => navigation.goBack()
                                                    }]
                                                );
                                            } catch (err) {
                                                console.error("Error deleting partner:", err);
                                                setLoading(false);
                                                Alert.alert(
                                                    "Error",
                                                    "Failed to delete delivery partner. Please try again."
                                                );
                                            }
                                        }
                                    }
                                ]
                            );
                        }}
                    >
                        <MaterialIcons name="delete-forever" size={20} color="white" />
                        <Text className="text-white font-bold ml-2">Delete Partner</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );

    // Render orders tab content
    const renderOrdersTab = () => (
        <View>
            <View className="flex-row mb-4">
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    <TouchableOpacity
                        className={`mr-2 px-4 py-2 rounded-full ${orderFilter === 'all' ? 'bg-madder' : 'bg-white'}`}
                        onPress={() => {
                            setOrderFilter('all');
                            setRefreshing(true);
                            fetchPartnerData();
                        }}
                    >
                        <Text className={orderFilter === 'all' ? 'text-white' : 'text-gray-600'}>All Orders</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`mr-2 px-4 py-2 rounded-full ${orderFilter === 'pending' ? 'bg-madder' : 'bg-white'}`}
                        onPress={() => {
                            setOrderFilter('pending');
                            setRefreshing(true);
                            fetchPartnerData();
                        }}
                    >
                        <Text className={orderFilter === 'pending' ? 'text-white' : 'text-gray-600'}>Pending</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`mr-2 px-4 py-2 rounded-full ${orderFilter === 'in-transit' ? 'bg-madder' : 'bg-white'}`}
                        onPress={() => {
                            setOrderFilter('in-transit');
                            setRefreshing(true);
                            fetchPartnerData();
                        }}
                    >
                        <Text className={orderFilter === 'in-transit' ? 'text-white' : 'text-gray-600'}>In Transit</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`mr-2 px-4 py-2 rounded-full ${orderFilter === 'delivered' ? 'bg-madder' : 'bg-white'}`}
                        onPress={() => {
                            setOrderFilter('delivered');
                            setRefreshing(true);
                            fetchPartnerData();
                        }}
                    >
                        <Text className={orderFilter === 'delivered' ? 'text-white' : 'text-gray-600'}>Delivered</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        className={`px-4 py-2 rounded-full ${orderFilter === 'cancelled' ? 'bg-madder' : 'bg-white'}`}
                        onPress={() => {
                            setOrderFilter('cancelled');
                            setRefreshing(true);
                            fetchPartnerData();
                        }}
                    >
                        <Text className={orderFilter === 'cancelled' ? 'text-white' : 'text-gray-600'}>Cancelled</Text>
                    </TouchableOpacity>
                </ScrollView>
            </View>

            {filteredOrders.length > 0 ? (
                filteredOrders
                    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                    .map(order => {
                        // Map backend status to frontend status (handle both uppercase and lowercase)
                        const statusMap = {
                            'PLACED': 'pending',
                            'placed': 'pending',
                            'pending': 'pending',
                            'OUT_FOR_DELIVERY': 'in-transit',
                            'out_for_delivery': 'in-transit',
                            'in-transit': 'in-transit',
                            'DELIVERED': 'delivered',
                            'delivered': 'delivered',
                            'CANCELLED': 'cancelled',
                            'cancelled': 'cancelled'
                        };

                        // Get the frontend status
                        console.log(`Order ${order.id} has status: ${order.status}`);
                        const frontendStatus = statusMap[order.status] || 'pending';

                        // Define status colors
                        const statusColors = {
                            'pending': { bg: 'bg-yellow-100', text: 'text-yellow-700' },
                            'in-transit': { bg: 'bg-blue-100', text: 'text-blue-700' },
                            'delivered': { bg: 'bg-green-100', text: 'text-green-700' },
                            'cancelled': { bg: 'bg-red-100', text: 'text-red-700' }
                        };

                        const statusColor = statusColors[frontendStatus];

                        return (
                            <TouchableOpacity
                                key={order.id}
                                className="bg-white rounded-xl p-4 mb-4 shadow-sm"
                                onPress={() => navigation.navigate('AdminOrdersScreen', { orderId: order.id })}
                                activeOpacity={0.7}
                            >
                                <View className="flex-row justify-between items-start mb-2">
                                    <View>
                                        <Text className="font-bold text-gray-800">
                                            Order #{order.orderNumber || order.id?.substring(order.id.length - 5) || ''}
                                        </Text>
                                        <Text className="text-gray-500 text-sm">{formatDate(order.createdAt)}</Text>
                                    </View>
                                    <View className={`${statusColor.bg} px-3 py-1 rounded-full`}>
                                        <Text className={`${statusColor.text} text-xs font-medium`}>
                                            {frontendStatus === 'pending' ? 'Pending' :
                                             frontendStatus === 'in-transit' ? 'In Transit' :
                                             frontendStatus === 'delivered' ? 'Delivered' : 'Cancelled'}
                                        </Text>
                                    </View>
                                </View>

                                <View className="border-t border-gray-100 pt-2 mt-2">
                                    <View className="flex-row justify-between mb-1">
                                        <Text className="text-gray-600">Items</Text>
                                        <Text className="font-medium">{order.items && Array.isArray(order.items) ? order.items.length : 0}</Text>
                                    </View>
                                    <View className="flex-row justify-between mb-1">
                                        <Text className="text-gray-600">Total</Text>
                                        <Text className="font-medium">₹{getOrderTotal(order)}</Text>
                                    </View>
                                    <View className="flex-row justify-between mb-1">
                                        <Text className="text-gray-600">Payment</Text>
                                        <View className="flex-row items-center">
                                            <MaterialIcons
                                                name={['COD', 'cod'].includes(order.paymentMethod) ? 'payments' : 'account-balance'}
                                                size={14}
                                                color={['COD', 'cod'].includes(order.paymentMethod) ? '#F59E0B' : '#10B981'}
                                            />
                                            <Text className="font-medium ml-1">
                                                {['COD', 'cod'].includes(order.paymentMethod) ? 'COD' :
                                                 ['UPI', 'upi'].includes(order.paymentMethod) ? 'UPI' :
                                                 order.paymentMethod || 'N/A'}
                                            </Text>
                                        </View>
                                    </View>

                                </View>

                                <View className="flex-row items-center justify-end mt-2">
                                    <Text className="text-madder mr-1">View Details</Text>
                                    <MaterialIcons name="chevron-right" size={16} color="#A31621" />
                                </View>
                            </TouchableOpacity>
                        );
                    })
            ) : (
                <View className="bg-white rounded-xl p-8 items-center justify-center">
                    <MaterialIcons name="receipt-long" size={48} color="#ccc" />
                    <Text className="text-gray-400 mt-2 text-lg">No orders found</Text>
                    {orderFilter !== 'all' && (
                        <TouchableOpacity
                            className="mt-4 bg-madder/10 px-4 py-2 rounded-lg"
                            onPress={() => {
                                setOrderFilter('all');
                                setRefreshing(true);
                                fetchPartnerData();
                            }}
                        >
                            <Text className="text-madder">View All Orders</Text>
                        </TouchableOpacity>
                    )}
                </View>
            )}
        </View>
    );



    return (
        <View className="flex-1 bg-snow">
            <View className="bg-madder p-4 pt-12 pb-6 rounded-b-3xl">
                <View className="flex-row items-center">
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        className="mr-4 bg-white/20 p-2 rounded-full"
                    >
                        <MaterialIcons name="arrow-back" size={22} color="white" />
                    </TouchableOpacity>
                    <Text className="text-xl text-white font-bold">Partner Details</Text>
                </View>
            </View>

            {/* Tab Navigation */}
            <View className="mx-4 mt-6">
                <View className="bg-white shadow-sm rounded-xl overflow-hidden">
                    <View className="flex-row">
                        <TouchableOpacity
                            className={`flex-1 py-4 px-2 ${activeTab === 'profile' ? 'bg-madder' : 'bg-white'}`}
                            onPress={() => setActiveTab('profile')}
                        >
                            <View className="flex-row justify-center items-center">
                                <MaterialIcons
                                    name="person"
                                    size={20}
                                    color={activeTab === 'profile' ? 'white' : '#666'}
                                />
                                <Text className={`ml-1 font-medium ${activeTab === 'profile' ? 'text-white' : 'text-gray-600'}`}>
                                    Profile
                                </Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className={`flex-1 py-4 px-2 ${activeTab === 'orders' ? 'bg-madder' : 'bg-white'}`}
                            onPress={() => setActiveTab('orders')}
                        >
                            <View className="flex-row justify-center items-center">
                                <MaterialIcons
                                    name="receipt"
                                    size={20}
                                    color={activeTab === 'orders' ? 'white' : '#666'}
                                />
                                <Text className={`ml-1 font-medium ${activeTab === 'orders' ? 'text-white' : 'text-gray-600'}`}>
                                    Orders
                                </Text>
                            </View>
                        </TouchableOpacity>


                    </View>
                </View>
            </View>

            <ScrollView
                ref={scrollViewRef}
                className="flex-1 px-4 pt-6"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 40 }}
                {...panResponder.panHandlers}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={["#A31621"]}
                        tintColor="#A31621"
                    />
                }
            >
                {activeTab === 'profile' && renderProfileTab()}
                {activeTab === 'orders' && renderOrdersTab()}
            </ScrollView>
        </View>
    );
};

export default DeliveryPartnerDetailScreen;
