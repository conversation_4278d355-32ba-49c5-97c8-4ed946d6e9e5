/**
 * Delivery Zone Configuration
 *
 * This file defines the delivery zones for the application.
 * It includes valid pincodes and geographical boundaries.
 */

// List of valid pincodes for delivery - Vellore District Only
export const validPincodes = [
    // Vellore District - Specific Areas Only
    '632515', // Tiruvalam, Ponnai Koot Road (Tiruvalam)
    '632519', // Ammundi, Karnambut, Sugarmill, EB Kut Road Tiruvalam
    '632106', // Sevur, Arumbaruthi
    '632014', // VIT, Brammapuram
    '632001', // Vellore Main
    '632006'  // Old Katpadi, Chittoor Bus Stand (Katpadi)
];

// Area details for each pincode
export const pincodeAreas = {
    '632515': {
        areas: ['Tiruvalam', 'Ponnai Koot Road'],
        district: 'Vellore',
        mainArea: 'Tiruvalam'
    },
    '632519': {
        areas: ['Ammundi', 'Karnambut', 'Sugarmill', 'EB Kut Road Tiruvalam'],
        district: 'Vellore',
        mainArea: 'Tiruvalam'
    },
    '632106': {
        areas: ['Sevur', 'Arumbaruthi'],
        district: 'Vellore',
        mainArea: 'Sevur'
    },
    '632014': {
        areas: ['VIT', 'Brammapuram'],
        district: 'Vellore',
        mainArea: 'VIT Campus'
    },
    '632001': {
        areas: ['Vellore Main', 'Town Area'],
        district: 'Vellore',
        mainArea: 'Vellore Main'
    },
    '632006': {
        areas: ['Old Katpadi', 'Chittoor Bus Stand'],
        district: 'Vellore',
        mainArea: 'Katpadi'
    }
};

// Central points of delivery operations - Vellore Only
export const centralPoints = [
    {
        name: 'Vellore Main',
        latitude: 12.9165,
        longitude: 79.1325
    },
    {
        name: 'VIT Campus',
        latitude: 12.9698,
        longitude: 79.1556
    },
    {
        name: 'Katpadi',
        latitude: 12.9698,
        longitude: 79.1003
    }
];

// Use Vellore Main as the default central point
export const centralPoint = centralPoints[0];

// Maximum delivery radius in kilometers
export const maxDeliveryRadius = 15;

// Define delivery zone polygons (geographical boundaries)
// Vellore District specific areas
export const deliveryZonePolygons = [
    // Vellore Main Area
    [
        { latitude: 12.9365, longitude: 79.1125 }, // Northwest
        { latitude: 12.9365, longitude: 79.1525 }, // Northeast
        { latitude: 12.8965, longitude: 79.1525 }, // Southeast
        { latitude: 12.8965, longitude: 79.1125 }  // Southwest
    ],
    // VIT Campus & Katpadi Area
    [
        { latitude: 12.9798, longitude: 79.0903 }, // Northwest
        { latitude: 12.9798, longitude: 79.1656 }, // Northeast
        { latitude: 12.9598, longitude: 79.1656 }, // Southeast
        { latitude: 12.9598, longitude: 79.0903 }  // Southwest
    ],
    // Tiruvalam & Surrounding Areas
    [
        { latitude: 12.9200, longitude: 79.0800 }, // Northwest
        { latitude: 12.9200, longitude: 79.1200 }, // Northeast
        { latitude: 12.8800, longitude: 79.1200 }, // Southeast
        { latitude: 12.8800, longitude: 79.0800 }  // Southwest
    ]
];

// Use Vellore Main polygon as the default
export const deliveryZonePolygon = deliveryZonePolygons[0];

// Time slots configuration
export const deliveryTimeSlots = {
    morning: {
        start: 9, // 9 AM
        end: 12   // 12 PM
    },
    evening: {
        start: 17, // 5 PM
        end: 19    // 7 PM
    }
};
