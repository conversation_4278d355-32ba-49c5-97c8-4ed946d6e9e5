import React, { useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

const AddToCartModal = ({
    visible,
    onClose,
    onProceedToCart,
    productName,
    productWeight,
    isUpdate = false,
    isProductDetailScreen = false
}) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(30)).current;
    const scaleAnim = useRef(new Animated.Value(0.8)).current;

    useEffect(() => {
        if (visible) {
            // Animate in with bounce effect
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 250,
                    useNativeDriver: true,
                }),
                Animated.spring(slideAnim, {
                    toValue: 0,
                    tension: 100,
                    friction: 8,
                    useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                    toValue: 1,
                    tension: 100,
                    friction: 8,
                    useNativeDriver: true,
                })
            ]).start();
        } else {
            // Animate out quickly
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 150,
                    useNativeDriver: true,
                }),
                Animated.timing(slideAnim, {
                    toValue: 30,
                    duration: 150,
                    useNativeDriver: true,
                }),
                Animated.timing(scaleAnim, {
                    toValue: 0.8,
                    duration: 150,
                    useNativeDriver: true,
                })
            ]).start();
        }
    }, [visible]);

    const handleProceedToCart = () => {
        onClose();
        setTimeout(() => {
            onProceedToCart();
        }, 100);
    };

    if (!visible) return null;

    return (
        <View
            style={{
                position: 'absolute',
                bottom: isProductDetailScreen ? 110 : 10, // Higher position for ProductDetailScreen due to bottom action bar
                left: 16,
                right: 16,
                zIndex: 1000,
                pointerEvents: 'box-none' // Allow touches to pass through to background
            }}
        >
            <TouchableOpacity
                activeOpacity={0.9}
                onPress={handleProceedToCart}
                style={{
                    opacity: fadeAnim,
                    transform: [
                        { translateY: slideAnim },
                        { scale: scaleAnim }
                    ],
                    pointerEvents: 'auto' // Only the modal card captures touches
                }}
            >
                <Animated.View
                    style={{
                        backgroundColor: '#000',
                        borderRadius: 8,
                        paddingVertical: 12,
                        paddingHorizontal: 16,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.2,
                        shadowRadius: 6,
                        elevation: 4
                    }}
                >
                    {/* Single Line Layout */}
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between'
                    }}>
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            flex: 1
                        }}>
                            <View style={{
                                width: 24,
                                height: 24,
                                borderRadius: 12,
                                backgroundColor: 'white',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginRight: 8
                            }}>
                                <MaterialIcons
                                    name="check"
                                    size={14}
                                    color="#000"
                                />
                            </View>

                            <Text style={{
                                fontSize: 14,
                                fontWeight: '600',
                                color: 'white'
                            }}>
                                {isUpdate ? 'Cart Updated' : 'Added to Cart'}
                            </Text>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                            <Text style={{
                                fontSize: 14,
                                fontWeight: '600',
                                color: 'white',
                                marginRight: 4
                            }}>
                                Checkout
                            </Text>
                            <MaterialIcons
                                name="arrow-forward"
                                size={16}
                                color="white"
                            />
                        </View>
                    </View>
                </Animated.View>
            </TouchableOpacity>
        </View>
    );
};

export default AddToCartModal;
