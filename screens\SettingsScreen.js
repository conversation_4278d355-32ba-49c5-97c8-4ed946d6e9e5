import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StatusBar, <PERSON>ing, Al<PERSON>, Modal, Animated } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";

const SettingsScreen = ({ navigation }) => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  const handleAccountDeletion = () => {
    setShowDeleteModal(true);

    // Animate the modal appearance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start();
  };

  const confirmDeletion = () => {
    const formUrl = 'https://docs.google.com/forms/d/e/1FAIpQLScpLg_e4J_9hq4F3k_SiqA6ATwSHpHymhLX-udRQwSRKBNgvg/viewform?usp=dialog';

    // Close modal first
    cancelDeletion();

    // Then open the form
    setTimeout(() => {
      Linking.openURL(formUrl).catch(err => {
        Alert.alert("Error", "Unable to open the form. Please try again later.");
      });
    }, 300);
  };

  const cancelDeletion = () => {
    // Animate the modal disappearance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 200,
        useNativeDriver: true,
      })
    ]).start(() => {
      setShowDeleteModal(false);
    });
  };

  return (
    <View className="flex-1 bg-gray-100">
      <StatusBar backgroundColor="#A31621" barStyle="light-content" />

      {/* Header */}
      <View className="bg-madder h-24 rounded-b-3xl p-4 pt-8 flex-row items-center">
        <TouchableOpacity
          className="p-2 rounded-full"
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text className="text-white text-xl font-bold ml-4">Settings</Text>
      </View>

      <ScrollView className="p-4 mt-2" showsVerticalScrollIndicator={false}>
        {/* Account Settings */}
        <View className="bg-white rounded-xl shadow-sm mb-4 overflow-hidden">
          <Text className="p-4 text-lg font-bold text-gray-800">Account Settings</Text>

          <TouchableOpacity
            className="flex-row items-center justify-between p-4 border-t border-gray-100 active:bg-gray-50"
            activeOpacity={0.7}
            onPress={() => navigation.navigate('EditProfileScreen', {
              userInfo: {
                name: 'New User',
                phone: '9xxxxxxxx'
              },
              onUpdate: (updatedInfo) => {
                // This will be handled in the EditProfileScreen
                console.log('Profile updated:', updatedInfo);
              }
            })}
          >
            <View className="flex-row items-center">
              <View className="w-8 h-8 rounded-full bg-madder/10 items-center justify-center">
                <MaterialIcons name="person" size={18} color="#A31621" />
              </View>
              <Text className="ml-3 text-gray-700 font-medium">Edit Profile</Text>
            </View>
            <MaterialIcons name="chevron-right" size={20} color="#9CA3AF" />
          </TouchableOpacity>
        </View>

        {/* General Settings */}
        <View className="bg-white rounded-xl shadow-sm mb-4 overflow-hidden">
          <Text className="p-4 text-lg font-bold text-gray-800">General</Text>

          <TouchableOpacity
            className="flex-row items-center justify-between p-4 border-t border-gray-100"
            activeOpacity={0.7}
            onPress={() => navigation.navigate('HelpSupportScreen')}
          >
            <View className="flex-row items-center">
              <View className="w-8 h-8 rounded-full bg-madder/10 items-center justify-center">
                <MaterialIcons name="help-outline" size={18} color="#A31621" />
              </View>
              <Text className="ml-3 text-gray-700 font-medium">Help & Support</Text>
            </View>
            <MaterialIcons name="chevron-right" size={20} color="#9CA3AF" />
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center justify-between p-4 border-t border-gray-100"
            activeOpacity={0.7}
            onPress={() => navigation.navigate('ContactUsScreen')}
          >
            <View className="flex-row items-center">
              <View className="w-8 h-8 rounded-full bg-madder/10 items-center justify-center">
                <MaterialIcons name="headset-mic" size={18} color="#A31621" />
              </View>
              <Text className="ml-3 text-gray-700 font-medium">Contact Us</Text>
            </View>
            <MaterialIcons name="chevron-right" size={20} color="#9CA3AF" />
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center justify-between p-4 border-t border-gray-100"
            activeOpacity={0.7}
            onPress={handleAccountDeletion}
          >
            <View className="flex-row items-center">
              <View className="w-8 h-8 rounded-full bg-madder/10 items-center justify-center">
                <MaterialIcons name="delete-forever" size={18} color="#A31621" />
              </View>
              <Text className="ml-3 text-gray-700 font-medium">Delete Account</Text>
            </View>
            <MaterialIcons name="chevron-right" size={20} color="#9CA3AF" />
          </TouchableOpacity>
        </View>

        {/* About */}
        <View className="bg-white rounded-xl shadow-sm mb-4 overflow-hidden">
          <Text className="p-4 text-lg font-bold text-gray-800">About</Text>

          <TouchableOpacity
            className="flex-row items-center justify-between p-4 border-t border-gray-100"
            activeOpacity={0.7}
          >
            <View className="flex-row items-center">
              <View className="w-8 h-8 rounded-full bg-madder/10 items-center justify-center">
                <MaterialIcons name="info" size={18} color="#A31621" />
              </View>
              <Text className="ml-3 text-gray-700 font-medium">App Version</Text>
            </View>
            <Text className="text-gray-500">1.0.0</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center justify-between p-4 border-t border-gray-100"
            activeOpacity={0.7}
            onPress={() => navigation.navigate('PolicyScreen', { type: 'privacy' })}
          >
            <View className="flex-row items-center">
              <View className="w-8 h-8 rounded-full bg-madder/10 items-center justify-center">
                <MaterialIcons name="policy" size={18} color="#A31621" />
              </View>
              <Text className="ml-3 text-gray-700 font-medium">Privacy Policy</Text>
            </View>
            <MaterialIcons name="chevron-right" size={20} color="#9CA3AF" />
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center justify-between p-4 border-t border-gray-100"
            activeOpacity={0.7}
            onPress={() => navigation.navigate('PolicyScreen', { type: 'terms' })}
          >
            <View className="flex-row items-center">
              <View className="w-8 h-8 rounded-full bg-madder/10 items-center justify-center">
                <MaterialIcons name="description" size={18} color="#A31621" />
              </View>
              <Text className="ml-3 text-gray-700 font-medium">Terms of Service</Text>
            </View>
            <MaterialIcons name="chevron-right" size={20} color="#9CA3AF" />
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          className="bg-white rounded-xl p-4 mb-8 flex-row justify-center items-center"
          activeOpacity={0.7}
          onPress={() => {
            navigation.goBack();
            // We need to access the logout function from ProfileScreen
            // This is a workaround - ideally you'd use a context or state management
            setTimeout(() => {
              // This will trigger the logout in ProfileScreen
              navigation.navigate('PreLoginScreen');
            }, 500);
          }}
        >
          <MaterialIcons name="logout" size={20} color="#EF4444" />
          <Text className="ml-2 text-red-500 font-medium">Logout</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Delete Account Modal */}
      <Modal
        visible={showDeleteModal}
        transparent={true}
        animationType="none"
        onRequestClose={cancelDeletion}
      >
        <Animated.View
          className="flex-1 justify-center items-center bg-black/30"
          style={{ opacity: fadeAnim }}
        >
          <Animated.View
            className="bg-white rounded-2xl p-6 m-4 items-center"
            style={{
              transform: [{ scale: scaleAnim }],
              width: '80%',
              maxWidth: 320
            }}
          >
            <View className="w-16 h-16 bg-madder/10 rounded-full items-center justify-center mb-4">
              <MaterialIcons name="delete-forever" size={32} color="#A31621" />
            </View>
            <Text className="text-xl font-bold text-gray-800 mb-2">Delete Account</Text>
            <Text className="text-gray-600 text-center mb-6">
              You will be redirected to a form to request account deletion. We will process your request within 5-7 business days.
            </Text>

            <View className="flex-row w-full">
              <TouchableOpacity
                className="flex-1 bg-gray-200 py-3 rounded-xl mr-2 items-center"
                onPress={cancelDeletion}
              >
                <Text className="font-medium text-gray-700">Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className="flex-1 bg-madder py-3 rounded-xl ml-2 items-center"
                onPress={confirmDeletion}
              >
                <Text className="font-medium text-white">Continue</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </Animated.View>
      </Modal>
    </View>
  );
};

export default SettingsScreen;