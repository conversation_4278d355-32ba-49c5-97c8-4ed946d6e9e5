import React, { useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Modal, Animated, StyleSheet } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, SHADOWS } from '../../styles/deliveryTheme';

/**
 * A custom alert modal component that matches the design of the logout modal
 *
 * @param {boolean} visible - Whether the modal is visible
 * @param {string} title - The title of the alert
 * @param {string} message - The message to display
 * @param {string} icon - The MaterialIcons icon name to display
 * @param {string} iconColor - The color of the icon (defaults to primary color)
 * @param {Array} buttons - Array of button objects with text, style, and onPress properties
 * @param {function} onClose - Function to call when the modal is closed
 */
const CustomAlertModal = ({
    visible,
    title,
    message,
    icon = "info",
    iconColor = COLORS.primary,
    buttons = [{ text: "OK", style: "default" }],
    onClose
}) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.8)).current;

    useEffect(() => {
        if (visible) {
            // Reset animations when modal is shown
            fadeAnim.setValue(0);
            scaleAnim.setValue(0.8);

            // Animate in with improved timing
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 250,
                    useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                    toValue: 1,
                    friction: 7,  // Lower friction for smoother spring
                    tension: 50,  // Higher tension for faster animation
                    useNativeDriver: true,
                })
            ]).start();
        }
    }, [visible, fadeAnim, scaleAnim]);

    const handleClose = () => {
        // Animate out with improved timing
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 180,
                useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
                toValue: 0.9,
                friction: 10,
                tension: 40,
                useNativeDriver: true,
            })
        ]).start(() => {
            if (onClose) onClose();
        });
    };

    // Handle button press
    const handleButtonPress = (button) => {
        // Call the button's onPress function if it exists
        if (button.onPress) button.onPress();

        // Close the modal
        handleClose();
    };

    // Determine button styles based on the number of buttons
    const getButtonContainerStyle = () => {
        if (buttons.length === 1) {
            return styles.singleButtonContainer;
        } else if (buttons.length === 2) {
            return styles.doubleButtonContainer;
        } else {
            return styles.multiButtonContainer;
        }
    };

    // Sort buttons to ensure cancel is on the left and action is on the right
    const sortedButtons = [...buttons].sort((a, b) => {
        // If one is cancel and one is not, cancel should come first (left side)
        if (a.style === 'cancel' && b.style !== 'cancel') return -1;
        if (a.style !== 'cancel' && b.style === 'cancel') return 1;
        return 0;
    });

    // Get button style based on button type
    const getButtonStyle = (buttonStyle) => {
        switch (buttonStyle) {
            case 'cancel':
                return styles.cancelButton;
            case 'destructive':
                return styles.destructiveButton;
            case 'default':
            default:
                return styles.defaultButton;
        }
    };

    // Get text style based on button type
    const getTextStyle = (buttonStyle) => {
        switch (buttonStyle) {
            case 'cancel':
                return styles.cancelButtonText;
            case 'destructive':
                return styles.destructiveButtonText;
            case 'default':
            default:
                return styles.defaultButtonText;
        }
    };

    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="none"
            onRequestClose={handleClose}
        >
            <Animated.View
                style={[styles.overlay, { opacity: fadeAnim }]}
            >
                <Animated.View
                    style={[
                        styles.modalContainer,
                        { transform: [{ scale: scaleAnim }] }
                    ]}
                >
                    <View style={styles.iconContainer}>
                        <MaterialIcons name={icon} size={32} color={iconColor} />
                    </View>
                    <Text style={styles.title}>{title}</Text>
                    <Text style={styles.message}>{message}</Text>

                    <View style={getButtonContainerStyle()}>
                        {sortedButtons.map((button, index) => (
                            <TouchableOpacity
                                key={index}
                                style={[
                                    styles.button,
                                    getButtonStyle(button.style),
                                    sortedButtons.length === 2 && index === 0 && styles.rightMargin,
                                    sortedButtons.length === 2 && index === 1 && styles.leftMargin,
                                ]}
                                onPress={() => handleButtonPress(button)}
                            >
                                <Text style={getTextStyle(button.style)}>{button.text}</Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </Animated.View>
            </Animated.View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 20,
        padding: 24,
        alignItems: 'center',
        width: '85%',
        maxWidth: 340,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.3,
        shadowRadius: 4.65,
        elevation: 8,
    },
    iconContainer: {
        width: 64,
        height: 64,
        borderRadius: 32,
        backgroundColor: `${COLORS.primary}10`,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 16,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#1F2937',
        marginBottom: 8,
        textAlign: 'center',
    },
    message: {
        fontSize: 16,
        color: '#6B7280',
        textAlign: 'center',
        marginBottom: 24,
    },
    singleButtonContainer: {
        width: '100%',
    },
    doubleButtonContainer: {
        flexDirection: 'row',
        width: '100%',
    },
    multiButtonContainer: {
        width: '100%',
    },
    button: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 12,
        justifyContent: 'center',
        alignItems: 'center',
    },
    defaultButton: {
        backgroundColor: COLORS.primary,
    },
    cancelButton: {
        backgroundColor: '#E5E7EB',
    },
    destructiveButton: {
        backgroundColor: '#EF4444',
    },
    defaultButtonText: {
        color: 'white',
        fontWeight: '600',
        fontSize: 16,
    },
    cancelButtonText: {
        color: '#4B5563',
        fontWeight: '600',
        fontSize: 16,
    },
    destructiveButtonText: {
        color: 'white',
        fontWeight: '600',
        fontSize: 16,
    },
    rightMargin: {
        marginRight: 8,
    },
    leftMargin: {
        marginLeft: 8,
    },
});

export default CustomAlertModal;
