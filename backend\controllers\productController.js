const Product = require('../models/Product');
const Category = require('../models/Category');

/**
 * Get all products
 * @route GET /api/products
 * @access Public
 */
const getAllProducts = async (req, res) => {
    try {
        const products = await Product.find().populate('category');
        console.log('Products found:', products.length);
        res.status(200).json({ products: products });
    } catch (error) {
        console.error('Error fetching products:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get product by ID
 * @route GET /api/products/:id
 * @access Public
 */
const getProductById = async (req, res) => {
    try {
        const product = await Product.findById(req.params.id).populate('category');

        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }

        console.log('Product found:', product._id);
        res.status(200).json({ product: product });
    } catch (error) {
        console.error('Error fetching product:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get products by category
 * @route GET /api/products/category/:categoryId
 * @access Public
 */
const getProductsByCategory = async (req, res) => {
    try {
        const products = await Product.find({ category: req.params.categoryId }).populate('category');
        console.log('Products found by category:', products.length);
        res.status(200).json({ products: products });
    } catch (error) {
        console.error('Error fetching products by category:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Create new product
 * @route POST /api/products
 * @access Private (Admin only)
 */
const createProduct = async (req, res) => {
    try {
        const {
            name,
            description,
            price,
            discount_price,
            weight,
            pieces,
            available,
            offer,
            reward_points,
            category,
            image
        } = req.body;

        // Check if category exists
        const categoryExists = await Category.findById(category);
        if (!categoryExists) {
            return res.status(400).json({ message: 'Category not found' });
        }

        // Generate a unique ID
        const productCount = await Product.countDocuments();
        const id = `PROD${(productCount + 1).toString().padStart(3, '0')}`;

        // Create new product
        const newProduct = new Product({
            id,
            name,
            description,
            price,
            discount_price,
            weight,
            pieces,
            available: available !== undefined ? available : true,
            offer,
            reward_points: reward_points || 0,
            category,
            image
        });

        const savedProduct = await newProduct.save();

        // Add product to category
        await Category.findByIdAndUpdate(category, {
            $push: { products: savedProduct._id }
        });

        res.status(201).json(savedProduct);
    } catch (error) {
        console.error('Error creating product:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update product
 * @route PUT /api/products/:id
 * @access Private (Admin only)
 */
const updateProduct = async (req, res) => {
    try {
        const {
            name,
            description,
            price,
            discount_price,
            weight,
            pieces,
            available,
            offer,
            reward_points,
            category,
            image
        } = req.body;

        // Check if product exists
        const product = await Product.findById(req.params.id);
        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }

        // Check if category changed
        if (category && category !== product.category.toString()) {
            // Remove product from old category
            await Category.findByIdAndUpdate(product.category, {
                $pull: { products: product._id }
            });

            // Add product to new category
            await Category.findByIdAndUpdate(category, {
                $push: { products: product._id }
            });
        }

        // Update product
        const updatedProduct = await Product.findByIdAndUpdate(
            req.params.id,
            {
                name: name || product.name,
                description: description || product.description,
                price: price || product.price,
                discount_price: discount_price !== undefined ? discount_price : product.discount_price,
                weight: weight || product.weight,
                pieces: pieces || product.pieces,
                available: available !== undefined ? available : product.available,
                offer: offer !== undefined ? offer : product.offer,
                reward_points: reward_points !== undefined ? reward_points : product.reward_points,
                category: category || product.category,
                image: image || product.image
            },
            { new: true }
        ).populate('category');

        res.status(200).json(updatedProduct);
    } catch (error) {
        console.error('Error updating product:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Delete product
 * @route DELETE /api/products/:id
 * @access Private (Admin only)
 */
const deleteProduct = async (req, res) => {
    try {
        // Check if product exists
        const product = await Product.findById(req.params.id);
        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }

        // Remove product from category
        await Category.findByIdAndUpdate(product.category, {
            $pull: { products: product._id }
        });

        // Delete product
        await Product.findByIdAndDelete(req.params.id);

        res.status(200).json({ message: 'Product deleted successfully' });
    } catch (error) {
        console.error('Error deleting product:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get bestselling products
 * @route GET /api/products/bestselling
 * @access Public
 */
const getBestsellingProducts = async (req, res) => {
    try {
        // For now, just return some products
        // In a real app, you would track sales and return the most sold products
        const products = await Product.find().limit(4).populate('category');
        console.log('Bestselling products found:', products.length);
        res.status(200).json({ products: products });
    } catch (error) {
        console.error('Error fetching bestselling products:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

module.exports = {
    getAllProducts,
    getProductById,
    getProductsByCategory,
    createProduct,
    updateProduct,
    deleteProduct,
    getBestsellingProducts
};