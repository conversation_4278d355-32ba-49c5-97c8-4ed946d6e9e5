const express = require('express');
const router = express.Router();
const {
    savePushToken,
    sendOTP,
    verifyOTPAndLogin,
    updateUserProfile,
    addDeliveryPartner,
    checkUserExists,
    refreshToken,
    logout
} = require('../controllers/authController');
const { protect, adminOnly } = require('../middleware/authMiddleware');

// Public routes
router.post('/save-push-token', savePushToken);
router.post('/send-otp', sendOTP);
router.post('/verify-otp', verifyOTPAndLogin);
router.post('/check-user', checkUserExists);
router.post('/refresh-token', refreshToken);

// Protected routes
router.post('/update-profile', protect, updateUserProfile);
router.post('/logout', protect, logout);

// Admin only routes
router.post('/add-delivery-partner', protect, adminOnly, addDeliveryPartner);

module.exports = router;