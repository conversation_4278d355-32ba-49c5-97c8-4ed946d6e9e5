import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useSocket } from '../context/SocketContext';

/**
 * Component to display Socket.IO connection status
 * Can be used for debugging or showing connection status to users
 */
const SocketStatus = ({ showReconnectButton = true, style = {} }) => {
    const { connected, connectionError, reconnect, ping } = useSocket();

    const handleReconnect = () => {
        console.log('Manual reconnect triggered');
        reconnect();
    };

    const handlePing = () => {
        ping((response) => {
            console.log('Ping response:', response);
        });
    };

    if (!connected && !connectionError) {
        return null; // Don't show anything if not connected and no error
    }

    return (
        <View style={[{
            padding: 8,
            margin: 4,
            borderRadius: 4,
            backgroundColor: connected ? '#d4edda' : '#f8d7da',
            borderColor: connected ? '#c3e6cb' : '#f5c6cb',
            borderWidth: 1,
        }, style]}>
            <Text style={{
                fontSize: 12,
                color: connected ? '#155724' : '#721c24',
                fontWeight: 'bold'
            }}>
                Socket: {connected ? 'Connected' : 'Disconnected'}
            </Text>
            
            {connectionError && (
                <Text style={{
                    fontSize: 10,
                    color: '#721c24',
                    marginTop: 2
                }}>
                    Error: {connectionError}
                </Text>
            )}

            {showReconnectButton && !connected && (
                <View style={{ flexDirection: 'row', marginTop: 4 }}>
                    <TouchableOpacity
                        onPress={handleReconnect}
                        style={{
                            backgroundColor: '#007bff',
                            paddingHorizontal: 8,
                            paddingVertical: 4,
                            borderRadius: 4,
                            marginRight: 4
                        }}
                    >
                        <Text style={{ color: 'white', fontSize: 10 }}>
                            Reconnect
                        </Text>
                    </TouchableOpacity>
                </View>
            )}

            {connected && __DEV__ && (
                <TouchableOpacity
                    onPress={handlePing}
                    style={{
                        backgroundColor: '#28a745',
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                        borderRadius: 4,
                        marginTop: 4
                    }}
                >
                    <Text style={{ color: 'white', fontSize: 10 }}>
                        Ping
                    </Text>
                </TouchableOpacity>
            )}
        </View>
    );
};

export default SocketStatus;
