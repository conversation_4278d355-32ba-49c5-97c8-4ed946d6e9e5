// Force production mode - no preview/development mode
const IS_PREVIEW = false;

module.exports = {
  expo: {
    name: "Meat Now",
    slug: "meat-now",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/logo1.png",
    platforms: ["ios", "android", "web"],
    userInterfaceStyle: "light",
    newArchEnabled: true,
    splash: {
      image: "./assets/logo1.png",
      resizeMode: "contain",
      backgroundColor: "#A31621"
    },
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.meathub.meatshop",
      config: {
        googleMapsApiKey: "YOUR_IOS_API_KEY"
      },
      infoPlist: {
        NSLocationWhenInUseUsageDescription: "Allow Meat Now to access your location only when selecting delivery address on the map.",
        NSLocationAlwaysAndWhenInUseUsageDescription: "Allow Meat Now to access your location only when selecting delivery address on the map.",
        UIBackgroundModes: ["remote-notification"]
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/logo1.png",
        backgroundColor: "#A31621"
      },
      package: "com.meathub.meatshop",
      googleServicesFile: "./google-services.json",
      config: {
        googleMaps: {
          apiKey: "AIzaSyC4IwgWpiexsb328mgLejrbnPCyhrwVGbs"
        }
      },
      permissions: [
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.ACCESS_FINE_LOCATION",
        "android.permission.POST_NOTIFICATIONS",
        "android.permission.VIBRATE",
        "android.permission.RECEIVE_BOOT_COMPLETED",
        "android.permission.WAKE_LOCK"
      ],
      allowBackup: false
    },
    web: {
      favicon: "./assets/logo1.png"
    },
    assetBundlePatterns: [
      "**/*",
      "assets/fonts/*",
      "node_modules/@expo/vector-icons/fonts/*"
    ],
    plugins: [
      "expo-font",
      [
        "expo-image-picker",
        {
          photosPermission: "The app needs access to your photos to upload product images (Admin only).",
          cameraPermission: "The app needs access to your camera to take product photos (Admin only)."
        }
      ],
      [
        "expo-location",
        {
          locationAlwaysAndWhenInUsePermission: "Allow Meat Now to access your location only when selecting delivery address on the map.",
          locationWhenInUsePermission: "Allow Meat Now to access your location only when selecting delivery address on the map.",
          isIosBackgroundLocationEnabled: false,
          isAndroidBackgroundLocationEnabled: false
        }
      ],
      [
        "expo-notifications",
        {
          icon: "./assets/logo.png",
          color: "#A31621",
          defaultChannel: "default",
          mode: IS_PREVIEW ? "development" : "production",
          sounds: [],
          enableBackgroundRefresh: true
        }
      ],
      "./plugins/withNotificationPermissions"
    ],
    notification: {
      icon: "./assets/logo.png",
      color: "#A31621",
      iosDisplayInForeground: true
    },
    extra: {
      eas: {
        projectId: "4044c41b-a6bd-4745-991e-8b5e7b02d38f"
      }
    },
    owner: "dinesh01"
  }
};
