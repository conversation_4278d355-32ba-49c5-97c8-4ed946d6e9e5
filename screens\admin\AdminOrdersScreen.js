import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Modal, TextInput, ActivityIndicator, Alert, RefreshControl, Linking } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { MaterialIcons } from "@expo/vector-icons";
import { useAdmin } from '../../context/AdminContext';
import { getAllOrders } from '../../utils/api/orderApi';

// Helper function to map API status to UI status
const mapStatusToUI = (status) => {
    if (!status) return 'pending';

    const statusMap = {
        'PENDING': 'pending',
        'PROCESSING': 'processing',
        'PREPARING': 'preparing',
        'OUT_FOR_DELIVERY': 'out_for_delivery',
        'DELIVERED': 'delivered',
        'CANCELLED': 'cancelled'
    };

    return statusMap[status] || status.toLowerCase();
};

// Helper function to get display name for status
const getStatusDisplayName = (status) => {
    const displayNames = {
        'pending': 'Pending',
        'processing': 'Processing',
        'preparing': 'Preparing',
        'out_for_delivery': 'Out for Delivery',
        'delivered': 'Delivered',
        'cancelled': 'Cancelled'
    };

    return displayNames[status] || status.charAt(0).toUpperCase() + status.slice(1);
};

const AdminOrdersScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { orderId } = route.params || {};
    const { orders: adminOrders } = useAdmin();
    const [selectedStatus, setSelectedStatus] = useState('all');
    const [showOrderDetails, setShowOrderDetails] = useState(false);
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [loading, setLoading] = useState(true); // Start with loading state
    const [refreshing, setRefreshing] = useState(false); // For pull-to-refresh
    const [orders, setOrders] = useState([]);

    const [orderStatuses] = useState(['all', 'pending', 'out_for_delivery', 'delivered']);

    // Function to fetch orders from the database
    const fetchOrders = async (isRefreshing = false) => {
        try {
            if (!isRefreshing) {
                setLoading(true);
            }
            console.log('Fetching all orders from database...');

            // Fetch orders from the database
            const response = await getAllOrders();
            console.log('Orders database response:', response);

            if (response && response.orders && response.orders.length > 0) {
                // Format orders for display
                const formattedOrders = response.orders.map(order => {
                    // Calculate total from items
                    const total = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

                    // Format date
                    const orderDate = new Date(order.createdAt);
                    const formattedDate = orderDate.toISOString().split('T')[0];

                    // Get customer info
                    const customerName = order.userId?.name || 'Unknown Customer';
                    const customerPhone = order.userId?.phone || order.userId?.number || 'No Phone';

                    // Format address properly
                    let formattedAddress = 'No Address';
                    if (order.deliveryAddress) {
                        if (typeof order.deliveryAddress === 'object') {
                            formattedAddress = order.deliveryAddress.fullAddress ||
                                `${order.deliveryAddress.doorNo || ''} ${order.deliveryAddress.streetName || ''}, ${order.deliveryAddress.area || ''}, ${order.deliveryAddress.district || ''} - ${order.deliveryAddress.pincode || ''}`.trim();
                        } else {
                            formattedAddress = order.deliveryAddress;
                        }
                    }

                    // Calculate subtotal using discount_price if available, otherwise use price
                    const subtotal = order.items.reduce((sum, item) => {
                        const itemPrice = item.discount_price || item.price;
                        return sum + (itemPrice * item.quantity);
                    }, 0);

                    // Get the final total from the order
                    const finalTotal = order.totalAmount || total;

                    // Check for various types of discounts
                    const couponDiscount = order.couponDiscount || 0;
                    const coinsDiscount = order.coinsDiscount || 0;
                    const totalDiscount = couponDiscount + coinsDiscount;
                    const hasDiscount = totalDiscount > 0 || subtotal > finalTotal;

                    return {
                        id: order._id,
                        orderNumber: order.orderNumber || `ORD${order._id.substring(0, 6)}`,
                        customer: customerName,
                        phone: customerPhone,
                        address: formattedAddress,
                        subtotal: subtotal,
                        total: finalTotal,
                        originalAmount: order.originalAmount || subtotal,
                        couponDiscount: couponDiscount,
                        coinsDiscount: coinsDiscount,
                        totalDiscount: totalDiscount,
                        appliedCoupon: order.appliedCoupon || null,
                        deliveryFee: order.deliveryFee || 0,
                        status: mapStatusToUI(order.status),
                        date: formattedDate,
                        items: order.items || [],
                        userId: order.userId?._id,
                        deliveryPartner: order.deliveryPartner?._id,
                        paymentMethod: order.paymentMethod || 'Not specified',
                        rawOrder: order // Keep the original order data
                    };
                });

                console.log(`Fetched ${formattedOrders.length} orders from database`);
                setOrders(formattedOrders);
            } else if (adminOrders && adminOrders.length > 0) {
                // Fallback to admin context if API fails
                console.log('Using orders from admin context');

                const formattedOrders = adminOrders.map(order => {
                    // Format the order for display
                    // Format address properly
                    let formattedAddress = 'No Address';
                    if (order.deliveryAddress) {
                        if (typeof order.deliveryAddress === 'object') {
                            formattedAddress = order.deliveryAddress.fullAddress ||
                                `${order.deliveryAddress.doorNo || ''} ${order.deliveryAddress.streetName || ''}, ${order.deliveryAddress.area || ''}, ${order.deliveryAddress.district || ''} - ${order.deliveryAddress.pincode || ''}`.trim();
                        } else {
                            formattedAddress = order.deliveryAddress;
                        }
                    }

                    // Calculate subtotal using discount_price if available, otherwise use price
                    const subtotal = order.items ? order.items.reduce((sum, item) => {
                        const itemPrice = item.discount_price || item.price;
                        return sum + (itemPrice * item.quantity);
                    }, 0) : 0;

                    // Get the final total from the order
                    const finalTotal = order.totalAmount || 0;

                    // Check for various types of discounts
                    const couponDiscount = order.couponDiscount || 0;
                    const coinsDiscount = order.coinsDiscount || 0;
                    const totalDiscount = couponDiscount + coinsDiscount;
                    const hasDiscount = totalDiscount > 0 || subtotal > finalTotal;

                    return {
                        id: order._id || order.id,
                        orderNumber: order.orderNumber || `ORD${(order._id || order.id).substring(0, 6)}`,
                        customer: order.userId?.name || 'Unknown Customer',
                        phone: order.userId?.phone || order.userId?.number || 'No Phone',
                        address: formattedAddress,
                        subtotal: subtotal,
                        total: finalTotal,
                        originalAmount: order.originalAmount || subtotal,
                        couponDiscount: couponDiscount,
                        coinsDiscount: coinsDiscount,
                        totalDiscount: totalDiscount,
                        appliedCoupon: order.appliedCoupon || null,
                        deliveryFee: order.deliveryFee || 0,
                        status: mapStatusToUI(order.status),
                        date: new Date(order.createdAt || Date.now()).toISOString().split('T')[0],
                        items: order.items || [],
                        userId: order.userId?._id,
                        deliveryPartner: order.deliveryPartner?._id,
                        paymentMethod: order.paymentMethod || 'Not specified',
                        rawOrder: order
                    };
                });

                setOrders(formattedOrders);
            } else {
                console.log('No orders found');
                setOrders([]);
            }
        } catch (error) {
            console.error('Error fetching orders:', error);
            Alert.alert('Error', 'Failed to fetch orders. Please try again later.');
            setOrders([]);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    // Handle pull-to-refresh
    const onRefresh = () => {
        setRefreshing(true);
        fetchOrders(true);
    };

    // Initial fetch
    useEffect(() => {
        fetchOrders();

        // Cleanup function to prevent memory leaks
        return () => {
            setOrders([]);
            setLoading(false);
            setRefreshing(false);
        };
    }, []);

    // If orderId is provided, show that order's details
    useEffect(() => {
        if (orderId) {
            const order = orders.find(o => o.id === orderId);
            if (order) {
                setSelectedOrder(order);
                setShowOrderDetails(true);
            }
        }
    }, [orderId, orders]);

    // Status update functionality removed as per requirements

    const handleOrderPress = (order) => {
        setSelectedOrder(order);
        setShowOrderDetails(true);
    };



    // Get color classes for status badges
    const getStatusColor = (status) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-700';
            case 'processing': return 'bg-blue-100 text-blue-700';
            case 'preparing': return 'bg-amber-100 text-amber-700';
            case 'out_for_delivery': return 'bg-indigo-100 text-indigo-700';
            case 'delivered': return 'bg-green-100 text-green-700';
            case 'cancelled': return 'bg-red-100 text-red-700';
            default: return 'bg-gray-100 text-gray-700';
        }
    };



    const filteredOrders = React.useMemo(() => {
        if (!orders || orders.length === 0) return [];

        return orders.filter(order => {
            // Add null checks to prevent crashes
            const matchesStatus = selectedStatus === 'all' || order?.status === selectedStatus;

            const matchesSearch = !searchQuery ||
                (order?.orderNumber && order.orderNumber.toString().toLowerCase().includes(searchQuery.toLowerCase())) ||
                (order?.customer && order.customer.toLowerCase().includes(searchQuery.toLowerCase())) ||
                (order?.phone && order.phone.toLowerCase().includes(searchQuery.toLowerCase()));

            return matchesStatus && matchesSearch;
        });
    }, [orders, selectedStatus, searchQuery]);

    return (
        <View className="flex-1 bg-gray-50">
            <View className="bg-madder px-6 pt-10 rounded-b-3xl pb-4">
                <View className="flex-row justify-between items-center mb-3">
                    <View>
                        <Text className="text-xl text-white font-bold">Orders</Text>
                        <Text className="text-white/80 text-xs mt-1">Manage customer orders</Text>
                    </View>
                </View>

                {/* Search Bar in Header */}
                <View className="bg-snow rounded-xl flex-row items-center px-4 mb-2 shadow-sm">
                    <MaterialIcons name="search" size={20} color="#666" />
                    <TextInput
                        placeholder="Search orders..."
                        className="flex-1 p-3"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor="#9CA3AF"
                    />
                    {searchQuery.length > 0 && (
                        <TouchableOpacity
                            onPress={() => setSearchQuery('')}
                            className="p-1 rounded-full"
                        >
                            <MaterialIcons name="close" size={18} color="#666" />
                        </TouchableOpacity>
                    )}
                </View>
            </View>

            {/* Main Content */}
            <View className="p-4">

                {/* Status Filter */}
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    className="mb-3"
                >
                    {orderStatuses.map(status => (
                        <TouchableOpacity
                            key={status}
                            onPress={() => setSelectedStatus(status)}
                            className={`mr-2 px-4 py-2 rounded-full ${selectedStatus === status ? 'bg-madder' : 'bg-white'
                                } shadow-sm`}
                        >
                            <Text className={selectedStatus === status ? 'text-white' : 'text-gray-600'}>
                                {status.charAt(0).toUpperCase() + status.slice(1)}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </ScrollView>

                {/* Orders List */}
                {loading ? (
                    <View className="flex-1 justify-center items-center">
                        <ActivityIndicator size="large" color="#A31621" />
                    </View>
                ) : (
                    <ScrollView
                        contentContainerStyle={{ paddingBottom: 250 }}
                        showsVerticalScrollIndicator={false}
                        refreshControl={
                            <RefreshControl
                                refreshing={refreshing}
                                onRefresh={onRefresh}
                                colors={["#A31621"]}
                                tintColor="#A31621"
                            />
                        }
                    >
                        {filteredOrders.map(order => (
                            <TouchableOpacity
                                key={order.id}
                                onPress={() => handleOrderPress(order)}
                                className="bg-white p-4 rounded-xl mb-4 shadow-sm"
                            >
                                <View className="flex-row justify-between items-start">
                                    <View>
                                        <Text className="font-bold text-lg">Order #{order.orderNumber}</Text>
                                        <Text className="text-gray-600">{order.customer}</Text>
                                        <View className="flex-row items-center justify-between mt-1">
                                            <View className="flex-row items-center">
                                                {order.totalDiscount > 0 ? (
                                                    <>
                                                        <Text className="text-gray-400 text-xs line-through mr-2">₹{order.originalAmount}</Text>
                                                        <Text className="text-madder font-medium">₹{order.total}</Text>
                                                        <View className="bg-green-100 rounded-full px-2 py-0.5 ml-2">
                                                            <Text className="text-green-700 text-xs">
                                                                {order.coinsDiscount > 0 && order.couponDiscount > 0
                                                                    ? 'Coupon + Coins'
                                                                : order.coinsDiscount > 0
                                                                    ? 'Coins Used'
                                                                    : 'Discount Applied'}
                                                            </Text>
                                                        </View>
                                                    </>
                                                ) : (
                                                    <Text className="text-madder font-medium">₹{order.total}</Text>
                                                )}

                                                {/* Payment Method Badge */}
                                                <View className="flex-row items-center ml-2">
                                                    <MaterialIcons
                                                        name={['COD', 'cod'].includes(order.paymentMethod) ? 'payments' : 'account-balance'}
                                                        size={14}
                                                        color={['COD', 'cod'].includes(order.paymentMethod) ? '#F59E0B' : '#10B981'}
                                                    />
                                                    <Text className="text-xs text-gray-600 ml-1">
                                                        {['COD', 'cod'].includes(order.paymentMethod) ? 'COD' :
                                                         ['UPI', 'upi'].includes(order.paymentMethod) ? 'UPI' :
                                                         order.paymentMethod || 'N/A'}
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                        <Text className="text-gray-500 text-sm">{order.date}</Text>
                                    </View>
                                    <View className={`px-3 py-1 rounded-full ${getStatusColor(order.status)}`}>
                                        <Text className="text-sm font-medium">
                                            {getStatusDisplayName(order.status)}
                                        </Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        ))}
                    </ScrollView>
                )}
            </View>

            {/* Order Details Modal */}
            <Modal
                visible={showOrderDetails}
                animationType="slide"
                transparent={true}
            >
                <View className="flex-1 bg-black/50 justify-end">
                    <View className="bg-white rounded-t-3xl p-6 h-3/4">
                        <View className="flex-row justify-between items-center mb-6">
                            <Text className="text-xl font-bold">Order Details</Text>
                            <TouchableOpacity onPress={() => setShowOrderDetails(false)}>
                                <MaterialIcons name="close" size={24} color="#666" />
                            </TouchableOpacity>
                        </View>

                        {selectedOrder && (
                            <ScrollView
                                showsVerticalScrollIndicator={false}
                                contentContainerStyle={{ paddingBottom: 40 }}
                            >
                                <View className="mb-6">
                                    <Text className="text-gray-600">Order Number</Text>
                                    <Text className="text-lg font-bold">#{selectedOrder.orderNumber}</Text>

                                    {/* Order Time Details */}
                                    <View className="mt-3 bg-gray-50 p-3 rounded-lg">
                                        <View className="flex-row items-center mb-1">
                                            <MaterialIcons name="access-time" size={16} color="#666" />
                                            <Text className="text-gray-700 ml-2 font-medium">Order Time Details</Text>
                                        </View>

                                        <View className="flex-row justify-between mt-2">
                                            <Text className="text-gray-600">Order Placed Date:</Text>
                                            <Text className="text-gray-800">{selectedOrder.date}</Text>
                                        </View>

                                        <View className="flex-row justify-between mt-1">
                                            <Text className="text-gray-600">Order Placed Time:</Text>
                                            <Text className="text-gray-800">
                                                {selectedOrder.rawOrder && selectedOrder.rawOrder.createdAt ?
                                                    new Date(selectedOrder.rawOrder.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) :
                                                    'Not available'}
                                            </Text>
                                        </View>

                                        {selectedOrder.status === 'delivered' && selectedOrder.rawOrder && selectedOrder.rawOrder.deliveredAt && (
                                            <View className="flex-row justify-between mt-1">
                                                <Text className="text-gray-600">Delivered At:</Text>
                                                <Text className="text-green-700 font-medium">
                                                    {new Date(selectedOrder.rawOrder.deliveredAt).toLocaleString([], {
                                                        month: 'short',
                                                        day: 'numeric',
                                                        hour: '2-digit',
                                                        minute: '2-digit'
                                                    })}
                                                </Text>
                                            </View>
                                        )}

                                        {selectedOrder.status === 'out_for_delivery' && selectedOrder.rawOrder && selectedOrder.rawOrder.deliveryStartedAt && (
                                            <View className="flex-row justify-between mt-1">
                                                <Text className="text-gray-600">Out for Delivery:</Text>
                                                <Text className="text-indigo-700 font-medium">
                                                    {new Date(selectedOrder.rawOrder.deliveryStartedAt).toLocaleString([], {
                                                        month: 'short',
                                                        day: 'numeric',
                                                        hour: '2-digit',
                                                        minute: '2-digit'
                                                    })}
                                                </Text>
                                            </View>
                                        )}

                                        {selectedOrder.rawOrder && selectedOrder.rawOrder.expectedDelivery && (
                                            <View className="flex-row justify-between mt-1">
                                                <Text className="text-gray-600">Expected Delivery:</Text>
                                                <Text className="text-gray-800">
                                                    {new Date(selectedOrder.rawOrder.expectedDelivery).toLocaleString([], {
                                                        month: 'short',
                                                        day: 'numeric',
                                                        hour: '2-digit',
                                                        minute: '2-digit'
                                                    })}
                                                </Text>
                                            </View>
                                        )}
                                    </View>
                                </View>

                                <View className="mb-6">
                                    <Text className="text-gray-600">Customer Details</Text>
                                    <Text className="font-bold">{selectedOrder.customer}</Text>
                                    <Text>{selectedOrder.phone}</Text>
                                    <Text>{typeof selectedOrder.address === 'object'
                                        ? (selectedOrder.address.fullAddress ||
                                           `${selectedOrder.address.doorNo || ''} ${selectedOrder.address.streetName || ''}, ${selectedOrder.address.area || ''}, ${selectedOrder.address.district || ''} - ${selectedOrder.address.pincode || ''}`.trim())
                                        : selectedOrder.address}
                                    </Text>
                                </View>

                                <View className="mb-6">
                                    <Text className="text-gray-600 mb-2">Items</Text>
                                    {selectedOrder.items.map((item, index) => (
                                        <View key={index} className="flex-row justify-between py-2 border-b border-gray-100">
                                            <View className="flex-1">
                                                <Text>{item.name} x{item.quantity}</Text>
                                                {item.selectedWeight && (
                                                    <Text className="text-gray-500 text-xs">{item.selectedWeight}</Text>
                                                )}
                                            </View>
                                            <View className="items-end">
                                                {item.discount_price && item.discount_price < item.price ? (
                                                    <View>
                                                        <Text className="text-gray-400 text-xs line-through">₹{item.price}</Text>
                                                        <Text className="font-medium text-madder">₹{item.discount_price}</Text>
                                                    </View>
                                                ) : (
                                                    <Text className="font-medium">₹{item.price}</Text>
                                                )}
                                            </View>
                                        </View>
                                    ))}
                                    {/* Original amount (before any discounts) */}
                                    <View className="flex-row justify-between mt-4">
                                        <Text className="text-gray-600">Discount Amount</Text>
                                        <Text className="text-gray-600">₹{selectedOrder.originalAmount}</Text>
                                    </View>

                                    {/* Coupon discount information */}
                                    {selectedOrder.couponDiscount > 0 && (
                                        <View className="flex-row justify-between mt-2">
                                            <View className="flex-row items-center">
                                                <Text className="text-green-600">Coupon Discount</Text>
                                                {selectedOrder.appliedCoupon && (
                                                    <Text className="text-xs text-gray-500 ml-2">({selectedOrder.appliedCoupon})</Text>
                                                )}
                                            </View>
                                            <Text className="text-green-600">-₹{selectedOrder.couponDiscount}</Text>
                                        </View>
                                    )}

                                    {/* Coins discount information */}
                                    {selectedOrder.coinsDiscount > 0 && (
                                        <View className="flex-row justify-between mt-2">
                                            <View className="flex-row items-center">
                                                <Text className="text-green-600">Coins Used</Text>
                                                <Text className="text-xs text-gray-500 ml-2">({selectedOrder.coinsDiscount} coins)</Text>
                                            </View>
                                            <Text className="text-green-600">-₹{selectedOrder.coinsDiscount}</Text>
                                        </View>
                                    )}

                                    {/* Delivery fee if applicable */}
                                    {selectedOrder.deliveryFee > 0 && (
                                        <View className="flex-row justify-between mt-2">
                                            <Text className="text-gray-600">Delivery Fee</Text>
                                            <Text className="text-gray-600">₹{selectedOrder.deliveryFee}</Text>
                                        </View>
                                    )}

                                    {/* Total with border top */}
                                    <View className="flex-row justify-between mt-4 pt-2 border-t border-gray-200">
                                        <Text className="font-bold">Total</Text>
                                        <Text className="font-bold text-madder">₹{selectedOrder.total}</Text>
                                    </View>
                                </View>

                                <View className="mb-6">
                                    <Text className="text-gray-600 mb-2">Payment Information</Text>
                                    <View className="bg-blue-50 p-4 rounded-lg">
                                        <View className="flex-row items-center">
                                            <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-3">
                                                <MaterialIcons
                                                    name={['COD', 'cod'].includes(selectedOrder.paymentMethod) ? 'payments' : 'account-balance'}
                                                    size={20}
                                                    color="#2563EB"
                                                />
                                            </View>
                                            <View className="flex-1">
                                                <Text className="text-xs text-blue-600 font-bold">PAYMENT MODE</Text>
                                                <Text className="text-gray-800 font-medium text-lg">
                                                    {['COD', 'cod'].includes(selectedOrder.paymentMethod) ? 'Cash on Delivery' :
                                                     ['UPI', 'upi'].includes(selectedOrder.paymentMethod) ? 'UPI Payment' :
                                                     selectedOrder.paymentMethod || 'Not specified'}
                                                </Text>
                                                {['COD', 'cod'].includes(selectedOrder.paymentMethod) && (
                                                    <Text className="text-gray-600 text-sm mt-1">
                                                        Customer will pay ₹{selectedOrder.total} in cash
                                                    </Text>
                                                )}
                                                {['UPI', 'upi'].includes(selectedOrder.paymentMethod) && (
                                                    <Text className="text-green-600 text-sm mt-1">
                                                        Payment completed online
                                                    </Text>
                                                )}
                                            </View>
                                            {['COD', 'cod'].includes(selectedOrder.paymentMethod) && (
                                                <View className="bg-orange-100 px-3 py-1 rounded-full">
                                                    <Text className="text-orange-700 text-xs font-medium">COD</Text>
                                                </View>
                                            )}
                                            {['UPI', 'upi'].includes(selectedOrder.paymentMethod) && (
                                                <View className="bg-green-100 px-3 py-1 rounded-full">
                                                    <Text className="text-green-700 text-xs font-medium">PAID</Text>
                                                </View>
                                            )}
                                        </View>
                                    </View>
                                </View>

                                <View className="mb-6">
                                    <Text className="text-gray-600 mb-2">Order Status</Text>
                                    <View className={`px-4 py-2 rounded-lg ${
                                        selectedOrder.status === 'pending' ? 'bg-yellow-100' :
                                        selectedOrder.status === 'out_for_delivery' ? 'bg-indigo-100' :
                                        selectedOrder.status === 'delivered' ? 'bg-green-100' :
                                        selectedOrder.status === 'cancelled' ? 'bg-red-100' : 'bg-gray-100'
                                    }`}>
                                        <Text className={`font-medium ${
                                            selectedOrder.status === 'pending' ? 'text-yellow-700' :
                                            selectedOrder.status === 'out_for_delivery' ? 'text-indigo-700' :
                                            selectedOrder.status === 'delivered' ? 'text-green-700' :
                                            selectedOrder.status === 'cancelled' ? 'text-red-700' : 'text-gray-600'
                                        }`}>
                                            {getStatusDisplayName(selectedOrder.status)}
                                        </Text>
                                    </View>
                                </View>

                                {/* Delivery Partner Details - Only show if a delivery partner is assigned */}
                                {selectedOrder.rawOrder && selectedOrder.rawOrder.deliveryPartner && (
                                    <View className="mb-6">
                                        <Text className="text-gray-600 mb-2">Delivery Partner</Text>
                                        <View className="bg-white p-4 rounded-lg">
                                            <View className="flex-row items-center mb-2">
                                                <MaterialIcons name="person" size={20} color="#4F46E5" />
                                                <Text className="font-bold ml-2">
                                                    {selectedOrder.rawOrder.deliveryPartner.name || "Assigned Partner"}
                                                </Text>
                                            </View>

                                            {selectedOrder.rawOrder.deliveryPartner.phoneNumber && (
                                                <TouchableOpacity
                                                    className="flex-row items-center mb-3 bg-white p-2 rounded-lg"
                                                    onPress={() => Linking.openURL(`tel:${selectedOrder.rawOrder.deliveryPartner.phoneNumber}`)}
                                                >
                                                    <MaterialIcons name="phone" size={18} color="#10B981" />
                                                    <Text className="ml-2 text-gray-700">{selectedOrder.rawOrder.deliveryPartner.phoneNumber}</Text>
                                                </TouchableOpacity>
                                            )}

                                            {/* Delivery Assignment Time */}
                                            {selectedOrder.rawOrder.deliveryPartnerAssignedAt && (
                                                <View className="flex-row items-center mt-1">
                                                    <MaterialIcons name="schedule" size={16} color="#4F46E5" />
                                                    <Text className="ml-2 text-gray-600">
                                                        Assigned: {new Date(selectedOrder.rawOrder.deliveryPartnerAssignedAt).toLocaleString([], {
                                                            month: 'short',
                                                            day: 'numeric',
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })}
                                                    </Text>
                                                </View>
                                            )}

                                            {/* Vehicle Information if available */}
                                            {selectedOrder.rawOrder.deliveryPartner.vehicleType && (
                                                <View className="flex-row items-center mt-2">
                                                    <MaterialIcons name="two-wheeler" size={16} color="#4F46E5" />
                                                    <Text className="ml-2 text-gray-600">
                                                        {selectedOrder.rawOrder.deliveryPartner.vehicleType}
                                                        {selectedOrder.rawOrder.deliveryPartner.vehicleNumber &&
                                                            ` (${selectedOrder.rawOrder.deliveryPartner.vehicleNumber})`}
                                                    </Text>
                                                </View>
                                            )}
                                        </View>
                                    </View>
                                )}

                                <View className="flex-row justify-between">
                                    <TouchableOpacity
                                        className="bg-madder py-3 px-6 rounded-xl flex-1"
                                        onPress={() => setShowOrderDetails(false)}
                                    >
                                        <Text className="text-white text-center font-bold">
                                            Close
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </ScrollView>
                        )}
                    </View>
                </View>
            </Modal>


        </View>
    );
};

export default AdminOrdersScreen;